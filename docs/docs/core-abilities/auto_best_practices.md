# Auto Best Practices 💎

`Supported Git Platforms: GitHub`

## Overview

![Auto best practice suggestion graph](https://www.qodo.ai/images/pr_agent/auto_best_practices_graph.png){width=684}

> Note - enabling a [Wiki](https://qodo-merge-docs.qodo.ai/usage-guide/enabling_a_wiki/) is required for this feature.

### Finding Code Problems - Exploration Phase

The `improve` tool identifies potential issues, problems and bugs in Pull Request (PR) code changes.
Rather than focusing on minor issues like code style or formatting, the tool intelligently analyzes code to detect meaningful problems.

The analysis intentionally takes a flexible, _exploratory_ approach to identify meaningful potential issues, allowing the tool to surface relevant code suggestions without being constrained by predefined categories.

### Tracking Implemented Suggestions

Qodo Merge features a novel [tracking system](https://qodo-merge-docs.qodo.ai/tools/improve/#suggestion-tracking) that automatically detects when PR authors implement AI-generated code suggestions.
All accepted suggestions are aggregated in a repository-specific wiki page called [`.pr_agent_accepted_suggestions`](https://github.com/qodo-ai/pr-agent/wiki/.pr_agent_accepted_suggestions)

### Learning and Applying Auto Best Practices

Monthly, Qodo Merge analyzes the collection of accepted suggestions to generate repository-specific best practices, stored in [`.pr_agent_auto_best_practices`](https://github.com/qodo-ai/pr-agent/wiki/.pr_agent_auto_best_practices) wiki file.
These best practices reflect recurring patterns in accepted code improvements.

The `improve` tool will incorporate these best practices as an additional analysis layer, checking PR code changes against known patterns of previously accepted improvements.
This creates a two-phase analysis:

1. Open exploration for general code issues
2. Targeted checking against established best practices - exploiting the knowledge gained from past suggestions

By keeping these phases decoupled, the tool remains free to discover new or unseen issues and problems, while also learning from past experiences.

When presenting the suggestions generated by the `improve` tool, Qodo Merge will add a dedicated label for each suggestion generated from the auto best practices - 'Learned best practice':

![Auto best practice suggestion](https://www.qodo.ai/images/pr_agent/auto_best_practices.png){width=684}

## Auto Best Practices vs Custom Best Practices

Teams and companies can also manually define their own [custom best practices](https://qodo-merge-docs.qodo.ai/tools/improve/#best-practices) in Qodo Merge.

When custom best practices exist, Qodo Merge will still generate an 'auto best practices' wiki file, though it won't be used by the `improve` tool.
However, this auto-generated file can still serve two valuable purposes:

1. It can help enhance your custom best practices with additional insights derived from suggestions your team found valuable enough to implement
2. It demonstrates effective patterns for writing AI-friendly best practices

Even when using custom best practices, we recommend regularly reviewing the auto best practices file to refine your custom rules.

## Relevant configurations

```toml
[auto_best_practices]
# Disable all auto best practices usage or generation
enable_auto_best_practices = true  

# Disable usage of auto best practices file in the 'improve' tool
utilize_auto_best_practices = true 

# Extra instructions to the auto best practices generation prompt
extra_instructions = ""            

# Max number of patterns to be detected
max_patterns = 5                   
```
