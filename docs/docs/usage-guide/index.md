# Usage guide

This section provides a detailed guide on how to use Qodo Merge.
It includes information on how to adjust Qodo Merge configurations, define which tools will run automatically, and other advanced configurations.

- [Introduction](./introduction.md)
- [Enabling a Wiki](./enabling_a_wiki)
- [Configuration File](./configuration_options.md)
- [Usage and Automation](./automations_and_usage.md)
    - [Local Repo (CLI)](./automations_and_usage.md#local-repo-cli)
    - [Online Usage](./automations_and_usage.md#online-usage)
    - [GitHub App](./automations_and_usage.md#github-app)
    - [GitHub Action](./automations_and_usage.md#github-action)
    - [GitLab Webhook](./automations_and_usage.md#gitlab-webhook)
    - [Gitea Webhook](./automations_and_usage.md#gitea-webhook)
    - [BitBucket App](./automations_and_usage.md#bitbucket-app)
    - [Azure DevOps Provider](./automations_and_usage.md#azure-devops-provider)
- [Managing Mail Notifications](./mail_notifications.md)
- [Changing a Model](./changing_a_model.md)
- [Additional Configurations](./additional_configurations.md)
    - [Ignoring files from analysis](./additional_configurations.md#ignoring-files-from-analysis)
    - [Extra instructions](./additional_configurations.md#extra-instructions)
    - [Working with large PRs](./additional_configurations.md#working-with-large-prs)
    - [Changing a model](https://qodo-merge-docs.qodo.ai/usage-guide/changing_a_model/)
    - [Patch Extra Lines](./additional_configurations.md#patch-extra-lines)
- [FAQ](https://qodo-merge-docs.qodo.ai/faq/)
- [Qodo Merge Models](./qodo_merge_models)
