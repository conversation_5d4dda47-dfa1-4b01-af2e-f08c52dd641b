# PR-Agent Installation Guide

PR-Agent can be deployed in various environments and platforms. Choose the installation method that best suits your needs:

## 🖥️ Local Installation

Learn how to run PR-Agent locally using:

- Docker image
- pip package
- CLI from source code

[View Local Installation Guide →](https://qodo-merge-docs.qodo.ai/installation/locally/)

## 🐙 GitHub Integration

Set up PR-Agent with GitHub as:

- GitHub Action
- Local GitHub App

[View GitHub Integration Guide →](https://qodo-merge-docs.qodo.ai/installation/github/)

## 🦊 GitLab Integration

Deploy PR-Agent on GitLab as:

- GitLab pipeline job
- Local GitLab webhook server

[View GitLab Integration Guide →](https://qodo-merge-docs.qodo.ai/installation/gitlab/)

## 🟦 BitBucket Integration

Implement PR-Agent in BitBucket as:

- BitBucket pipeline job
- Local BitBucket server

[View BitBucket Integration Guide →](https://qodo-merge-docs.qodo.ai/installation/bitbucket/)

## 🔷  Azure DevOps Integration

Configure PR-Agent with Azure DevOps as:

- Azure DevOps pipeline job
- Local Azure DevOps webhook

[View Azure DevOps Integration Guide →](https://qodo-merge-docs.qodo.ai/installation/azure/)
