site_name: <PERSON><PERSON><PERSON> Merge (and open-source PR-Agent)
repo_url: https://github.com/qodo-ai/pr-agent
repo_name: Qodo-ai/pr-agent

nav:
  - Overview:
    - 'index.md'
    - 💎 Qodo Merge: 'overview/pr_agent_pro.md'
    - Data Privacy: 'overview/data_privacy.md'
  - Installation:
    - 'installation/index.md'
    - PR-Agent: 'installation/pr_agent.md'
    - 💎 Qodo Merge: 'installation/qodo_merge.md'
  - Usage Guide:
    - 'usage-guide/index.md'
    - Introduction: 'usage-guide/introduction.md'
    - Enabling a Wiki: 'usage-guide/enabling_a_wiki.md'
    - Configuration File: 'usage-guide/configuration_options.md'
    - Usage and Automation: 'usage-guide/automations_and_usage.md'
    - Managing Mail Notifications: 'usage-guide/mail_notifications.md'
    - Changing a Model: 'usage-guide/changing_a_model.md'
    - Additional Configurations: 'usage-guide/additional_configurations.md'
    - Frequently Asked Questions: 'faq/index.md'
    - 💎 Qodo Merge Models: 'usage-guide/qodo_merge_models.md'
  - Tools:
     - 'tools/index.md'
     - Describe: 'tools/describe.md'
     - Review: 'tools/review.md'
     - Improve: 'tools/improve.md'
     - Ask: 'tools/ask.md'
     - Help: 'tools/help.md'
     - Help Docs: 'tools/help_docs.md'
     - Update Changelog: 'tools/update_changelog.md'
     - 💎 Add Documentation: 'tools/documentation.md'
     - 💎 Analyze: 'tools/analyze.md'
     - 💎 CI Feedback: 'tools/ci_feedback.md'
     - 💎 Custom Prompt: 'tools/custom_prompt.md'
     - 💎 Generate Labels: 'tools/custom_labels.md'
     - 💎 Generate Tests: 'tools/test.md'
     - 💎 Implement: 'tools/implement.md'
     - 💎 Improve Components: 'tools/improve_component.md'
     - 💎 Scan Repo Discussions: 'tools/scan_repo_discussions.md'
     - 💎 Similar Code: 'tools/similar_code.md'
  - Core Abilities:
      - 'core-abilities/index.md'
      - Auto approval: 'core-abilities/auto_approval.md'
      - Auto best practices: 'core-abilities/auto_best_practices.md'
      - Chat on code suggestions: 'core-abilities/chat_on_code_suggestions.md'
      - Chrome extension: 'chrome-extension/index.md'
      - Code validation: 'core-abilities/code_validation.md'
#      - Compression strategy: 'core-abilities/compression_strategy.md'
      - Dynamic context: 'core-abilities/dynamic_context.md'
      - Fetching ticket context: 'core-abilities/fetching_ticket_context.md'
      - Impact evaluation: 'core-abilities/impact_evaluation.md'
      - Incremental Update: 'core-abilities/incremental_update.md'
      - Interactivity: 'core-abilities/interactivity.md'
      - Local and global metadata: 'core-abilities/metadata.md'
      - RAG context enrichment: 'core-abilities/rag_context_enrichment.md'
      - Self-reflection: 'core-abilities/self_reflection.md'
      - Static code analysis: 'core-abilities/static_code_analysis.md'
#  - Chrome Extension:
#      - Qodo Merge Chrome Extension: 'chrome-extension/index.md'
#      - Features: 'chrome-extension/features.md'
#      - Data Privacy: 'chrome-extension/data_privacy.md'
#      - Options: 'chrome-extension/options.md'
  - PR Benchmark:
      - PR Benchmark: 'pr_benchmark/index.md'
  - Recent Updates:
      - Recent Updates: 'recent_updates/index.md'
  - AI Docs Search: 'ai_search/index.md'
#  - Code Fine-tuning Benchmark: 'finetuning_benchmark/index.md'

theme:
  logo: assets/logo.svg
  favicon: assets/favicon.ico
  name: material
  icon:
    repo: fontawesome/brands/github
  features:
    - navigation.tabs
    - navigation.expand
    - navigation.path
    - navigation.top
    - navigation.tracking
    - navigation.indexes
    - search.suggest
    - search.highlight
    - content.tabs.link
    - content.code.annotation
    - content.code.copy
  language: en
  custom_dir: overrides

  palette:
    - media: "(prefers-color-scheme)"
      toggle:
        icon: material/brightness-auto
        name: Switch to light mode
    - media: "(prefers-color-scheme: light)"
      scheme: default
      toggle:
        icon: material/toggle-switch-off-outline
        name: Switch to dark mode
      primary: custom
      accent: custom
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      toggle:
        icon: material/toggle-switch
        name: Switch to light mode
      primary: custom
      accent: custom

plugins:
  - social
  - search
  - glightbox

extra:
  generator: false
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/Codium-ai
    - icon: fontawesome/brands/discord
      link: https://discord.com/invite/SgSxuQ65GF
    - icon: fontawesome/brands/youtube
      link: https://www.youtube.com/@Codium-AI
    - icon: fontawesome/brands/linkedin
      link: https://www.linkedin.com/company/codiumai
    - icon: fontawesome/brands/twitter
      link: https://twitter.com/CodiumAI
    - icon: fontawesome/brands/instagram
      link: https://www.instagram.com/codiumai/
  analytics:
    provider: custom
    property: ${{ secrets.GOOGLE_ANALYTICS_ID }}

extra_css:
  - css/custom.css

markdown_extensions:
  - pymdownx.highlight:
      anchor_linenums: true
  - pymdownx.inlinehilite
  - pymdownx.snippets
  - admonition
  - pymdownx.arithmatex:
      generic: true
  - footnotes
  - pymdownx.details
  - pymdownx.superfences
  - pymdownx.mark
  - md_in_html
  - attr_list
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
  - pymdownx.tabbed:
      alternate_style: true
  - toc:
      title: On this page
      toc_depth: 3
      permalink: true


copyright: |
  &copy; 2025 <a href="https://www.codium.ai/"  target="_blank" rel="noopener">QodoAI</a>
