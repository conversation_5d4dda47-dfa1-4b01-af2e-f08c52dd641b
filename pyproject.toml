[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "pr-agent"
version = "0.3.0"

authors = [{ name = "QodoAI", email = "<EMAIL>" }]

maintainers = [
  { name = "Tal Ridnik", email = "<EMAIL>" },
]

description = "QodoAI PR-Agent aims to help efficiently review and handle pull requests, by providing AI feedbacks and suggestions."
readme = "README.md"
requires-python = ">=3.11"
keywords = ["AI", "Agents", "Pull Request", "Automation", "Code Review"]
license = { file = "LICENSE" }

classifiers = [
  "Intended Audience :: Developers",
  "Programming Language :: Python :: 3",
]
dynamic = ["dependencies"]


[tool.setuptools.dynamic]
dependencies = { file = ["requirements.txt"] }

[project.urls]
"Homepage" = "https://github.com/qodo-ai/pr-agent"
"Documentation" = "https://qodo-merge-docs.qodo.ai/"

[tool.setuptools]
include-package-data = true

[tool.setuptools.packages.find]
where = ["."]
include = [
  "pr_agent*",
] # include pr_agent and any sub-packages it finds under it.

[project.scripts]
pr-agent = "pr_agent.cli:run"

[tool.ruff]
line-length = 120

lint.select = [
  "E",    # Pyflakes
  "F",    # Pyflakes
  "B",    # flake8-bugbear
  "I001", # isort basic checks
  "I002", # isort missing-required-import
]

# First commit - only fixing isort
lint.fixable = [
  "I001", # isort basic checks
]

lint.unfixable = [
  "B", # Avoid trying to fix flake8-bugbear (`B`) violations.
]

lint.exclude = ["api/code_completions"]

lint.ignore = ["E999", "B008"]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = [
  "E402",
] # Ignore `E402` (import violations) in all `__init__.py` files, and in `path/to/file.py`.

[tool.bandit]
exclude_dirs = ["tests"]
skips = ["B101"]
tests = []
