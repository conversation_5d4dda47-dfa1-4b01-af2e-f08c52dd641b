{"permissions": {"allow": ["<PERSON><PERSON>(source:*)", "<PERSON><PERSON>(python:*)", "Bash(source .venv/bin/activate)", "Bash(python -m pr_agent.cli --pr_url=https://gitlab.basemind.com/raccoon/lipu-mobile/-/merge_requests/1921 describe --config.publish_output=false --config.verbosity_level=0 --config.response_language=\"zh-CN\")", "Bash(grep:*)", "Bash(ls:*)", "Bash(pip install:*)", "<PERSON><PERSON>(sed:*)", "Bash(git add:*)", "Bash(MARKDOWN_FILE=./fixtures/mr_description_summary_1921.md DIFF_FILE=./fixtures/diff_1921.json bun run build)", "<PERSON><PERSON>(open:*)", "Bash(bun run:*)", "<PERSON><PERSON>(npx playwright:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(bun add:*)", "Bash(rg:*)", "Bash(pr-agent --pr_url=https://gitlab.basemind.com/raccoon/lipu-mobile/-/merge_requests/1921 describe)"], "deny": []}}