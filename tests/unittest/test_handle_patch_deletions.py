# Generated by CodiumAI
import logging

from pr_agent.algo.git_patch_processing import handle_patch_deletions
from pr_agent.config_loader import get_settings

"""
Code Analysis

Objective:
The objective of the function is to handle entire file or deletion patches and return the patch after omitting the
deletion hunks.

Inputs:
- patch: a string representing the patch to be handled
- original_file_content_str: a string representing the original content of the file
- new_file_content_str: a string representing the new content of the file
- file_name: a string representing the name of the file

Flow:
- If new_file_content_str is empty, set patch to "File was deleted" and return it
- Otherwise, split patch into lines and omit the deletion hunks using the omit_deletion_hunks function
- If the resulting patch is different from the original patch, log a message and set patch to the new patch
- Return the resulting patch

Outputs:
- A string representing the patch after omitting the deletion hunks

Additional aspects:
- The function uses the settings from the configuration files to determine the verbosity level of the logging messages
- The omit_deletion_hunks function is called to remove the deletion hunks from the patch
- The function handles the case where the new_file_content_str is empty by setting the patch to "File was deleted"
"""


class TestHandlePatchDeletions:
    # Tests that handle_patch_deletions returns the original patch when new_file_content_str is not empty
    def test_handle_patch_deletions_happy_path_new_file_content_exists(self):
        patch = '--- a/file.py\n+++ b/file.py\n@@ -1,2 +1,2 @@\n-foo\n-bar\n+baz\n'
        original_file_content_str = 'foo\nbar\n'
        new_file_content_str = 'foo\nbaz\n'
        file_name = 'file.py'
        assert handle_patch_deletions(patch, original_file_content_str, new_file_content_str,
                                      file_name) == patch.rstrip()

    # Tests that handle_patch_deletions returns 'File was deleted' when new_file_content_str is empty
    def test_handle_patch_deletions_edge_case_new_file_content_empty(self):
        patch = '--- a/file.py\n+++ b/file.py\n@@ -1,2 +1,2 @@\n-foo\n-bar\n'
        original_file_content_str = 'foo\nbar\n'
        new_file_content_str = ''
        file_name = 'file.py'
        assert handle_patch_deletions(patch, original_file_content_str, new_file_content_str,
                                      file_name) is None

    # Tests that handle_patch_deletions returns the original patch when patch and patch_new are equal
    def test_handle_patch_deletions_edge_case_patch_and_patch_new_are_equal(self):
        patch = '--- a/file.py\n+++ b/file.py\n@@ -1,2 +1,2 @@\n-foo\n-bar\n'
        original_file_content_str = 'foo\nbar\n'
        new_file_content_str = 'foo\nbar\n'
        file_name = 'file.py'
        assert handle_patch_deletions(patch, original_file_content_str, new_file_content_str,
                                      file_name).rstrip() == patch.rstrip()

    # Tests that handle_patch_deletions returns the modified patch when patch and patch_new are not equal
    def test_handle_patch_deletions_edge_case_patch_and_patch_new_are_not_equal(self):
        patch = '--- a/file.py\n+++ b/file.py\n@@ -1,2 +1,2 @@\n-foo\n-bar\n'
        original_file_content_str = 'foo\nbar\n'
        new_file_content_str = 'foo\nbaz\n'
        file_name = 'file.py'
        expected_patch = '--- a/file.py\n+++ b/file.py\n@@ -1,2 +1,2 @@\n-foo\n-bar'
        assert handle_patch_deletions(patch, original_file_content_str, new_file_content_str,
                                      file_name) == expected_patch
