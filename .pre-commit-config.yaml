# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks

default_language_version:
  python: python3

repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: check-added-large-files
      - id: check-toml
      - id: check-yaml
      - id: end-of-file-fixer
      - id: trailing-whitespace
  # - repo: https://github.com/rhysd/actionlint
  #   rev: v1.7.3
  #   hooks:
  #     - id: actionlint
  - repo: https://github.com/pycqa/isort
    # rev must match what's in dev-requirements.txt
    rev: 5.13.2
    hooks:
      - id: isort
  # - repo: https://github.com/PyCQA/bandit
  #   rev: 1.7.10
  #   hooks:
  #     - id: bandit
  #       args: [
  #         "-c", "pyproject.toml",
  #       ]
  # - repo: https://github.com/astral-sh/ruff-pre-commit
  #   rev: v0.7.1
  #   hooks:
  #     - id: ruff
  #       args:
  #         - --fix
  #     - id: ruff-format
  # -   repo: https://github.com/PyCQA/autoflake
  #     rev: v2.3.1
  #     hooks:
  #     -   id: autoflake
  #         args:
  #           - --in-place
  #           - --remove-all-unused-imports
  #           - --remove-unused-variables
