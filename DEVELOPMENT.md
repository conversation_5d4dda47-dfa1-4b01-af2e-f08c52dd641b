# PR-Agent 开发环境设置指南

本项目使用现代化的 Python 工具链进行开发。

## 环境要求

- Python 3.12+
- [uv](https://docs.astral.sh/uv/) - 现代化的 Python 包管理器

## 快速开始

### 1. 创建并激活虚拟环境

```bash
# 创建虚拟环境
uv venv --python 3.12

# 激活虚拟环境
source .venv/bin/activate
```

### 2. 安装依赖

```bash
# 安装项目依赖
uv pip install -r requirements.txt

# 安装开发依赖
uv pip install -r requirements-dev.txt

# 以可编辑模式安装项目本身
uv pip install -e .
```

### 3. 配置 PR-Agent

复制配置模板并设置必要的密钥：

```bash
# 复制配置模板
cp pr_agent/settings/.secrets_template.toml pr_agent/settings/.secrets.toml

# 编辑配置文件，添加必要的 API 密钥
# 至少需要设置：
# - AI 模型 API 密钥 (OpenAI 或 Gemini)
# - Git 平台访问令牌 (GitHub, GitLab, 等)
```

### 4. 验证安装

```bash
# 测试命令行工具
pr-agent --help

# 运行测试 (可选)
pytest
```

## 项目结构

```
pr-agent/
├── pr_agent/              # 主要代码包
│   ├── algo/              # AI 算法和处理器
│   ├── git_providers/     # Git 平台集成
│   ├── settings/          # 配置文件
│   └── tools/             # PR 分析工具
├── tests/                 # 测试代码
├── docs/                  # 文档
├── .venv/                 # 虚拟环境 (git 忽略)
├── requirements.txt       # 生产依赖
├── requirements-dev.txt   # 开发依赖
└── pyproject.toml         # 项目配置
```

## 故障排除

### 常见问题

1. **Python 版本问题**: 确保使用 Python 3.12+
2. **依赖冲突**: 删除 `.venv` 目录后重新创建
3. **权限问题**: 确保有正确的 Git 平台访问权限

### 重置环境

```bash
# 完全重置环境
rm -rf .venv
uv venv --python 3.12
source .venv/bin/activate
uv pip install -r requirements.txt
uv pip install -r requirements-dev.txt
uv pip install -e .
```

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 编写测试
4. 确保代码通过所有检查
5. 提交 Pull Request

---

关于更多使用说明，请参考主 [README.md](README.md) 文件。 