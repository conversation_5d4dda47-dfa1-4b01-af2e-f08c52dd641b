aiohttp==3.9.5
anthropic>=0.52.0
#anthropic[vertex]==0.47.1
atlassian-python-api==3.41.4
azure-devops==7.1.0b3
azure-identity==1.15.0
boto3==1.33.6
certifi==2024.8.30
dynaconf==3.2.4
fastapi==0.111.0
GitPython==3.1.41
google-cloud-aiplatform==1.38.0
google-generativeai==0.8.3
google-cloud-storage==2.10.0
Jinja2==3.1.2
litellm==1.70.4
loguru==0.7.2
msrest==0.7.1
openai>=1.55.3
pytest==7.4.0
PyGithub==1.59.*
PyYAML==6.0.1
python-gitlab==3.15.0
retry==0.9.2
starlette-context==0.3.6
tiktoken==0.8.0
ujson==5.8.0
uvicorn==0.22.0
tenacity==8.2.3
gunicorn==22.0.0
pytest-cov==5.0.0
pydantic==2.8.2
html2text==2024.2.26
giteapy==1.0.8
# Uncomment the following lines to enable the 'similar issue' tool
# pinecone-client
# pinecone-datasets @ git+https://github.com/mrT23/pinecone-datasets.git@main
# lancedb==0.5.1
# uncomment this to support language LangChainOpenAIHandler
# langchain==0.2.0
# langchain-core==0.2.28
# langchain-openai==0.1.20
