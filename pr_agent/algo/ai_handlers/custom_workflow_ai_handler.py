import json
import aiohttp
import asyncio
from typing import Tuple, Optional

from pr_agent.algo.ai_handlers.base_ai_handler import BaseAiHandler
from pr_agent.config_loader import get_settings
from pr_agent.log import get_logger


class CustomWorkflowAi<PERSON>andler(BaseAiHandler):
    """
    自定义工作流 AI 处理器
    支持基于工作流 ID 的简化 API 调用
    """
    
    def __init__(self):
        """
        初始化自定义工作流 AI 处理器
        """
        self.access_token = get_settings().get("CUSTOM_WORKFLOW.ACCESS_TOKEN")
        self.api_base_url = get_settings().get("CUSTOM_WORKFLOW.API_BASE_URL")
        self.workflow_id = get_settings().get("CUSTOM_WORKFLOW.WORKFLOW_ID")
        
        if not self.access_token:
            raise ValueError("CUSTOM_WORKFLOW.ACCESS_TOKEN 配置缺失")
        if not self.api_base_url:
            raise ValueError("CUSTOM_WORKFLOW.API_BASE_URL 配置缺失")
        if not self.workflow_id:
            raise ValueError("CUSTOM_WORKFLOW.WORKFLOW_ID 配置缺失")
            
        get_logger().info(f"初始化自定义工作流 AI 处理器，工作流 ID: {self.workflow_id}")
    
    @property
    def deployment_id(self):
        """
        返回部署 ID
        """
        return self.workflow_id
    
    async def chat_completion(self, model: str, system: str, user: str, temperature: float = 0.2, img_path: str = None) -> Tuple[str, str]:
        """
        执行聊天完成请求
        
        Args:
            model: 模型名称（在工作流模式下忽略）
            system: 系统提示词
            user: 用户提示词
            temperature: 温度参数（在工作流模式下忽略）
            img_path: 图片路径（暂不支持）
            
        Returns:
            Tuple[str, str]: (响应内容, 结束原因)
        """
        if img_path:
            get_logger().warning("自定义工作流 API 暂不支持图片输入")
            return "自定义工作流 API 暂不支持图片输入", "error"
        
        # 构建请求数据
        request_data = {
            "workflow_id": self.workflow_id,
            "data": {
                "system": system,
                "user": user
            }
        }
        
        # 构建请求头
        headers = {
            "Authorization": f"Bearer {self.access_token}",
            "Content-Type": "application/json"
        }
        
        # 构建 URL
        url = f"{self.api_base_url}/openapi/v1/workflow/call"
        
        try:
            get_logger().debug(f"调用自定义工作流 API: {url}")
            get_logger().debug(f"请求数据: {json.dumps(request_data, ensure_ascii=False)}")
            
            # 发送异步请求
            connector = aiohttp.TCPConnector(ssl=False)  # 跳过 SSL 验证
            async with aiohttp.ClientSession(connector=connector) as session:
                async with session.post(
                    url,
                    json=request_data,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=get_settings().config.get("ai_timeout", 120))
                ) as response:
                    result = await response.json()
                    
                    # 检查 HTTP 状态码
                    if response.status != 200:
                        error_msg = f"HTTP 错误: {response.status} {response.reason}"
                        get_logger().error(f"{error_msg}\n响应: {json.dumps(result, ensure_ascii=False)}")
                        return error_msg, "error"
                    
                    # 检查业务状态码
                    if result.get("code") != 0:
                        error_msg = f"API 错误: {result.get('msg', '未知错误')}"
                        get_logger().error(error_msg)
                        return error_msg, "error"
                    
                    # 提取响应内容
                    response_text = result.get("data", {}).get("result", {}).get("text", "")
                    
                    if not response_text:
                        error_msg = "API 响应中未找到有效内容"
                        get_logger().error(f"{error_msg}\n完整响应: {json.dumps(result, ensure_ascii=False)}")
                        return error_msg, "error"
                    
                    get_logger().debug(f"AI 响应: {response_text}")
                    get_logger().info("自定义工作流 API 调用成功")
                    
                    return response_text, "stop"
                    
        except asyncio.TimeoutError:
            error_msg = "请求超时"
            get_logger().error(error_msg)
            return error_msg, "error"
        except aiohttp.ClientError as e:
            error_msg = f"网络请求错误: {str(e)}"
            get_logger().error(error_msg)
            return error_msg, "error"
        except json.JSONDecodeError as e:
            error_msg = f"响应解析错误: {str(e)}"
            get_logger().error(error_msg)
            return error_msg, "error"
        except Exception as e:
            error_msg = f"未知错误: {str(e)}"
            get_logger().error(error_msg)
            return error_msg, "error"