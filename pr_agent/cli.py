import argparse
import asyncio
import os
import warnings

# Suppress Azure pkg_resources deprecation warnings
warnings.filterwarnings("ignore", message="pkg_resources is deprecated", category=UserWarning)

from pr_agent.agent.pr_agent import PRAgent, commands
from pr_agent.algo.utils import get_version
from pr_agent.config_loader import get_settings, apply_environment_variables
from pr_agent.log import get_logger, setup_logger

log_level = os.environ.get("LOG_LEVEL", "INFO")
setup_logger(log_level)

# Apply environment variables before any other operations
apply_environment_variables()


def _export_mr_description_to_file(pr_url):
    """Export MR description result to file"""
    try:
        import re
        
        # Extract MR ID from URL
        def extract_mr_id(url):
            # For commit range: project-path#commit1..commit2
            if '#' in url and ('...' in url or '..' in url):
                project_path, commit_range = url.split('#')
                return commit_range.replace('/', '_')
            
            # For GitLab: extract number after /merge_requests/
            gitlab_match = re.search(r'merge_requests/(\d+)', url)
            if gitlab_match:
                return gitlab_match.group(1)
            
            # For GitHub: extract number after /pull/
            github_match = re.search(r'/pull/(\d+)', url)
            if github_match:
                return github_match.group(1)
            
            # For other providers or if it's already just a number
            number_match = re.search(r'\d+', url)
            if number_match:
                return number_match.group(0)
            
            return "unknown"
        
        # Get the description data from settings
        data = get_settings().data
        if not data or not isinstance(data, dict) or "artifact" not in data:
            get_logger().warning("No description artifact found to export")
            return
        
        artifact = data["artifact"]
        if not artifact:
            get_logger().warning("Empty description artifact")
            return
        
        # Generate filename
        mr_id = extract_mr_id(pr_url)
        # 优先使用环境变量 PR_AGENT_OUTPUT_DIR
        export_dir = os.environ.get('PR_AGENT_OUTPUT_DIR') or get_settings().config.get('export_mr_description_dir', 'debug_output')
        filename = f"mr_description_summary_{mr_id}.md"
        filepath = os.path.join(export_dir, filename)
        
        # Ensure directory exists
        os.makedirs(export_dir, exist_ok=True)
        
        # Write file
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(artifact)
        
        get_logger().info(f"✅ MR description exported to: {filepath}")
        
    except Exception as e:
        get_logger().error(f"Failed to export MR description: {str(e)}")


def set_parser():
    parser = argparse.ArgumentParser(description='AI based pull request analyzer', usage=
    """\
    Usage: cli.py --pr-url=<URL on supported git hosting service> <command> [<args>].
    For example:
    - cli.py --pr_url=... review
    - cli.py --pr_url=... describe
    - cli.py --pr_url=... improve
    - cli.py --pr_url=... ask "write me a poem about this PR"
    - cli.py --pr_url=... reflect
    - cli.py --issue_url=... similar_issue
    - cli.py --pr_url/--issue_url= help_docs [<asked question>]

    Supported commands:
    - review / review_pr - Add a review that includes a summary of the PR and specific suggestions for improvement.

    - ask / ask_question [question] - Ask a question about the PR.

    - describe / describe_pr - Modify the PR title and description based on the PR's contents.

    - improve / improve_code - Suggest improvements to the code in the PR as pull request comments ready to commit.
    Extended mode ('improve --extended') employs several calls, and provides a more thorough feedback

    - reflect - Ask the PR author questions about the PR.

    - update_changelog - Update the changelog based on the PR's contents.

    - add_docs

    - generate_labels
    
    - help_docs - Ask a question, from either an issue or PR context, on a given repo (current context or a different one)


    Configuration:
    To edit any configuration parameter from 'configuration.toml', just add -config_path=<value>.
    For example: 'python cli.py --pr_url=... review --pr_reviewer.extra_instructions="focus on the file: ..."'
    """)
    parser.add_argument('--version', action='version', version=f'pr-agent {get_version()}')
    parser.add_argument('--pr_url', type=str, help='The URL of the PR to review', default=None)
    parser.add_argument('--issue_url', type=str, help='The URL of the Issue to review', default=None)
    parser.add_argument('--commit-range', type=str, help='Compare two commits (format: "from_commit..to_commit" or "from_commit...to_commit")', default=None)
    parser.add_argument('--project-path', type=str, help='GitLab project path (e.g., "group/project")', default=None)
    parser.add_argument('command', type=str, help='The', choices=commands, default='review')
    parser.add_argument('rest', nargs=argparse.REMAINDER, default=[])
    return parser


def run_command(pr_url, command):
    # Preparing the command
    run_command_str = f"--pr_url={pr_url} {command.lstrip('/')}"
    args = set_parser().parse_args(run_command_str.split())

    # Run the command. Feedback will appear in GitHub PR comments
    run(args=args)


def run(inargs=None, args=None):
    parser = set_parser()
    if not args:
        args = parser.parse_args(inargs)
    if not args.pr_url and not args.issue_url and not args.commit_range:
        parser.print_help()
        return

    command = args.command.lower()
    get_settings().set("CONFIG.CLI_MODE", True)

    async def inner():
        if args.issue_url:
            result = await asyncio.create_task(PRAgent().handle_request(args.issue_url, [command] + args.rest))
        elif args.commit_range:
            # Create a pseudo-URL for commit range comparison
            if not args.project_path:
                parser.error("--project-path is required when using --commit-range")
            commit_url = f"commit-range://{args.project_path}#{args.commit_range}"
            result = await asyncio.create_task(PRAgent().handle_request(commit_url, [command] + args.rest))
        else:
            result = await asyncio.create_task(PRAgent().handle_request(args.pr_url, [command] + args.rest))

        if get_settings().litellm.get("enable_callbacks", False):
            # There may be additional events on the event queue from the run above. If there are give them time to complete.
            get_logger().debug("Waiting for event queue to complete")
            tasks = [task for task in asyncio.all_tasks() if task is not asyncio.current_task()]
            if tasks:
                _, pending = await asyncio.wait(tasks, timeout=30)
                if pending:
                    get_logger().warning(
                        f"{len(pending)} callback tasks({[task.get_coro() for task in pending]}) did not complete within timeout"
                    )

        return result

    result = asyncio.run(inner())
    
    # Export MR description to file if enabled
    if (command == "describe" and 
        get_settings().config.get("export_mr_description", False)):
        try:
            export_url = args.pr_url or args.issue_url
            if args.commit_range:
                export_url = f"{args.project_path}#{args.commit_range}"
            _export_mr_description_to_file(export_url)
        except Exception as e:
            get_logger().error(f"Failed to export MR description: {e}")
    
    if not result:
        parser.print_help()


if __name__ == '__main__':
    run()
