{"name": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON>", "key": "app_key", "vendor": {"name": "Qodo", "url": "https://qodo.ai"}, "authentication": {"type": "jwt"}, "baseUrl": "base_url", "lifecycle": {"installed": "/installed", "uninstalled": "/uninstalled"}, "scopes": ["account", "repository:write", "pullrequest:write", "wiki"], "contexts": ["account"], "modules": {"webhooks": [{"event": "*", "url": "/webhook"}]}, "links": {"privacy": "https://qodo.ai/privacy-policy", "terms": "https://qodo.ai/terms"}}