[ignore]

glob = [
    # Ignore files and directories matching these glob patterns.
    # See https://docs.python.org/3/library/glob.html
    'vendor/**',
    
    # Only ignore truly irrelevant binary files - let important visual assets be handled by content processing
    # Note: Images, videos, and fonts are now processed by clean_patch_content instead of being completely ignored
    '**/*.{zip,tar,tar.gz,rar,7z,gz,bz2,xz}',  # Archive files
    '**/*.{exe,dll,so,dylib,bin,deb,rpm,msi}',  # Executable/system files
    '**/*.{pdf,doc,docx,xls,xlsx,ppt,pptx}',   # Office documents
    
    # Package management and lock files
    '**/package-lock.json',
    '**/yarn.lock',
    '**/pnpm-lock.yaml',
    '**/poetry.lock',
    '**/Pipfile.lock',
    '**/Cargo.lock',
    '**/go.sum',
    '**/composer.lock',
    '**/Gemfile.lock',
    '**/mix.lock',
    '**/*.jar',
    '**/*.war',
    '**/*.ear',
    '**/*.nupkg',
    
    # Build and distribution directories
    '**/node_modules/**',
    '**/dist/**',
    '**/build/**',
    '**/target/**',
    '**/bin/**',
    '**/obj/**',
    '**/out/**',
    '**/.next/**',
    '**/.nuxt/**',
    '**/coverage/**',
    
    # IDE and editor files
    '**/.vscode/**',
    '**/.idea/**',
    '**/*.iml',
    '**/.project',
    '**/.classpath',
    '**/.settings/**',
    
    # System and temporary files
    '**/.DS_Store',
    '**/Thumbs.db',
    '**/*.tmp',
    '**/*.temp',
    '**/*.log',
    '**/*.swp',
    '**/*.swo',
    '**/*~',
]
regex = [
    # Ignore files and directories matching these regex patterns.
    # See https://learnbyexample.github.io/python-regex-cheatsheet/
    # for example: regex = ['.*\.toml$']
]
