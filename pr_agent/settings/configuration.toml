# Important: This file contains all available configuration options.
# Do not copy this entire file to your repository configuration.
# Your repository configuration should only include options you wish to override from the defaults.
#
# Use this page: 'https://qodo-merge-docs.qodo.ai/ai_search/' to ask questions about the configuration options.

[config]
# models
model="vertex_ai/gemini-2.5-pro-preview-03-25"
fallback_models=[]
# AI handler selection: "litellm" (default) or "custom_workflow"
ai_handler="custom_workflow"
#model_reasoning="o4-mini" # dedictated reasoning model for self-reflection
#model_weak="gpt-4o" # optional, a weaker model to use for some easier tasks
# CLI
git_provider="gitlab"
********************
publish_output_progress=true
verbosity_level=0 # 0,1,2
use_extra_bad_extensions=false
# Log
log_level="DEBUG"
# Configurations
use_wiki_settings_file=true
use_repo_settings_file=true
use_global_settings_file=true
disable_auto_feedback = false
ai_timeout=120 # 2minutes
skip_keys = []
custom_reasoning_model = false # when true, disables system messages and temperature controls for models that don't support chat-style inputs
response_language="zh-CN" # Language locales code for PR responses in ISO 3166 and ISO 639 format (e.g., "en-US", "it-IT", "zh-CN", ...)
# token limits
max_description_tokens = 2000  # Increased for better PR descriptions
max_commits_tokens = 1000      # Increased to handle more commit messages
max_model_tokens = 800000      # Set to 800K for Gemini 2.5 Pro (leaving 200K buffer from 1M limit)
custom_model_max_tokens=-1     # for models not in the default list
model_token_count_estimate_factor=0.3 # factor to increase the token count estimate, in order to reduce likelihood of model failure due to too many tokens - applicable only when requesting an accurate estimate.
# patch extension logic
patch_extension_skip_types = [
    ".md", ".txt",
    # Binary and media files
    ".png", ".jpg", ".jpeg", ".gif", ".svg", ".ico", ".webp", ".bmp", ".tiff",
    ".mp4", ".avi", ".mov", ".mkv", ".wmv", ".flv", ".webm",
    ".mp3", ".wav", ".flac", ".aac", ".ogg", ".wma",
    ".zip", ".tar", ".tar.gz", ".rar", ".7z", ".gz", ".bz2", ".xz",
    ".exe", ".dll", ".so", ".dylib", ".bin", ".deb", ".rpm", ".msi",
    ".ttf", ".otf", ".woff", ".woff2", ".eot",
    ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
    # Compiled files
    ".jar", ".war", ".ear", ".nupkg", ".class", ".pyc", ".pyo",
]
allow_dynamic_context=true
max_extra_lines_before_dynamic_context = 10 # will try to include up to 10 extra lines before the hunk in the patch, until we reach an enclosing function or class
patch_extra_lines_before = 5 # Number of extra lines (+3 default ones) to include before each hunk in the patch
patch_extra_lines_after = 1 # Number of extra lines (+3 default ones) to include after each hunk in the patch
secret_provider="" # "" (disabled), "google_cloud_storage", or "aws_secrets_manager" for secure secret management
cli_mode=false
ai_disclaimer_title=""  # Pro feature, title for a collapsible disclaimer to AI outputs
ai_disclaimer=""  # Pro feature, full text for the AI disclaimer
output_relevant_configurations=false
large_patch_policy = "clip" # "clip", "skip"
duplicate_prompt_examples = false
# debug settings
debug_ai_response = true
debug_prompts = true
debug_output_dir = "./mr-report-generator/fixtures"
export_diff_data = true
export_diff_dir = "./mr-report-generator/fixtures"
export_mr_description = true
export_mr_description_dir = "./mr-report-generator/fixtures"

# seed
seed=-1 # set positive value to fix the seed (and ensure temperature=0)
temperature=0.2
# ignore logic
ignore_pr_title = ["^\\[Auto\\]", "^Auto"] # a list of regular expressions to match against the PR title to ignore the PR agent
ignore_pr_target_branches = [] # a list of regular expressions of target branches to ignore from PR agent when an PR is created
ignore_pr_source_branches = [] # a list of regular expressions of source branches to ignore from PR agent when an PR is created
ignore_pr_labels = [] # labels to ignore from PR agent when an PR is created
ignore_pr_authors = [] # authors to ignore from PR agent when an PR is created
ignore_repositories = [] # a list of regular expressions of repository full names (e.g. "org/repo") to ignore from PR agent processing
ignore_language_framework = [] # a list of code-generation languages or frameworks (e.g. 'protobuf', 'go_gen') whose auto-generated source files will be excluded from analysis
#
is_auto_command = false # will be auto-set to true if the command is triggered by an automation
enable_ai_metadata = false # will enable adding ai metadata
reasoning_effort = "medium" # "low", "medium", "high"
# auto approval 💎
enable_auto_approval=false # Set to true to enable auto-approval of PRs under certain conditions
auto_approve_for_low_review_effort=-1 # -1 to disable, [1-5] to set the threshold for auto-approval
auto_approve_for_no_suggestions=false # If true, the PR will be auto-approved if there are no suggestions
ensure_ticket_compliance=false # Set to true to disable auto-approval of PRs if the ticket is not compliant
# extended thinking for Claude reasoning models
enable_claude_extended_thinking = false # Set to true to enable extended thinking feature
extended_thinking_budget_tokens = 2048
extended_thinking_max_output_tokens = 4096


[pr_reviewer] # /review #
# enable/disable features
require_score_review=false
require_tests_review=true
require_estimate_effort_to_review=true
require_can_be_split_review=false
require_security_review=true
require_todo_scan=false
require_ticket_analysis_review=true
# general options
publish_output_no_suggestions=true # Set to "false" if you only need the reviewer's remarks (not labels, not "security audit", etc.) and want to avoid noisy "No major issues detected" comments.
persistent_comment=true
extra_instructions = ""
num_max_findings = 3
final_update_message = true
# review labels
enable_review_labels_security=true
enable_review_labels_effort=true
# specific configurations for incremental review (/review -i)
require_all_thresholds_for_incremental_review=false
minimal_commits_for_incremental_review=0
minimal_minutes_for_incremental_review=0
enable_intro_text=true
enable_help_text=false # Determines whether to include help text in the PR review. Enabled by default.

[pr_description] # /describe #
publish_labels=false
add_original_user_description=true
generate_ai_title=false
use_bullet_points=true
extra_instructions = ""
enable_pr_type=true
final_update_message = true
enable_help_text=false
enable_help_comment=true
enable_pr_diagram=true # adds a section with a diagram of the PR changes
# describe as comment
publish_description_as_comment=false
publish_description_as_comment_persistent=true
## changes walkthrough section
enable_semantic_files_types=true
collapsible_file_list='adaptive' # true, false, 'adaptive'
collapsible_file_list_threshold=6
inline_file_summary=false # false, true, 'table'
# markers
use_description_markers=false
include_generated_by_header=true
# large pr mode 💎
enable_large_pr_handling=true
max_ai_calls=4
async_ai_calls=true
#custom_labels = ['Bug fix', 'Tests', 'Bug fix with tests', 'Enhancement', 'Documentation', 'Other']

[pr_questions] # /ask #
enable_help_text=false
use_conversation_history=true


[pr_code_suggestions] # /improve #
max_context_tokens=600000  # Increased for Gemini 2.5 Pro to handle larger PRs
#
commitable_code_suggestions = false
dual_publishing_score_threshold=-1 # -1 to disable, [0-10] to set the threshold (>=) for publishing a code suggestion both in a table and as commitable
focus_only_on_problems=true
#
extra_instructions = ""
enable_help_text=false
enable_chat_text=false
persistent_comment=true
max_history_len=4
publish_output_no_suggestions=true
# enable to apply suggestion 💎
apply_suggestions_checkbox=true
# suggestions scoring
suggestions_score_threshold=0 # [0-10]| recommend not to set this value above 8, since above it may clip highly relevant suggestions
new_score_mechanism=true
new_score_mechanism_th_high=9
new_score_mechanism_th_medium=7
# params for '/improve --extended' mode
auto_extended_mode=true
num_code_suggestions_per_chunk=4
max_number_of_calls = 3
parallel_calls = true

final_clip_factor = 0.8
decouple_hunks = false
# self-review checkbox
demand_code_suggestions_self_review=false # add a checkbox for the author to self-review the code suggestions
code_suggestions_self_review_text= "**Author self-review**: I have reviewed the PR code suggestions, and addressed the relevant ones."
approve_pr_on_self_review=false # Pro feature. if true, the PR will be auto-approved after the author clicks on the self-review checkbox
fold_suggestions_on_self_review=true # Pro feature. if true, the code suggestions will be folded after the author clicks on the self-review checkbox
# Suggestion impact 💎
publish_post_process_suggestion_impact=true
wiki_page_accepted_suggestions=true
allow_thumbs_up_down=false

[pr_custom_prompt] # /custom_prompt #
prompt = """\
The code suggestions should focus only on the following:
- ...
- ...
...
"""
suggestions_score_threshold=0
num_code_suggestions_per_chunk=3
self_reflect_on_custom_suggestions=true
enable_help_text=false


[pr_add_docs] # /add_docs #
extra_instructions = ""
docs_style = "Sphinx" # "Google Style with Args, Returns, Attributes...etc", "Numpy Style", "Sphinx Style", "PEP257", "reStructuredText"
file = ""              # in case there are several components with the same name, you can specify the relevant file
class_name = ""        # in case there are several methods with the same name in the same file, you can specify the relevant class name

[pr_update_changelog] # /update_changelog #
push_changelog_changes=false
extra_instructions = ""
add_pr_link=true
skip_ci_on_push=true

[pr_analyze] # /analyze #
enable_help_text=true

[pr_test] # /test #
extra_instructions = ""
testing_framework = "" # specify the testing framework you want to use
num_tests=3            # number of tests to generate. max 5.
avoid_mocks=true       # if true, the generated tests will prefer to use real objects instead of mocks
file = ""              # in case there are several components with the same name, you can specify the relevant file
class_name = ""        # in case there are several methods with the same name in the same file, you can specify the relevant class name
enable_help_text=false

[pr_improve_component] # /improve_component #
num_code_suggestions=4
extra_instructions = ""
file = ""              # in case there are several components with the same name, you can specify the relevant file
class_name = ""        # in case there are several methods with the same name in the same file, you can specify the relevant class name

[checks] # /checks (pro feature) #
enable_auto_checks_feedback=true
excluded_checks_list=["lint"] # list of checks to exclude, for example: ["check1", "check2"]
persistent_comment=true
enable_help_text=true
final_update_message = false

[pr_help] # /help #
force_local_db=false
num_retrieved_snippets=5

[pr_config] # /config #

[pr_help_docs]
repo_url = "" #If not overwritten, will use the repo from where the context came from (issue or PR)
repo_default_branch = "main"
docs_path = "docs"
exclude_root_readme = false
supported_doc_exts = [".md", ".mdx", ".rst"]
enable_help_text=false

[github]
# The type of deployment to create. Valid values are 'app' or 'user'.
deployment_type = "user"
ratelimit_retries = 5
base_url = "https://api.github.com"
publish_inline_comments_fallback_with_verification = true
try_fix_invalid_inline_comments = true
app_name = "pr-agent"
ignore_bot_pr = true

[github_action_config]
# auto_review = true    # set as env var in .github/workflows/pr-agent.yaml
# auto_describe = true  # set as env var in .github/workflows/pr-agent.yaml
# auto_improve = true   # set as env var in .github/workflows/pr-agent.yaml
# pr_actions = ['opened', 'reopened', 'ready_for_review', 'review_requested']

[github_app]
# these toggles allows running the github app from custom deployments
bot_user = "github-actions[bot]"
override_deployment_type = true
# settings for "pull_request" event
handle_pr_actions = ['opened', 'reopened', 'ready_for_review']
pr_commands = [
    "/describe --pr_description.final_update_message=false",
    "/review",
    "/improve",
]
# settings for "pull_request" event with "synchronize" action - used to detect and handle push triggers for new commits
handle_push_trigger = false
push_trigger_ignore_bot_commits = true
push_trigger_ignore_merge_commits = true
push_trigger_wait_for_initial_review = true
push_trigger_pending_tasks_backlog = true
push_trigger_pending_tasks_ttl = 300
push_commands = [
    "/describe",
    "/review",
]

[gitlab]
url = "https://gitlab.basemind.com"
pr_commands = [
    "/describe --pr_description.final_update_message=false",
    "/review",
    "/improve",
]
handle_push_trigger = false
push_commands = [
    "/describe",
    "/review",
]

[gitea_app]
url = "https://gitea.com"
handle_push_trigger = false
pr_commands = [
    "/describe",
    "/review",
    "/improve",
]

[bitbucket_app]
pr_commands = [
    "/describe --pr_description.final_update_message=false",
    "/review",
    "/improve --pr_code_suggestions.commitable_code_suggestions=true",
]
avoid_full_files = false

[local]
# LocalGitProvider settings - uncomment to use paths other than default
# description_path= "path/to/description.md"
# review_path= "path/to/review.md"

[gerrit]
# endpoint to the gerrit service
# url = "ssh://gerrit.example.com:29418"
# user for gerrit authentication
# user = "ai-reviewer"
# patch server where patches will be saved
# patch_server_endpoint = "http://127.0.0.1:5000/patch"
# token to authenticate in the patch server
# patch_server_token = ""

[bitbucket_server]
# URL to the BitBucket Server instance
# url = "https://git.bitbucket.com"
url = ""
pr_commands = [
    "/describe --pr_description.final_update_message=false",
    "/review",
    "/improve --pr_code_suggestions.commitable_code_suggestions=true",
]

[litellm]
# use_client = false
# drop_params = false
enable_callbacks = false
success_callback = []
failure_callback = []
service_callback = []

[pr_similar_issue]
skip_comments = false
force_update_dataset = false
max_issues_to_scan = 500
vectordb = "pinecone"

[pr_find_similar_component]
class_name = ""
file = ""
search_from_org = false
allow_fallback_less_words = true
number_of_keywords = 5
number_of_results = 5

[pinecone]
# fill and place in .secrets.toml
#api_key = ...
# environment = "gcp-starter"

[lancedb]
uri = "./lancedb"

[best_practices]
content = ""
organization_name = ""
max_lines_allowed = 800
enable_global_best_practices = false

[auto_best_practices]
enable_auto_best_practices = true # public - general flag to disable all auto best practices usage
utilize_auto_best_practices = true # public - disable usage of auto best practices in the 'improve' tool
extra_instructions = "" # public - extra instructions to the auto best practices generation prompt
content = ""
max_patterns = 5 # max number of patterns to be detected


[azure_devops_server]
pr_commands = [
    "/describe",
    "/review",
    "/improve",
]

[custom_workflow]
# 自定义工作流 API 配置
# access_token 在 .secrets.toml 中设置
api_base_url = "https://flow.stepfun-inc.com"  # API 基础 URL，可通过环境变量 CUSTOM_WORKFLOW_API_BASE_URL 设置
workflow_id = "1132"   # 工作流 ID，可通过环境变量 CUSTOM_WORKFLOW_WORKFLOW_ID 设置
