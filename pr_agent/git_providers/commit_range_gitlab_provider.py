import re
from typing import List, Optional
from urllib.parse import urlparse

import gitlab
from gitlab import GitlabGetError, GitlabAuthenticationError

from pr_agent.algo.types import EDIT_TYPE, FilePatchInfo
from pr_agent.algo.file_filter import filter_ignored
from pr_agent.algo.git_patch_processing import decode_if_bytes
from pr_agent.algo.language_handler import is_valid_file
from pr_agent.algo.utils import load_large_diff
from pr_agent.config_loader import get_settings
from pr_agent.log import get_logger
from .git_provider import GitProvider


class CommitRangePRMimic:
    """
    This class mimics the PR object for commit range comparison.
    """
    def __init__(self, title: str, description: str):
        self.title = title
        self.description = description


class CommitRangeGitLabProvider(GitProvider):
    """
    GitLab provider for comparing two commits using GitLab's compare API.
    """

    def __init__(self, project_path: str, from_commit: str, to_commit: str, straight: bool = False):
        """
        Initialize CommitRangeGitLabProvider.
        
        Args:
            project_path: GitLab project path (e.g., "group/project")
            from_commit: Source commit SHA or branch name
            to_commit: Target commit SHA or branch name
            straight: If True, use two-dot diff (..), if False, use three-dot diff (...)
        """
        gitlab_url = get_settings().get("GITLAB.URL", None)
        if not gitlab_url:
            raise ValueError("GitLab URL is not set in the config file")
        self.gitlab_url = gitlab_url
        
        gitlab_access_token = get_settings().get("GITLAB.PERSONAL_ACCESS_TOKEN", None)
        if not gitlab_access_token:
            raise ValueError("GitLab personal access token is not set in the config file")
        
        self.gl = gitlab.Gitlab(url=gitlab_url, oauth_token=gitlab_access_token)
        self.project_path = project_path
        self.from_commit = from_commit
        self.to_commit = to_commit
        self.straight = straight
        self.max_comment_chars = 65000
        
        self.id_project = project_path
        self.diff_files = None
        self.git_files = None
        self.project = None
        self.compare_result = None
        self.pr = None
        
        self._initialize_comparison()
        
        # Create PR mimic object
        self.pr = CommitRangePRMimic(self.get_pr_title(), self.get_pr_description())

    def _initialize_comparison(self):
        """Initialize the comparison using GitLab's compare API."""
        try:
            self.project = self.gl.projects.get(self.id_project)
            self.compare_result = self.project.repository_compare(
                from_=self.from_commit,
                to=self.to_commit,
                straight=self.straight
            )
            get_logger().debug(f"Initialized comparison between {self.from_commit} and {self.to_commit}")
        except GitlabGetError as e:
            get_logger().error(f"Failed to get project or compare commits: {e}")
            raise
        except Exception as e:
            get_logger().error(f"Unexpected error during comparison initialization: {e}")
            raise

    def is_supported(self, capability: str) -> bool:
        """Return whether a capability is supported."""
        if capability in ['get_issue_comments', 'create_inline_comment', 'publish_inline_comments',
                         'publish_file_comments', 'publish_description', 'publish_comment']:
            return False
        return True

    def get_diff_files(self) -> List[FilePatchInfo]:
        """Get the diff files from the comparison."""
        if self.diff_files:
            return self.diff_files

        if not self.compare_result:
            get_logger().error("No comparison result available")
            return []

        # Get diffs from compare result
        diffs_original = self.compare_result.get('diffs', [])
        diffs = filter_ignored(diffs_original, 'gitlab')
        
        if diffs != diffs_original:
            try:
                names_original = [diff['new_path'] for diff in diffs_original]
                names_filtered = [diff['new_path'] for diff in diffs]
                get_logger().info(f"Filtered out [ignore] files for commit comparison", extra={
                    'original_files': names_original,
                    'filtered_files': names_filtered
                })
            except Exception:
                pass

        diff_files = []
        invalid_files_names = []
        
        for diff in diffs:
            if not is_valid_file(diff['new_path']):
                invalid_files_names.append(diff['new_path'])
                continue

            # Get file contents for old and new versions
            original_file_content_str = self._get_file_content(diff['old_path'], self.from_commit)
            new_file_content_str = self._get_file_content(diff['new_path'], self.to_commit)

            # Ensure content is properly decoded
            original_file_content_str = decode_if_bytes(original_file_content_str)
            new_file_content_str = decode_if_bytes(new_file_content_str)

            # Determine edit type
            edit_type = EDIT_TYPE.MODIFIED
            if diff['new_file']:
                edit_type = EDIT_TYPE.ADDED
            elif diff['deleted_file']:
                edit_type = EDIT_TYPE.DELETED
            elif diff['renamed_file']:
                edit_type = EDIT_TYPE.RENAMED

            filename = diff['new_path']
            patch = diff['diff']
            if not patch:
                patch = load_large_diff(filename, new_file_content_str, original_file_content_str)

            # Count number of lines added and removed
            patch_lines = patch.splitlines(keepends=True)
            num_plus_lines = len([line for line in patch_lines if line.startswith('+')])
            num_minus_lines = len([line for line in patch_lines if line.startswith('-')])
            
            diff_files.append(
                FilePatchInfo(
                    original_file_content_str, 
                    new_file_content_str,
                    patch=patch,
                    filename=filename,
                    edit_type=edit_type,
                    old_filename=None if diff['old_path'] == diff['new_path'] else diff['old_path'],
                    num_plus_lines=num_plus_lines,
                    num_minus_lines=num_minus_lines
                )
            )
        
        if invalid_files_names:
            get_logger().info(f"Filtered out files with invalid extensions: {invalid_files_names}")

        self.diff_files = diff_files
        return diff_files

    def _get_file_content(self, file_path: str, commit_sha: str) -> str:
        """Get file content at a specific commit."""
        try:
            if not file_path:
                return ""
            file_info = self.project.files.get(file_path, commit_sha)
            return file_info.decode().decode('utf-8')
        except GitlabGetError:
            # File doesn't exist at this commit (new or deleted file)
            return ""
        except Exception as e:
            get_logger().warning(f"Failed to get file content for {file_path} at {commit_sha}: {e}")
            return ""

    def get_files(self) -> List[str]:
        """Get the list of files changed in the comparison."""
        if not self.git_files:
            if not self.compare_result:
                return []
            self.git_files = [diff['new_path'] for diff in self.compare_result.get('diffs', [])]
        return self.git_files

    def get_pr_title(self) -> str:
        """Generate a title for the commit range comparison."""
        try:
            commits = self.compare_result.get('commits', [])
            if len(commits) == 1:
                return f"单个提交: {commits[0].get('title', '')}"
            elif len(commits) > 1:
                return f"代码变更汇总: {len(commits)} 个提交 ({self.from_commit[:7]}...{self.to_commit[:7]})"
            else:
                return f"代码变更对比: {self.from_commit[:7]}...{self.to_commit[:7]}"
        except Exception:
            return f"代码变更对比: {self.from_commit[:7]}...{self.to_commit[:7]}"

    def get_pr_description(self, full: bool = True) -> str:
        """Generate a description for the commit range comparison."""
        try:
            commits = self.compare_result.get('commits', [])
            if not commits:
                return f"对比提交 {self.from_commit} 和 {self.to_commit} 之间的代码变更"
            
            description = f"**提交范围对比**: {self.from_commit[:7]}...{self.to_commit[:7]}\n\n"
            description += f"**包含的提交 ({len(commits)} 个):**\n"
            
            for commit in commits:
                short_id = commit.get('short_id', commit.get('id', '')[:8])
                title = commit.get('title', '无标题')
                author = commit.get('author_name', '未知作者')
                created_at = commit.get('created_at', '')
                if created_at:
                    # 简化日期格式
                    date_part = created_at.split('T')[0] if 'T' in created_at else created_at
                    description += f"- `{short_id}` {title} - {author} ({date_part})\n"
                else:
                    description += f"- `{short_id}` {title} - {author}\n"
            
            return description
        except Exception as e:
            get_logger().error(f"Error generating PR description: {e}")
            return f"对比提交 {self.from_commit} 和 {self.to_commit} 之间的代码变更"

    def get_pr_branch(self) -> str:
        """Return the target commit as branch name."""
        return self.to_commit

    def get_user_id(self) -> str:
        """Return empty string as user ID is not applicable for commit comparison."""
        return ""

    def get_pr_url(self) -> str:
        """Return a URL for the comparison."""
        return f"{self.gitlab_url}/{self.project_path}/-/compare/{self.from_commit}...{self.to_commit}"

    def get_pr_id(self) -> str:
        """Return a synthetic PR ID for the comparison."""
        # Use the same dot notation as provided (.. or ...)
        dots = ".." if self.straight else "..."
        return f"{self.from_commit[:8]}{dots}{self.to_commit[:8]}"

    def get_latest_commit_url(self) -> str:
        """Return URL to the latest commit in the comparison."""
        try:
            return f"{self.gitlab_url}/{self.project_path}/-/commit/{self.to_commit}"
        except Exception:
            return ""

    # Unsupported operations for commit comparison
    def publish_description(self, pr_title: str, pr_body: str):
        """Not supported for commit comparison."""
        get_logger().warning("publish_description not supported for commit comparison")

    def publish_comment(self, comment: str, is_temporary: bool = False):
        """Not supported for commit comparison."""
        get_logger().warning("publish_comment not supported for commit comparison")
        return None

    def edit_comment(self, comment, body: str):
        """Not supported for commit comparison."""
        get_logger().warning("edit_comment not supported for commit comparison")

    def create_inline_comment(self, body: str, relevant_file: str, relevant_line_in_file: str):
        """Not supported for commit comparison."""
        get_logger().warning("create_inline_comment not supported for commit comparison")

    def publish_inline_comments(self, comments: List):
        """Not supported for commit comparison."""
        get_logger().warning("publish_inline_comments not supported for commit comparison")

    def get_issue_comments(self):
        """Not supported for commit comparison."""
        return []

    def publish_persistent_comment(self, pr_comment: str, initial_header: str, update_header: bool = True,
                                  name: str = 'review', final_update_message: bool = True):
        """Not supported for commit comparison."""
        get_logger().warning("publish_persistent_comment not supported for commit comparison")

    def remove_initial_comment(self, comment):
        """Not supported for commit comparison."""
        get_logger().warning("remove_initial_comment not supported for commit comparison")

    def get_languages(self) -> dict:
        """Return languages from the project."""
        try:
            return self.project.languages()
        except Exception:
            return {}

    def get_pr_labels(self) -> List[str]:
        """Return empty list as labels are not applicable for commit comparison."""
        return []

    def get_pr_body(self) -> str:
        """Return the generated description."""
        return self.get_pr_description()

    # Additional abstract methods implementation
    def add_eyes_reaction(self, comment):
        """Not supported for commit comparison."""
        get_logger().warning("add_eyes_reaction not supported for commit comparison")
        return False

    def get_commit_messages(self):
        """Return commit messages from the comparison."""
        try:
            commits = self.compare_result.get('commits', [])
            if not commits:
                return []
            
            messages = []
            for commit in commits:
                short_id = commit.get('short_id', commit.get('id', '')[:8])
                title = commit.get('title', '无标题')
                author = commit.get('author_name', '未知作者')
                created_at = commit.get('created_at', '')
                
                if created_at:
                    date_part = created_at.split('T')[0] if 'T' in created_at else created_at
                    messages.append(f"{short_id}: {title} - {author} ({date_part})")
                else:
                    messages.append(f"{short_id}: {title} - {author}")
            
            return messages
        except Exception as e:
            get_logger().error(f"Error getting commit messages: {e}")
            return []

    def get_pr_description_full(self):
        """Return full PR description."""
        return self.get_pr_description()

    def get_repo_settings(self):
        """Return empty repo settings."""
        return {}

    def publish_code_suggestions(self, code_suggestions):
        """Not supported for commit comparison."""
        get_logger().warning("publish_code_suggestions not supported for commit comparison")
        return False

    def publish_inline_comment(self, comment):
        """Not supported for commit comparison."""
        get_logger().warning("publish_inline_comment not supported for commit comparison")
        return False

    def publish_labels(self, labels):
        """Not supported for commit comparison."""
        get_logger().warning("publish_labels not supported for commit comparison")
        return False

    def remove_comment(self, comment):
        """Not supported for commit comparison."""
        get_logger().warning("remove_comment not supported for commit comparison")
        return False

    def remove_reaction(self, comment, reaction):
        """Not supported for commit comparison."""
        get_logger().warning("remove_reaction not supported for commit comparison")
        return False