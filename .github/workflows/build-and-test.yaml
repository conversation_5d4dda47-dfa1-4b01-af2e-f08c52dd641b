name: Build-and-test

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    steps:
      - id: checkout
        uses: actions/checkout@v4

      - id: dockerx
        name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - id: build
        name: Build dev docker
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./docker/Dockerfile
          push: false
          load: true
          tags: codiumai/pr-agent:test
          cache-from: type=gha,scope=dev
          cache-to: type=gha,mode=max,scope=dev
          target: test

      - id: test
        name: Test dev docker
        run: |
          docker run --rm codiumai/pr-agent:test pytest -v tests/unittest
