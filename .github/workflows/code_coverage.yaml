name: Code-coverage

on:
  workflow_dispatch:
  # push:
  #   branches:
  #     - main
  pull_request:
    branches:
      - main

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    steps:
      - id: checkout
        uses: actions/checkout@v4

      - id: dockerx
        name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3

      - id: build
        name: Build dev docker
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./docker/Dockerfile
          push: false
          load: true
          tags: codiumai/pr-agent:test
          cache-from: type=gha,scope=dev
          cache-to: type=gha,mode=max,scope=dev
          target: test

      - id: code_cov
        name: Test dev docker
        run: |
          docker run --name test_container codiumai/pr-agent:test  pytest  tests/unittest --cov=pr_agent --cov-report term --cov-report xml:coverage.xml
          docker cp test_container:/app/coverage.xml coverage.xml
          docker rm test_container

      - name: Validate coverage report
        run: |
          if [ ! -f coverage.xml ]; then
            echo "Coverage report not found"
            exit 1
          fi
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
