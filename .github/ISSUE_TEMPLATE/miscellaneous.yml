name: "❔ General Issue"
description: Submit a general issue
labels: ["general"]
body:

  - type: dropdown
    id: information-git-provider
    attributes:
      label: Git provider (optional)
      description: 'Git Provider:'
      options:
        - "Github Cloud"
        - "Github Enterprise"
        - "Gitlab"
        - "Bitbucket Cloud"
        - "Bitbucket Server"
        - "Azure"
        - "Other"

  - type: textarea
    id: system-info
    attributes:
      label: System Info (optional)
      description: Please share your system info with us.
      placeholder: model used, deployment type (action/app/cli/...), etc...
    validations:
      required: false

  - type: textarea
    id: issues-details
    attributes:
      label: Issues details
      description: Please share the issues details.
      placeholder: Describe the issue
    validations:
      required: true
