# MR 报告生成器

现代化的 TypeScript + React 驱动的 MR 变更报告生成器，支持美观的 diff 展示和交互式界面。

## 特性

- 🎨 **现代化 UI**: 使用 React + Tailwind CSS，提供美观的用户界面
- 🔍 **增强的 diff 显示**: 支持语法高亮和 GitHub/GitLab 风格的差异展示
- 📱 **响应式设计**: 适配桌面和移动设备
- 🌗 **多主题支持**: 支持 GitHub 和 GitLab 风格主题
- 🚀 **快速构建**: 使用 Vite 和 Bun 进行快速开发和构建
- 📊 **图表支持**: 支持 Mermaid 流程图显示
- 🔧 **TypeScript**: 完整的类型安全支持

## 安装

```bash
cd mr-report-generator
bun install
```

## 使用方法

### 命令行使用

```bash
# 基本用法
bun run generate --markdown mr_description.md --diff diff.json --output report.html

# 指定主题
bun run generate --markdown mr_description.md --diff diff.json --output report.html --theme gitlab

# 使用紧凑模板
bun run generate --markdown mr_description.md --diff diff.json --output report.html --template compact
```

### 参数说明

- `--markdown <file>`: MR 描述的 Markdown 文件路径
- `--diff <file>`: Diff 数据的 JSON 文件路径
- `--output <file>`: 输出 HTML 文件路径
- `--template <type>`: 模板类型 (default|compact) [默认: default]
- `--theme <theme>`: 主题 (github|gitlab) [默认: github]

### 配合 pr-agent 使用

```bash
# 1. 生成 MR 描述
pr-agent --pr_url=https://gitlab.example.com/project/-/merge_requests/123 describe --config.publish_output=false --config.verbosity_level=0 --config.response_language="zh-CN" 2>/dev/null > mr_description.md

# 2. 获取 diff 数据 (需要你的 GitLab API 或其他方式获取 diff JSON)
# 参考 ai_context/diff_1921.json 的格式

# 3. 生成报告
cd mr-report-generator
bun run generate --markdown ../mr_description.md --diff ../diff.json --output mr_report.html
```

## 开发

### 项目结构

```
mr-report-generator/
├── src/
│   ├── components/          # React 组件
│   │   ├── DiffViewer.tsx  # 差异展示组件
│   │   └── MRReport.tsx    # 主报告组件
│   ├── types/              # TypeScript 类型定义
│   │   └── index.ts
│   ├── utils/              # 工具函数
│   │   ├── markdown-parser.ts
│   │   └── diff-processor.ts
│   ├── cli.ts              # 命令行界面
│   └── generator.ts        # HTML 生成器
├── package.json
├── tsconfig.json
├── tailwind.config.js
└── vite.config.ts
```

### 开发命令

```bash
# 安装依赖
bun install

# 开发模式
bun run dev

# 构建
bun run build

# 类型检查
bun run type-check

# 代码检查
bun run lint
```

## 输出示例

生成的 HTML 报告包含以下部分：

1. **描述**: 显示 MR 的详细描述
2. **流程图**: 展示 Mermaid 图表（如果有）
3. **变更概览**: 按类别分组的文件变更摘要
4. **代码差异**: 详细的代码差异，支持语法高亮和折叠

## 功能特性

### ✅ 完整功能实现

- **现代化架构**: TypeScript + React SSR + Tailwind CSS
- **完整路径显示**: 变更概览显示完整文件路径（如 `app/bbs-feed/index.tsx`）
- **平滑跳转**: 点击变更概览中的文件路径可平滑跳转到对应的代码差异
- **智能展开**: 跳转时自动展开折叠的代码差异部分
- **高亮效果**: 跳转后目标区域会短暂高亮显示
- **GitHub 风格 diff**: 支持添加/删除行的背景颜色高亮
- **响应式设计**: 适配不同屏幕尺寸
- **类型安全**: 完整的 TypeScript 类型支持

### 🔧 交互功能

- **可折叠区域**: 变更概览和代码差异都支持展开/折叠
- **平滑滚动**: 页面内跳转使用平滑滚动效果
- **语法高亮**: 支持 TypeScript/JavaScript 语法高亮
- **Mermaid 图表**: 支持流程图渲染
- **浏览器控制台**: 提供调试信息，便于排查问题

## 与原版本的改进

相比原来的 JavaScript 版本，新版本提供了：

- ✅ **React SSR**: 使用 React 静态渲染，无需运行时服务端
- ✅ **完整路径显示**: 学习 Python 版本的实现，显示完整文件路径
- ✅ **可靠的跳转功能**: 点击文件路径能准确跳转到对应代码差异
- ✅ **更好的 diff 显示**: 支持行号、语法高亮、背景颜色
- ✅ **增强的交互**: 智能展开、高亮提示、平滑滚动
- ✅ **更好的构建系统**: Vite + Bun 快速构建
- ✅ **多主题支持**: GitHub/GitLab 风格切换

## 贡献

欢迎提交 Issues 和 Pull Requests！

## 许可证

MIT License