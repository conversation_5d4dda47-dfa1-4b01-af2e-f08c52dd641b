import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { viteSingleFile } from 'vite-plugin-singlefile';
import { viteMrDataPlugin } from './vite-plugin-mr-data';

// 环境变量检查函数
function validateEnvironmentVariables() {
  const requiredEnvVars = ['MARKDOWN_FILE', 'DIFF_FILE'];
  const missingVars: string[] = [];

  for (const varName of requiredEnvVars) {
    if (!process.env[varName]) {
      missingVars.push(varName);
    }
  }

  if (missingVars.length > 0) {
    console.error('\n❌ 缺少必需的环境变量:');
    missingVars.forEach((varName) => {
      console.error(`   - ${varName}`);
    });
    console.error('\n请使用以下方式运行构建:');
    console.error(
      'MARKDOWN_FILE=./mr_description.md DIFF_FILE=./diff.json npm run build'
    );
    console.error('\n或者使用快捷脚本:');
    console.error('npm run generate -- ./mr_description.md ./diff.json\n');

    throw new Error('环境变量验证失败');
  }

  console.log('✅ 环境变量验证通过:');
  console.log(`   MARKDOWN_FILE: ${process.env.MARKDOWN_FILE}`);
  console.log(`   DIFF_FILE: ${process.env.DIFF_FILE}`);
}

export default defineConfig(({ command }) => {
  validateEnvironmentVariables();

  return {
    plugins: [
      react(),
      viteMrDataPlugin(),
      viteSingleFile({
        removeViteModuleLoader: true,
        useRecommendedBuildConfig: true,
      }),
    ],
    build: {
      // 确保所有资源都被内联
      assetsInlineLimit: Number.MAX_SAFE_INTEGER,
      rollupOptions: {
        // 如果需要，可以在这里添加额外的 Rollup 配置
      },
    },
    define: {
      'process.env.NODE_ENV': JSON.stringify(
        process.env.NODE_ENV || 'development'
      ),
    },
  };
});
