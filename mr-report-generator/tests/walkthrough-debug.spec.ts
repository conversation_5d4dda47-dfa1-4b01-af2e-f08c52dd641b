import { test, expect } from '@playwright/test';

test.describe('变更概览调试', () => {
  test('检查变更概览部分的内容和数据', async ({ page }) => {
    // 首先确保构建完成
    const distPath = 'file://' + process.cwd() + '/dist/index.html';
    console.log('访问路径:', distPath);
    
    // 导航到生成的 HTML 文件
    await page.goto(distPath);
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
    
    // 检查变更概览部分是否存在
    const walkthroughSection = page.getByTestId('walkthrough-section');
    await expect(walkthroughSection).toBeVisible();
    
    // 检查标题
    const walkthroughTitle = page.getByTestId('walkthrough-title');
    await expect(walkthroughTitle).toContainText('变更概览');
    
    // 检查内容区域
    const walkthroughContent = page.getByTestId('walkthrough-content');
    await expect(walkthroughContent).toBeVisible();
    
    // 调试：输出变更概览的完整内容
    const contentText = await walkthroughContent.textContent();
    console.log('=== 变更概览完整内容 ===');
    console.log(contentText);
    console.log('=== 内容长度 ===', contentText?.length);
    
    // 调试：输出 HTML 结构
    const contentHTML = await walkthroughContent.innerHTML();
    console.log('=== 变更概览 HTML 结构 ===');
    console.log(contentHTML.substring(0, 1000)); // 只输出前1000字符避免过长
    
    // 检查是否有分类
    const categories = walkthroughContent.locator('.border.border-gray-200.rounded-lg');
    const categoryCount = await categories.count();
    console.log('=== 分类数量 ===', categoryCount);
    
    // 输出每个分类的信息
    for (let i = 0; i < categoryCount; i++) {
      const category = categories.nth(i);
      const categoryName = await category.locator('h4').textContent();
      const fileCount = await category.locator('.text-sm.text-gray-500').textContent();
      console.log(`分类 ${i + 1}: ${categoryName} - ${fileCount}`);
      
      // 检查是否可以点击展开
      const header = category.locator('.bg-gray-50');
      await header.click();
      
      // 等待展开动画
      await page.waitForTimeout(300);
      
      // 获取文件列表
      const files = category.locator('.flex.items-start.space-x-3');
      const filesCount = await files.count();
      console.log(`  文件数量: ${filesCount}`);
      
      // 输出前几个文件信息
      for (let j = 0; j < Math.min(3, filesCount); j++) {
        const file = files.nth(j);
        const fileName = await file.locator('button').textContent();
        const fileDesc = await file.locator('code').textContent();
        console.log(`  文件 ${j + 1}: ${fileName} - ${fileDesc}`);
      }
    }
    
    // 检查是否有降级到基础分类的情况
    if (categoryCount === 0) {
      console.log('=== 警告：没有找到任何分类，可能出现了问题 ===');
      
      // 检查是否有错误信息
      const errorElements = page.locator('.text-red-500, .error, [data-error]');
      const errorCount = await errorElements.count();
      if (errorCount > 0) {
        for (let i = 0; i < errorCount; i++) {
          const error = await errorElements.nth(i).textContent();
          console.log(`错误 ${i + 1}: ${error}`);
        }
      }
      
      // 检查控制台日志
      page.on('console', msg => {
        console.log(`浏览器控制台 [${msg.type()}]: ${msg.text()}`);
      });
    }
    
    // 截图保存调试信息
    await page.screenshot({ 
      path: 'tests/debug-walkthrough-full.png', 
      fullPage: true 
    });
    
    // 只截取变更概览部分
    await walkthroughSection.screenshot({ 
      path: 'tests/debug-walkthrough-section.png' 
    });
  });
  
  test('检查数据源和解析', async ({ page }) => {
    // 注入脚本检查内部数据
    const distPath = 'file://' + process.cwd() + '/dist/index.html';
    await page.goto(distPath);
    await page.waitForLoadState('networkidle');
    
    const debugInfo = await page.evaluate(() => {
      // 尝试获取应用的内部状态
      const appElement = document.querySelector('[data-testid="walkthrough-content"]');
      
      return {
        hasWalkthroughContent: !!appElement,
        walkthroughHTML: appElement?.innerHTML?.substring(0, 500),
        reactFiberInfo: appElement?.hasOwnProperty('_reactInternalFiber') || 
                       appElement?.hasOwnProperty('__reactInternalInstance'),
        documentTitle: document.title,
        headElements: Array.from(document.head.children).map(el => el.tagName).slice(0, 10)
      };
    });
    
    console.log('=== 调试信息 ===');
    console.log(JSON.stringify(debugInfo, null, 2));
  });
});