import { test, expect } from '@playwright/test';
import { execSync } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

test.describe('MR 报告 - 描述部分调试', () => {
  test.beforeEach(async ({ page }) => {
    // 先构建最新的 HTML 文件，使用测试 fixtures
    console.log('构建最新的 HTML 文件...');
    execSync('MARKDOWN_FILE=./fixtures/mr_description_summary_1921.md DIFF_FILE=./fixtures/diff_1921.json bun run build', { 
      cwd: path.join(__dirname, '..'),
      stdio: 'inherit'
    });
    
    // 加载生成的 HTML 文件
    const htmlPath = path.join(__dirname, '..', 'dist', 'index.html');
    await page.goto(`file://${htmlPath}`);
    
    // 等待页面加载完成
    await page.waitForLoadState('domcontentloaded');
    await page.waitForTimeout(500); // 等待 React 渲染完成
  });

  test('应该正确渲染描述部分', async ({ page }) => {
    // 检查描述部分是否存在
    const descriptionSection = page.locator('[data-testid="description-section"]');
    await expect(descriptionSection).toBeVisible();
    
    // 检查描述标题
    const descriptionTitle = page.locator('[data-testid="description-title"]');
    await expect(descriptionTitle).toBeVisible();
    await expect(descriptionTitle).toContainText('描述');
    
    // 检查描述内容
    const descriptionContent = page.locator('[data-testid="description-content"]');
    await expect(descriptionContent).toBeVisible();
    
    // 输出描述内容的 HTML 结构用于调试
    const htmlContent = await descriptionContent.innerHTML();
    console.log('=== 描述内容 HTML 结构 ===');
    console.log(htmlContent);
    
    // 检查是否有 Markdown 渲染的迹象
    const hasMarkdownElements = await page.evaluate(() => {
      const content = document.querySelector('[data-testid="description-content"]');
      if (!content) return false;
      
      // 检查是否有常见的 Markdown 渲染元素
      const hasParagraphs = content.querySelectorAll('p').length > 0;
      const hasHeaders = content.querySelectorAll('h1, h2, h3, h4, h5, h6').length > 0;
      const hasLists = content.querySelectorAll('ul, ol').length > 0;
      const hasCodeBlocks = content.querySelectorAll('pre, code').length > 0;
      const hasLinks = content.querySelectorAll('a').length > 0;
      const hasStrong = content.querySelectorAll('strong, b').length > 0;
      const hasEmphasis = content.querySelectorAll('em, i').length > 0;
      
      return {
        hasParagraphs,
        hasHeaders,
        hasLists,
        hasCodeBlocks,
        hasLinks,
        hasStrong,
        hasEmphasis,
        totalElements: content.children.length,
        textContent: content.textContent?.slice(0, 200) + '...',
        innerHTML: content.innerHTML.slice(0, 500) + '...'
      };
    });
    
    console.log('=== Markdown 渲染检查结果 ===');
    console.log(JSON.stringify(hasMarkdownElements, null, 2));
    
    // 检查是否是原始 Markdown 文本（未渲染）
    const rawText = await descriptionContent.textContent();
    console.log('=== 原始文本内容 ===');
    console.log(rawText?.slice(0, 500) + '...');
    
    // 检查是否包含 Markdown 语法标记（表示未正确渲染）
    const hasMarkdownSyntax = rawText?.includes('##') || 
                              rawText?.includes('**') || 
                              rawText?.includes('```') ||
                              rawText?.includes('- ') ||
                              rawText?.includes('* ') ||
                              rawText?.includes('[') && rawText?.includes('](');
    
    console.log('=== Markdown 语法检查 ===');
    console.log('包含未渲染的 Markdown 语法:', hasMarkdownSyntax);
    
    // 获取页面控制台错误
    const consoleMessages: string[] = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleMessages.push(msg.text());
      }
    });
    
    // 等待一下看是否有控制台错误
    await page.waitForTimeout(1000);
    
    if (consoleMessages.length > 0) {
      console.log('=== 控制台错误 ===');
      consoleMessages.forEach(msg => console.log(msg));
    }
    
    // 截图保存调试信息
    await page.screenshot({ 
      path: path.join(__dirname, 'debug-description-full.png'),
      fullPage: true 
    });
    
    await descriptionSection.screenshot({ 
      path: path.join(__dirname, 'debug-description-section.png') 
    });
    
    console.log('描述部分调试截图已保存');
  });

  test('检查 Markdown 解析函数', async ({ page }) => {
    // 检查 markdownToHtml 函数是否正常工作
    const markdownTestResult = await page.evaluate(() => {
      // 尝试获取 markdownToHtml 函数
      const testMarkdown = '## 测试标题\n\n这是一个 **粗体** 文本。\n\n- 列表项1\n- 列表项2\n\n```javascript\nconst test = "代码块";\n```';
      
      // 查看 window 对象上是否有相关函数
      const windowKeys = Object.keys(window).filter(key => key.includes('markdown') || key.includes('html'));
      
      return {
        testMarkdown,
        windowKeys,
        hasMarkdownToHtml: typeof (window as any).markdownToHtml === 'function'
      };
    });
    
    console.log('=== Markdown 解析函数检查 ===');
    console.log(JSON.stringify(markdownTestResult, null, 2));
  });
});