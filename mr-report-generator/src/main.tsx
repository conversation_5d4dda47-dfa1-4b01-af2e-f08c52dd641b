import React from 'react'
import ReactDOM from 'react-dom/client'
import './index.css'
import { MRReport } from './components/MRReport'
import { parseMarkdown } from './utils/markdown-parser'
import { processDiffData } from './utils/diff-processor'
import { markdownContent, diffData } from 'virtual:mr-data'

// 解析数据
const mrData = parseMarkdown(markdownContent)
const processedDiffs = processDiffData(diffData)

// 初始化 Mermaid 和 Highlight.js
document.addEventListener('DOMContentLoaded', function() {
  console.log('🚀 初始化 MR 报告...')
  
  // 初始化 Mermaid
  if (typeof window !== 'undefined' && (window as any).mermaid) {
    (window as any).mermaid.initialize({ startOnLoad: true })
  }
  
  // 初始化 Highlight.js
  if (typeof window !== 'undefined' && (window as any).hljs) {
    (window as any).hljs.highlightAll()
  }
  
  console.log('✅ MR 报告初始化完成')
})

// 渲染应用
const root = ReactDOM.createRoot(document.getElementById('root')!)
root.render(
  <React.StrictMode>
    <MRReport mrData={mrData} diffs={processedDiffs} />
  </React.StrictMode>
) 