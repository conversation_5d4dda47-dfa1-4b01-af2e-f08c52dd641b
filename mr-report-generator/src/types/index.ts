export interface MRData {
  prType: string;
  description: string;
  mermaidDiagram: string;
  changesWalkthrough: ChangesWalkthrough;
  walkthroughHtml: string;
}

export interface ChangesWalkthrough {
  Enhancement: FileChange[];
  Refactoring: FileChange[];
  'Configuration changes': FileChange[];
  'Additional files': FileChange[];
  [key: string]: FileChange[]; // 支持动态分类
}

export interface FileChange {
  filePath: string;
  description: string;
}

export interface DiffData {
  oldPath?: string;
  newPath?: string;
  diff: string;
  newFile?: boolean;
  deletedFile?: boolean;
  renamedFile?: boolean;
}

export interface ProcessedDiff {
  filePath: string;
  diffContent: string;
  isBinary: boolean;
  isNewFile: boolean;
  isDeletedFile: boolean;
  isRenamedFile: boolean;
  fileId: string;
  fileType: string;
}

export interface DiffLine {
  type: 'added' | 'removed' | 'context' | 'header';
  content: string;
  oldLineNumber?: number;
  newLineNumber?: number;
}

export interface GenerateOptions {
  markdownFile: string;
  diffFile: string;
  outputFile: string;
  template?: 'default' | 'compact';
  theme?: 'github' | 'gitlab';
}