import { useState, useEffect, useRef } from 'react';
import { DiffViewer, DiffViewerRef } from './DiffViewer';
import { markdownToHtml } from '../utils/markdown-parser';
import type { MRData, ProcessedDiff } from '../types';
import clsx from 'clsx';

interface MRReportProps {
  mrData: MRData;
  diffs: ProcessedDiff[];
  className?: string;
}

export const MRReport: React.FC<MRReportProps> = ({ mrData, diffs, className }) => {
  // 使用 React 状态管理变更概览部分的折叠/展开
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  
  // 为每个DiffViewer创建ref
  const diffViewerRefs = useRef<Record<string, DiffViewerRef | null>>({});

  // 初始化 Mermaid
  useEffect(() => {
    // 等待 DOM 加载完成
    const initMermaid = async () => {
      // 检查 Mermaid 是否已经加载
      if (typeof window !== 'undefined' && (window as any).mermaid) {
        const mermaid = (window as any).mermaid;
        
        // 配置 Mermaid
        mermaid.initialize({
          startOnLoad: false,
          theme: 'neutral',
          securityLevel: 'loose',
          fontFamily: 'ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif'
        });

        // 渲染所有 Mermaid 图表
        const mermaidElements = document.querySelectorAll('.mermaid');
        mermaidElements.forEach(async (element, index) => {
          if (element.textContent) {
            try {
              const { svg } = await mermaid.render(`mermaid-${index}`, element.textContent);
              element.innerHTML = svg;
            } catch (error) {
              console.error('Mermaid 渲染错误:', error);
              element.innerHTML = `<div class="text-red-500 p-4 border border-red-300 rounded bg-red-50">
                <p><strong>图表渲染失败</strong></p>
                <pre class="text-xs mt-2">${element.textContent}</pre>
              </div>`;
            }
          }
        });
      } else {
        console.warn('Mermaid 库未加载');
      }
    };

    // 延迟执行以确保 DOM 渲染完成
    const timer = setTimeout(initMermaid, 100);
    return () => clearTimeout(timer);
  }, [mrData.mermaidDiagram]); // 当图表数据变化时重新初始化

  const toggleCategory = (categoryId: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));
  };

  const scrollToFile = (fileId: string) => {
    // 先展开对应的DiffViewer
    const diffViewerRef = diffViewerRefs.current[fileId];
    if (diffViewerRef) {
      diffViewerRef.expand();
    }

    // 利用浏览器原生hash滚动（CSS :target会自动高亮）
    window.location.hash = fileId;
  };

  // 文件路径验证：确保AI描述的文件在真实diff中存在
  const validateFilePath = (aiFilePath: string): ProcessedDiff | null => {
    // 尝试精确匹配
    let matchedDiff = diffs.find(diff => diff.filePath === aiFilePath);
    
    if (!matchedDiff) {
      // 尝试文件名匹配（去除路径）
      const fileName = aiFilePath.split('/').pop() || aiFilePath;
      matchedDiff = diffs.find(diff => diff.filePath.endsWith(fileName));
    }
    
    if (!matchedDiff) {
      // 尝试部分路径匹配
      matchedDiff = diffs.find(diff => 
        diff.filePath.includes(aiFilePath) || aiFilePath.includes(diff.filePath)
      );
    }
    
    return matchedDiff || null;
  };

  // 计算统计数据
  const stats = {
    totalFiles: diffs.length,
    totalLines: diffs.reduce((acc, diff) => acc + diff.diffContent.split('\n').length, 0),
    addedLines: diffs.reduce((acc, diff) => {
      const lines = diff.diffContent.split('\n');
      return acc + lines.filter(line => line.startsWith('+')).length;
    }, 0),
    deletedLines: diffs.reduce((acc, diff) => {
      const lines = diff.diffContent.split('\n');
      return acc + lines.filter(line => line.startsWith('-')).length;
    }, 0)
  };

  const renderWalkthroughSection = () => {
    // 使用AI分类和描述，但验证文件是否真实存在
    const walkthrough = mrData.changesWalkthrough;
    const validatedCategories: Array<{
      name: string;
      files: Array<{ diff: ProcessedDiff; description: string }>;
      id: string;
    }> = [];

    Object.entries(walkthrough).forEach(([categoryName, fileChanges]) => {
      // 排除 Additional files 分类
      if (categoryName === 'Additional files') {
        return;
      }

      const validatedFiles: Array<{ diff: ProcessedDiff; description: string }> = [];
      
      fileChanges.forEach(fileChange => {
        const matchedDiff = validateFilePath(fileChange.filePath);
        if (matchedDiff) {
          validatedFiles.push({
            diff: matchedDiff,
            description: fileChange.description
          });
        }
      });

      if (validatedFiles.length > 0) {
        validatedCategories.push({
          name: categoryName,
          files: validatedFiles,
          id: categoryName.toLowerCase().replace(/\s+/g, '-')
        });
      }
    });

    // 如果没有AI分类或验证失败，降级到基于diff的简单分类
    if (validatedCategories.length === 0) {
      const enhancementFiles = diffs.filter(diff => 
        diff.filePath.match(/\.(tsx|ts|js|jsx)$/)
      );
      
      const otherFiles = diffs.filter(diff => 
        !diff.filePath.match(/\.(tsx|ts|js|jsx)$/)
      );

      const fallbackCategories = [
        { name: 'Enhancement', files: enhancementFiles.map(diff => ({ diff, description: '文件变更' })), id: 'enhancement' },
        { name: 'Other files', files: otherFiles.map(diff => ({ diff, description: '文件变更' })), id: 'other' }
      ].filter(category => category.files.length > 0);

      return renderCategoryList(fallbackCategories);
    }

    return renderCategoryList(validatedCategories);
  };

  const renderCategoryList = (categories: Array<{
    name: string;
    files: Array<{ diff: ProcessedDiff; description: string }>;
    id: string;
  }>) => {
    return (
      <div className="space-y-6">
        {categories.map(category => (
          <div key={category.id} className="category-card">
            <div 
              className="category-header"
              onClick={() => toggleCategory(category.id)}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <h4 className="font-semibold text-gray-800 text-lg">{category.name}</h4>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-sm text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                    {category.files.length} 文件
                  </span>
                  <svg 
                    className={clsx(
                      'expand-icon w-5 h-5 text-gray-500',
                      { 'rotated': expandedCategories[category.id] }
                    )}
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
            </div>
            
            <div className={clsx(
              'diff-content',
              { 'expanded': expandedCategories[category.id], 'collapsed': !expandedCategories[category.id] }
            )}>
              <div className="p-6 space-y-4">
                {category.files.map((file, index) => (
                  <div key={index} className="file-item">
                    <div className="file-dot"></div>
                    <div className="flex-1 min-w-0">
                      <button
                        onClick={() => scrollToFile(file.diff.fileId)}
                        className="file-link font-mono text-sm font-medium text-left block w-full mb-2"
                      >
                        {file.diff.filePath}
                      </button>
                      <p className="text-sm text-gray-600">
                        <span className="bg-blue-50 text-blue-700 px-2 py-1 rounded-md text-xs border border-blue-200">
                          {file.description}
                        </span>
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className={clsx('min-h-screen', className)}>
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header with gradient background */}
        <div className="gradient-bg rounded-2xl p-8 mb-8 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-4xl font-bold mb-3">
                MR 变更报告
              </h1>
              <div className="flex items-center space-x-2 text-blue-100">
                <span className="bg-white/20 px-3 py-1 rounded-full text-sm font-medium">
                  {mrData.prType}
                </span>
                <span className="text-blue-100">•</span>
                <span className="text-sm">
                  📅 {new Date().toLocaleDateString('zh-CN')}
                </span>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="text-right">
                <div className="text-3xl font-bold">{stats.totalFiles}</div>
                <div className="text-blue-100 text-sm">文件变更</div>
              </div>
            </div>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="stat-card">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-gray-900">{stats.totalFiles}</div>
                <div className="text-sm text-gray-500">文件变更</div>
              </div>
              <div className="stat-icon">
                📁
              </div>
            </div>
          </div>
          
          <div className="stat-card">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-gray-900">{stats.totalLines}</div>
                <div className="text-sm text-gray-500">总行数</div>
              </div>
              <div className="stat-icon">
                📊
              </div>
            </div>
          </div>
          
          <div className="stat-card">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-green-600">+{stats.addedLines}</div>
                <div className="text-sm text-gray-500">新增行</div>
              </div>
              <div className="stat-icon" style={{background: 'linear-gradient(135deg, #059669 0%, #10b981 100%)'}}>
                ➕
              </div>
            </div>
          </div>
          
          <div className="stat-card">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-red-600">-{stats.deletedLines}</div>
                <div className="text-sm text-gray-500">删除行</div>
              </div>
              <div className="stat-icon" style={{background: 'linear-gradient(135deg, #dc2626 0%, #ef4444 100%)'}}>
                ➖
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="space-y-8">
          {/* Description Section */}
          <div className="modern-card p-8" data-testid="description-section">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6 flex items-center" data-testid="description-title">
              <span className="mr-3 text-2xl">📝</span>
              描述 (Description)
            </h2>
            <div 
              className="prose prose-lg max-w-none"
              data-testid="description-content"
              dangerouslySetInnerHTML={{ __html: markdownToHtml(mrData.description) }}
            />
          </div>

          {/* Diagram Section */}
          {mrData.mermaidDiagram && (
            <div className="modern-card p-8">
              <h2 className="text-2xl font-semibold text-gray-900 mb-6 flex items-center">
                <span className="mr-3 text-2xl">📊</span>
                变更图表 (Changes Diagram)
              </h2>
              <div className="mermaid bg-gradient-to-br from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-100">
                {mrData.mermaidDiagram}
              </div>
            </div>
          )}

          {/* Walkthrough Section */}
          <div className="modern-card p-8" data-testid="walkthrough-section">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6 flex items-center" data-testid="walkthrough-title">
              <span className="mr-3 text-2xl">🗂️</span>
              变更概览 (Changes Walkthrough)
            </h2>
            <div data-testid="walkthrough-content">
              {renderWalkthroughSection()}
            </div>
          </div>

          {/* Diffs Section */}
          <div className="modern-card p-8">
            <h2 className="text-2xl font-semibold text-gray-900 mb-6 flex items-center">
              <span className="mr-3 text-2xl">🔍</span>
              代码变更详情 (Code Diff Section)
            </h2>
            <div className="space-y-6">
              {diffs.map(diff => (
                <DiffViewer 
                  key={diff.fileId} 
                  ref={(ref) => {
                    diffViewerRefs.current[diff.fileId] = ref;
                  }}
                  diff={diff} 
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

