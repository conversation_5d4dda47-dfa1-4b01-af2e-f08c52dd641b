import { useState, useImperativeHandle, forwardRef, useEffect } from 'react';
import { parseDiffLines, escapeHtml } from '../utils/diff-processor';
import type { ProcessedDiff, DiffLine } from '../types';
import clsx from 'clsx';

interface DiffViewerProps {
  diff: ProcessedDiff;
  className?: string;
}

export interface DiffViewerRef {
  expand: () => void;
  collapse: () => void;
  toggle: () => void;
}

export const DiffViewer = forwardRef<DiffViewerRef, DiffViewerProps>(({ diff, className }, ref) => {
  // 默认折叠，支持多个同时展开
  const [isExpanded, setIsExpanded] = useState(false);
  const diffLines = parseDiffLines(diff.diffContent);

  // 暴露控制方法给父组件
  useImperativeHandle(ref, () => ({
    expand: () => {
      setIsExpanded(true);
      // 展开时更新hash
      window.location.hash = diff.fileId;
    },
    collapse: () => {
      setIsExpanded(false);
      // 收起时不做任何操作，保持当前URL
    },
    toggle: () => {
      const newState = !isExpanded;
      setIsExpanded(newState);
      // 只在展开时更新hash，收起时不操作
      if (newState) {
        window.location.hash = diff.fileId;
      }
    }
  }));

  // 页面加载时检查URL并自动展开滚动
  useEffect(() => {
    const hash = window.location.hash.slice(1);
    if (hash === diff.fileId) {
      setIsExpanded(true);
    }
  }, []); // 只在组件挂载时执行一次

  const handleToggle = () => {
    const newState = !isExpanded;
    setIsExpanded(newState);
    // 只在展开时更新hash，收起时不操作
    if (newState) {
      window.location.hash = diff.fileId;
    }
  };

  const getFileIcon = (fileType: string) => {
    const icons: Record<string, string> = {
      typescript: '🔵',
      javascript: '🟡',
      python: '🐍',
      java: '☕',
      css: '🎨',
      html: '🌐',
      json: '📄',
      markdown: '📝',
      yaml: '⚙️',
      bash: '💻'
    };
    return icons[fileType] || '📄';
  };

  const getStatusBadge = () => {
    if (diff.isNewFile) {
      return <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded">新增</span>;
    }
    if (diff.isDeletedFile) {
      return <span className="px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded">删除</span>;
    }
    if (diff.isRenamedFile) {
      return <span className="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">重命名</span>;
    }
    return <span className="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded">修改</span>;
  };

  const renderDiffLine = (line: DiffLine, index: number) => {
    const lineClasses = clsx(
      'font-mono text-sm leading-relaxed relative',
      {
        'bg-diff-added text-diff-added-text': line.type === 'added',
        'bg-diff-removed text-diff-removed-text': line.type === 'removed',
        'bg-diff-context text-gray-700': line.type === 'context',
        'bg-gray-50 text-gray-500 font-semibold': line.type === 'header'
      }
    );

    return (
      <div key={index} className={lineClasses}>
        {/* 使用伪元素创建左边指示线，不影响布局 */}
        {line.type === 'added' && (
          <div className="absolute left-0 top-0 bottom-0 w-1 bg-green-500"></div>
        )}
        {line.type === 'removed' && (
          <div className="absolute left-0 top-0 bottom-0 w-1 bg-red-500"></div>
        )}
        
        <div className="flex">
          {/* Line numbers - 改进布局，参考VSCode样式 */}
          <div className="flex-shrink-0 w-20 px-1 py-1 text-xs text-gray-400 bg-gray-50 border-r select-none">
            <div className="flex items-center justify-center space-x-1">
              <span className="w-8 text-right">{line.oldLineNumber || ''}</span>
              <span className="w-8 text-right">{line.newLineNumber || ''}</span>
            </div>
          </div>
          
          {/* Line content */}
          <div className="flex-1 px-3 py-1 overflow-x-auto">
            <pre 
              className="whitespace-pre-wrap break-all"
              dangerouslySetInnerHTML={{ __html: escapeHtml(line.content) }}
            />
          </div>
        </div>
      </div>
    );
  };

  if (diff.isBinary) {
    return (
      <div id={diff.fileId} className={clsx('diff-section border border-github-border rounded-lg overflow-hidden', className)}>
        <div className="bg-github-light-gray px-4 py-3 border-b border-github-border">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <span className="text-xl">{getFileIcon(diff.fileType)}</span>
              <span className="font-mono text-sm text-gray-700">{diff.filePath}</span>
              {getStatusBadge()}
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-500">二进制文件</span>
              <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
        </div>
        <div className="p-4 text-center text-gray-500">
          <span className="inline-block p-3 bg-gray-100 rounded-lg">
            📁 二进制文件，无法显示差异内容
          </span>
        </div>
      </div>
    );
  }

      return (
      <div id={diff.fileId} className={clsx('diff-section border border-github-border rounded-lg overflow-hidden', className)}>
        {/* File header */}
      <div 
        className="bg-github-light-gray px-4 py-3 border-b border-github-border cursor-pointer hover:bg-gray-100 transition-colors"
        onClick={handleToggle}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <span className="text-xl">{getFileIcon(diff.fileType)}</span>
            <span className="font-mono text-sm text-gray-700">{diff.filePath}</span>
            {getStatusBadge()}
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-500">
              {diffLines.filter(l => l.type === 'added').length} 添加, {diffLines.filter(l => l.type === 'removed').length} 删除
            </span>
            <svg 
              className={clsx(
                'expand-icon w-4 h-4 text-gray-500',
                { 'rotated': isExpanded }
              )}
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>
      </div>

      {/* Diff content */}
      <div className={clsx(
        'diff-content bg-white',
        { 'expanded': isExpanded, 'collapsed': !isExpanded }
      )}>
        <div className="max-h-96 overflow-y-auto">
          {diffLines.map((line, index) => renderDiffLine(line, index))}
        </div>
      </div>
    </div>
  );
});