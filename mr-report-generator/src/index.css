@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局样式优化 */
html {
  scroll-behavior: smooth;
}

body {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  min-height: 100vh;
}

/* 现代化卡片样式 */
.modern-card {
  @apply bg-white rounded-xl shadow-lg border border-gray-100;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.modern-card:hover {
  @apply shadow-xl;
  transform: translateY(-2px);
}

/* 渐变背景 */
.gradient-bg {
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%);
}

.gradient-bg-secondary {
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
}

/* 现代化按钮样式 */
.modern-button {
  @apply px-4 py-2 rounded-lg font-medium transition-all duration-300;
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.modern-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

/* 统计卡片样式 */
.stat-card {
  @apply bg-white rounded-xl p-6 shadow-lg border border-gray-100;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  transition: all 0.3s ease;
}

.stat-card:hover {
  @apply shadow-xl;
  transform: translateY(-3px);
}

.stat-icon {
  @apply w-12 h-12 rounded-full flex items-center justify-center text-white text-xl;
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

/* 原生target高亮效果 */
:target {
  border: 2px solid #3b82f6 !important;
  transition: all 0.3s ease;
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

/* Focus状态支持 */
.diff-section:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 键盘导航支持 */
.file-link:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 优化的展开收起动画 */
.diff-content {
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: max-height, opacity;
}

.diff-content.expanded {
  max-height: 2000px;
  opacity: 1;
}

.diff-content.collapsed {
  max-height: 0;
  opacity: 0;
}

/* 现代化旋转箭头动画 */
.expand-icon {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform;
}

.expand-icon.rotated {
  transform: rotate(180deg);
  color: #3b82f6;
}

/* 文件链接样式优化 */
.file-link {
  @apply text-blue-600 hover:text-blue-800 transition-all duration-200;
  position: relative;
}

.file-link:hover {
  text-decoration: none;
}

.file-link::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background: linear-gradient(90deg, #3b82f6, #1e40af);
  transition: width 0.3s ease;
}

.file-link:hover::after {
  width: 100%;
}

/* 分类卡片样式 */
.category-card {
  @apply border border-gray-200 rounded-xl overflow-hidden;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.category-card:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.category-header {
  @apply px-6 py-4 cursor-pointer transition-all duration-300;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.category-header:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
}

/* 文件项样式 */
.file-item {
  @apply flex items-start space-x-4 p-4 bg-white rounded-lg border border-gray-100;
  transition: all 0.3s ease;
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
}

.file-item:hover {
  @apply shadow-md;
  transform: translateX(4px);
  border-color: #3b82f6;
}

.file-dot {
  @apply w-3 h-3 rounded-full mt-2;
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

/* 加载动画 */
@keyframes pulse-glow {
  0%, 100% {
    opacity: 1;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  50% {
    opacity: 0.8;
    box-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
  }
}

.loading-pulse {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #1e40af 0%, #1e3a8a 100%);
}

/* Prose样式优化 */
.prose code {
  @apply bg-blue-50 text-blue-800 px-2 py-1 rounded-md text-sm font-mono border border-blue-200;
}

.prose strong {
  @apply font-semibold text-gray-900;
}

.prose em {
  @apply italic text-gray-700;
}

.prose ul {
  @apply list-disc ml-6 space-y-2;
}

.prose li {
  @apply text-gray-700 leading-relaxed;
}

.prose p {
  @apply text-gray-700 leading-relaxed mb-4;
}

.prose p:last-child {
  @apply mb-0;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .modern-card {
    @apply mx-2;
  }
  
  .stat-card {
    @apply p-4;
  }
  
  .category-header {
    @apply px-4 py-3;
  }
  
  .file-item {
    @apply p-3 space-x-3;
  }
} 