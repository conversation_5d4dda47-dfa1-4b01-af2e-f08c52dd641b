import type { DiffData, ProcessedDiff, DiffLine } from '../types';

export function processDiffData(diffData: DiffData[]): ProcessedDiff[] {
  const processed: ProcessedDiff[] = [];

  for (const item of diffData) {
    // Fix: use snake_case field names from the JSON
    const filePath = (item as any).new_path || (item as any).old_path || '';
    let diffContent = item.diff || '';

    // Handle binary files
    if (diffContent.startsWith('Binary files')) {
      diffContent = `Binary files differ: ${filePath}`;
    }

    processed.push({
      filePath,
      diffContent,
      isBinary: diffContent.startsWith('Binary files'),
      isNewFile: (item as any).new_file || false,
      isDeletedFile: (item as any).deleted_file || false,
      isRenamedFile: (item as any).renamed_file || false,
      fileId: generateFileId(filePath),
      fileType: getFileType(filePath)
    });
  }

  return processed;
}

export function generateFileId(filePath: string): string {
  return 'diff-' + filePath.replace(/[\/\.]/g, '-').replace(/[^a-zA-Z0-9-]/g, '');
}

export function getFileType(filePath: string): string {
  const extension = filePath.split('.').pop()?.toLowerCase() || '';
  
  const typeMap: Record<string, string> = {
    'ts': 'typescript',
    'tsx': 'typescript',
    'js': 'javascript',
    'jsx': 'javascript',
    'py': 'python',
    'java': 'java',
    'cpp': 'cpp',
    'c': 'c',
    'css': 'css',
    'scss': 'scss',
    'html': 'html',
    'json': 'json',
    'xml': 'xml',
    'yml': 'yaml',
    'yaml': 'yaml',
    'md': 'markdown',
    'sh': 'bash',
    'sql': 'sql',
    'go': 'go',
    'rs': 'rust',
    'php': 'php',
    'rb': 'ruby'
  };

  return typeMap[extension] || 'text';
}

export function parseDiffLines(diffContent: string): DiffLine[] {
  const lines = diffContent.split('\n');
  const parsedLines: DiffLine[] = [];
  
  let oldLineNumber = 0;
  let newLineNumber = 0;

  for (const line of lines) {
    if (line.startsWith('@@')) {
      // Parse hunk header to get line numbers
      const hunkMatch = line.match(/@@ -(\d+)(?:,\d+)? \+(\d+)(?:,\d+)? @@/);
      if (hunkMatch) {
        oldLineNumber = parseInt(hunkMatch[1]) - 1;
        newLineNumber = parseInt(hunkMatch[2]) - 1;
      }
      
      parsedLines.push({
        type: 'header',
        content: line,
        oldLineNumber: undefined,
        newLineNumber: undefined
      });
    } else if (line.startsWith('+')) {
      newLineNumber++;
      parsedLines.push({
        type: 'added',
        content: line,
        oldLineNumber: undefined,
        newLineNumber
      });
    } else if (line.startsWith('-')) {
      oldLineNumber++;
      parsedLines.push({
        type: 'removed',
        content: line,
        oldLineNumber,
        newLineNumber: undefined
      });
    } else if (line.startsWith(' ') || line === '') {
      oldLineNumber++;
      newLineNumber++;
      parsedLines.push({
        type: 'context',
        content: line,
        oldLineNumber,
        newLineNumber
      });
    } else {
      // Header lines like "diff --git", "index", "+++"", "---"
      parsedLines.push({
        type: 'header',
        content: line,
        oldLineNumber: undefined,
        newLineNumber: undefined
      });
    }
  }

  return parsedLines;
}

export function escapeHtml(unsafe: string): string {
  return unsafe
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}