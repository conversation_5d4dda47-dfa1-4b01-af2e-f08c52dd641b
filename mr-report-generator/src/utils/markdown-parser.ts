import type { MRData, ChangesWalkthrough } from '../types';
import { marked } from 'marked';

export function parseMarkdown(markdown: string): MRData {
  const data: MRData = {
    prType: '',
    description: '',
    mermaidDiagram: '',
    changesWalkthrough: {
      Enhancement: [],
      Refactoring: [],
      'Configuration changes': [],
      'Additional files': []
    },
    walkthroughHtml: ''
  };

  // Extract PR Type
  const prTypeMatch = markdown.match(/### \*\*PR Type\*\*\s*\n([^\n]+)/);
  if (prTypeMatch) {
    data.prType = prTypeMatch[1].trim();
  }

  // Extract Description
  const descMatch = markdown.match(/### \*\*Description\*\*\s*\n(.*?)(?=\n### |\n___|$)/s);
  if (descMatch) {
    data.description = descMatch[1].trim();
  }

  // Extract Mermaid diagram
  const mermaidMatch = markdown.match(/```mermaid\s*\n(.*?)\n```/s);
  if (mermaidMatch) {
    data.mermaidDiagram = mermaidMatch[1].trim();
  }

  // Extract Changes walkthrough HTML (修复正则表达式以匹配带emoji的标题)
  const walkthroughMatch = markdown.match(/### \*\*Changes walkthrough\*\*[^\n]*\n(.*?)(?=\n### |\n___|$)/s);
  if (walkthroughMatch) {
    data.walkthroughHtml = walkthroughMatch[1].trim();
  }

  // Extract Changes walkthrough (enhanced parsing)
  data.changesWalkthrough = extractChangesWalkthrough(markdown);

  return data;
}

function extractChangesWalkthrough(markdown: string): ChangesWalkthrough {
  const walkthrough: ChangesWalkthrough = {
    Enhancement: [],
    Refactoring: [],
    'Configuration changes': [],
    'Additional files': []
  };

  // Look for the Changes Walkthrough section
  const walkthroughMatch = markdown.match(/### \*\*Changes walkthrough\*\*(.*?)(?=\n### |\n___|$)/s);
  if (!walkthroughMatch) {
    return walkthrough;
  }

  const walkthroughContent = walkthroughMatch[1];

  try {
    // 使用 DOM 解析器解析 HTML
    if (typeof window !== 'undefined') {
      // 浏览器环境
      const parser = new DOMParser();
      const doc = parser.parseFromString(walkthroughContent, 'text/html');
      parseWalkthroughDOM(doc, walkthrough);
    } else {
      // Node.js 环境 - 使用 jsdom
      const { JSDOM } = require('jsdom');
      const dom = new JSDOM(walkthroughContent);
      parseWalkthroughDOM(dom.window.document, walkthrough);
    }
  } catch (error) {
    console.warn('HTML 解析失败，使用正则表达式降级:', error);
    
    // 降级到正则表达式解析 - 修复实际格式
    const categoryMatches = walkthroughContent.matchAll(/<td><strong>([^<]+)<\/strong><\/td><td><details><summary>\d+ files<\/summary><table>([\\s\\S]*?)<\/table><\/details><\/td>/g);
    
    for (const categoryMatch of categoryMatches) {
      const categoryName = categoryMatch[1].trim();
      const tableContent = categoryMatch[2];
      
      if (!walkthrough[categoryName]) {
        walkthrough[categoryName] = [];
      }
      
      // 实际格式: <td><strong>filename</strong><dd><code>description</code>...
      const fileMatches = tableContent.matchAll(/<td><strong>([^<]+)<\/strong><dd><code>([^<]+)<\/code>/g);
      
      for (const fileMatch of fileMatches) {
        walkthrough[categoryName].push({
          filePath: fileMatch[1].trim(),
          description: fileMatch[2].trim()
        });
      }
    }
  }

  return walkthrough;
}

function parseWalkthroughDOM(doc: Document, walkthrough: ChangesWalkthrough): void {
  // 查找主表格
  const mainTable = doc.querySelector('table');
  if (!mainTable) return;

  // 查找所有分类行
  const rows = mainTable.querySelectorAll('tbody > tr');
  
  for (const row of rows) {
    // 第一个 td 包含分类名
    const categoryCell = row.querySelector('td:first-child strong');
    if (!categoryCell) continue;
    
    const categoryName = categoryCell.textContent?.trim();
    if (!categoryName) continue;
    
    // 第二个 td 包含文件列表
    const filesCell = row.querySelector('td:nth-child(2)');
    if (!filesCell) continue;
    
    // 查找嵌套的表格中的文件
    const nestedTable = filesCell.querySelector('table');
    if (!nestedTable) continue;
    
    // 初始化分类
    if (!walkthrough[categoryName]) {
      walkthrough[categoryName] = [];
    }
    
    // 解析文件条目 - 基于实际的 HTML 结构
    const fileCells = nestedTable.querySelectorAll('td');
    for (const fileCell of fileCells) {
      const fileNameElement = fileCell.querySelector('strong');
      const descriptionElement = fileCell.querySelector('dd code');
      
      if (fileNameElement && descriptionElement) {
        const filePath = fileNameElement.textContent?.trim();
        const description = descriptionElement.textContent?.trim();
        
        if (filePath && description) {
          walkthrough[categoryName].push({
            filePath,
            description
          });
        }
      }
    }
  }
}

export function markdownToHtml(markdownText: string): string {
  // 配置 marked 解析器
  marked.setOptions({
    gfm: true,
    breaks: true
  });

  // 使用 marked 解析 Markdown
  const html = marked.parse(markdownText) as string;
  
  // 添加 Tailwind CSS 类名
  return html
    .replace(/<ul>/g, '<ul class="list-disc ml-6 space-y-1">')
    .replace(/<ol>/g, '<ol class="list-decimal ml-6 space-y-1">')
    .replace(/<li>/g, '<li class="text-gray-700">')
    .replace(/<p>/g, '<p class="text-gray-700 leading-relaxed mb-2">')
    .replace(/<h1>/g, '<h1 class="text-2xl font-bold text-gray-900 mb-4">')
    .replace(/<h2>/g, '<h2 class="text-xl font-semibold text-gray-900 mb-3">')
    .replace(/<h3>/g, '<h3 class="text-lg font-medium text-gray-900 mb-2">')
    .replace(/<h4>/g, '<h4 class="text-base font-medium text-gray-900 mb-2">')
    .replace(/<h5>/g, '<h5 class="text-sm font-medium text-gray-900 mb-1">')
    .replace(/<h6>/g, '<h6 class="text-xs font-medium text-gray-900 mb-1">')
    .replace(/<strong>/g, '<strong class="font-semibold">')
    .replace(/<em>/g, '<em class="italic">')
    .replace(/<code>/g, '<code class="bg-gray-100 px-1 py-0.5 rounded text-sm font-mono">')
    .replace(/<pre>/g, '<pre class="bg-gray-100 p-4 rounded-lg overflow-x-auto">')
    .replace(/<blockquote>/g, '<blockquote class="border-l-4 border-gray-300 pl-4 italic text-gray-600">')
    .replace(/<a /g, '<a class="text-blue-600 hover:text-blue-800 underline" ')
    .replace(/<table>/g, '<table class="w-full border-collapse border border-gray-300">')
    .replace(/<th>/g, '<th class="border border-gray-300 px-4 py-2 bg-gray-50 font-semibold text-left">')
    .replace(/<td>/g, '<td class="border border-gray-300 px-4 py-2">');
}