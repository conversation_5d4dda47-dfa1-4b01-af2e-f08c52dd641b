/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'diff-added': '#d1f2dd',
        'diff-removed': '#ffeaea',
        'diff-added-border': '#a3d977',
        'diff-removed-border': '#f97583',
        'diff-added-text': '#22863a',
        'diff-removed-text': '#cb2431',
        'diff-context': '#f8f9fa',
        'github-blue': '#0366d6',
        'github-gray': '#586069',
        'github-light-gray': '#f6f8fa',
        'github-border': '#e1e4e8'
      }
    },
  },
  plugins: [],
}