# MR 报告生成器 - Claude 开发指南

本文件为 Claude Code 在 MR 报告生成器项目中工作时提供指导。

## 项目概述

MR 报告生成器是一个基于 TypeScript + React 的现代化工具，用于生成交互式的 MR（Merge Request）报告。

### 技术栈
- **前端**: React 18 + TypeScript + Tailwind CSS
- **构建系统**: Vite + Bun 用于快速开发和构建
- **测试**: Playwright 用于端到端测试
- **HTML 解析**: jsdom 用于解析 AI 生成的 markdown

## 开发环境设置

### 初始化
```bash
cd mr-report-generator
bun install
```

### 开发命令
```bash
# 开发模式
bun run dev

# 生产构建
bun run build

# 类型检查
bun run type-check

# 运行测试
npx playwright test

# 查看测试报告
npx playwright show-report
```

## 核心功能架构

### 数据源处理

**重要**: MR 报告生成器处理两种不同的数据源：

1. **Git Diff 数据** (真实数据)：
   - 来源：通过 GitLab API 获取的 `mr.changes()`
   - 格式：包含实际文件更改的 JSON
   - 准确性：100% 可靠
   - 位置：`fixtures/diff_*.json`

2. **AI 生成的描述** (可能不准确)：
   - 来源：PR-Agent 的 AI 分析
   - 格式：带有 HTML 表格的 Markdown
   - 准确性：可能包含幻觉或不准确信息
   - 位置：`fixtures/mr_description_summary_*.md`

### 关键组件

1. **数据处理** (`src/utils/`):
   - `markdown-parser.ts`: 解析 AI 生成的 MR 描述（使用 jsdom 进行 HTML 解析）
   - `diff-processor.ts`: 将 Git diff 数据处理成结构化格式

2. **UI 组件** (`src/components/`):
   - `MRReport.tsx`: 带有交互功能的主报告组件
   - `DiffViewer.tsx`: 带有语法高亮的高级 diff 查看器

3. **类型定义** (`src/types/index.ts`):
   - 完整的 TypeScript 接口定义
   - 支持动态分类的 ChangesWalkthrough 接口

## 测试和调试

### Playwright 测试设置

项目使用 Playwright 进行端到端测试，特别用于调试复杂的数据解析问题。

#### 测试文件结构
```
tests/
├── walkthrough-debug.spec.ts  # 变更概览调试测试
└── debug-*.png               # 测试生成的截图
```

#### 运行调试测试

1. **基础测试运行**：
```bash
npx playwright test tests/walkthrough-debug.spec.ts
```

2. **可视化调试**（不推荐 AI 执行）：
```bash
npx playwright test tests/walkthrough-debug.spec.ts --headed
```

3. **生成测试报告**：
```bash
npx playwright test
npx playwright show-report
```

#### 调试测试内容

`walkthrough-debug.spec.ts` 测试会自动：

1. **构建最新代码**：使用测试 fixtures 重新构建 HTML
2. **页面内容分析**：
   - 检查变更概览部分是否正确渲染
   - 验证 AI 分类数量和类型
   - 输出每个分类的文件数量和描述
3. **数据源验证**：
   - 检查 HTML 解析是否成功
   - 验证文件路径匹配
   - 输出调试信息到控制台
4. **截图生成**：
   - `debug-walkthrough-full.png`: 完整页面截图
   - `debug-walkthrough-section.png`: 变更概览部分截图

#### 添加测试用的 data-testid

为了方便测试定位元素，在关键组件上添加 `data-testid` 属性：

```tsx
// 示例：MRReport.tsx 中的测试标识
<div data-testid="walkthrough-section">
  <h2 data-testid="walkthrough-title">变更概览</h2>
  <div data-testid="walkthrough-content">
    {renderWalkthroughSection()}
  </div>
</div>
```

### 常见调试场景

#### 1. AI 分类解析问题

**症状**: 变更概览显示降级的 "Enhancement" 和 "Other files" 而不是 AI 智能分类

**调试步骤**:
```bash
# 1. 运行调试测试查看解析结果（手动执行，不推荐 AI 执行）
npx playwright test tests/walkthrough-debug.spec.ts --headed

# 2. 检查控制台输出的分类信息
# 3. 查看生成的截图确认UI显示

# 4. 如果解析失败，检查 markdown 文件格式
cat fixtures/mr_description_summary_*.md | grep -A 20 "Changes walkthrough"
```

**解决方案**: 检查 `markdown-parser.ts` 中的 HTML 解析逻辑

#### 2. 文件路径验证失败

**症状**: AI 描述的文件在页面中不显示

**调试步骤**:
```bash
# 1. 比较 AI markdown 中的文件路径和实际 diff 数据
jq '.[] | .new_path' fixtures/diff_*.json | head -10
grep -o '<strong>[^<]*</strong>' fixtures/mr_description_summary_*.md | head -10

# 2. 检查文件路径匹配逻辑
# 查看 MRReport.tsx 中的 validateFilePath 函数
```

#### 3. HTML 解析错误

**症状**: 控制台显示 "HTML 解析失败，使用正则表达式降级"

**调试步骤**:
```bash
# 1. 检查 jsdom 是否正确安装
bun list jsdom

# 2. 验证 HTML 结构是否符合预期
grep -A 5 -B 5 '<table>' fixtures/mr_description_summary_*.md
```

## 生成完整报告的工作流

### 1. 生成 MR 描述（包含 diff 导出）
```bash
# 在项目根目录
pr-agent --pr_url=https://gitlab.com/owner/repo/-/merge_requests/123 describe
```

### 2. 构建交互式 HTML 报告
```bash
cd mr-report-generator
MARKDOWN_FILE=./fixtures/mr_description_summary_123.md \
DIFF_FILE=./fixtures/diff_123.json \
bun run build
```

### 3. 查看报告
```bash
open mr-report-generator/dist/index.html
```

## 数据格式说明

### AI 生成的 HTML 表格格式

AI 生成的 Changes walkthrough 使用以下 HTML 结构：

```html
<table>
  <thead>
    <tr><th></th><th align="left">Relevant files</th></tr>
  </thead>
  <tbody>
    <tr>
      <td><strong>分类名</strong></td>
      <td>
        <details>
          <summary>X files</summary>
          <table>
            <tr>
              <td>
                <strong>文件名</strong>
                <dd><code>文件描述</code></dd>
              </td>
              <td><a href="链接">+X/-Y</a></td>
            </tr>
          </table>
        </details>
      </td>
    </tr>
  </tbody>
</table>
```

### 文件路径验证策略

系统使用多重匹配策略确保 AI 描述的文件在真实 diff 中存在：

1. **精确匹配**: 完全匹配文件路径
2. **文件名匹配**: 只匹配文件名（去除路径）
3. **部分路径匹配**: 包含关系匹配

## 故障排除

### 常见问题

1. **构建失败**:
   - 检查环境变量是否正确设置
   - 确认 fixtures 文件存在且格式正确

2. **测试失败**:
   - 确保先运行构建生成最新的 HTML
   - 检查浏览器环境和 Playwright 安装

3. **解析错误**:
   - 验证 AI 生成的 markdown 格式
   - 检查 jsdom 依赖是否正确安装

### 性能优化

- 支持大型 MR（50+ 文件）的优化处理
- Diff 查看器支持延迟加载和折叠
- Token 感知处理防止 API 限制

## 配置文件

- **Playwright 配置**: `playwright.config.ts`
- **Vite 配置**: `vite.config.ts`
- **TypeScript 配置**: `tsconfig.json`
- **包管理**: `package.json`

## 重要注意事项

1. **始终验证数据源**: AI 生成的内容可能不准确，系统优先使用真实的 Git diff 数据
2. **保持测试文件**: fixtures 目录中的测试文件对调试至关重要
3. **使用 HTML 解析器**: 避免使用脆弱的正则表达式解析复杂的 HTML 结构
4. **截图调试**: Playwright 生成的截图是调试 UI 问题的最佳工具