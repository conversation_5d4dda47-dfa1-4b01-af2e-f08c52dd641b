import { readFile } from 'fs/promises'
import { Plugin } from 'vite'

const VIRTUAL_MODULE_ID = 'virtual:mr-data'
const RESOLVED_VIRTUAL_MODULE_ID = '\0' + VIRTUAL_MODULE_ID

export function viteMrDataPlugin(): Plugin {
  return {
    name: 'vite-plugin-mr-data',
    resolveId(id) {
      if (id === VIRTUAL_MODULE_ID) {
        return RESOLVED_VIRTUAL_MODULE_ID
      }
    },
    async load(id) {
      if (id === RESOLVED_VIRTUAL_MODULE_ID) {
        try {
          // 从环境变量获取文件路径
          const markdownFile = process.env.MARKDOWN_FILE
          const diffFile = process.env.DIFF_FILE

          if (!markdownFile || !diffFile) {
            throw new Error('环境变量 MARKDOWN_FILE 和 DIFF_FILE 必须设置')
          }

          console.log(`📖 读取 Markdown 文件: ${markdownFile}`)
          console.log(`📊 读取 Diff 文件: ${diffFile}`)

          // 读取文件内容
          const markdownContent = await readFile(markdownFile, 'utf-8')
          const diffContent = await readFile(diffFile, 'utf-8')

          // 返回虚拟模块的内容
          return `
export const markdownContent = ${JSON.stringify(markdownContent)};
export const diffData = ${JSON.stringify(JSON.parse(diffContent))};
          `
        } catch (error) {
          console.error('❌ 读取 MR 数据文件时发生错误:', error)
          
          // 返回空数据以避免构建失败
          return `
export const markdownContent = "";
export const diffData = [];
          `
        }
      }
    }
  }
} 