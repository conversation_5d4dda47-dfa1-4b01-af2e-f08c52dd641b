{"name": "mr-report-generator", "version": "1.0.0", "description": "Modern TypeScript-based MR report generator with React UI", "main": "dist/index.js", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "generate": "sh -c 'if [ $# -ne 2 ]; then echo \"❌ 使用方法: npm run generate -- <markdown文件> <diff文件>\"; echo \"📖 示例: npm run generate -- ./mr_description.md ./diff.json\"; exit 1; fi; echo \"📦 设置环境变量并开始构建...\"; MARKDOWN_FILE=\"$1\" DIFF_FILE=\"$2\" npm run build' -- ", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit"}, "keywords": ["merge-request", "report", "diff", "typescript", "react"], "author": "", "license": "MIT", "dependencies": {"@types/marked": "^6.0.0", "@types/node": "^24.0.10", "clsx": "^2.0.0", "diff2html": "^3.4.47", "highlight.js": "^11.9.0", "jsdom": "^26.1.0", "marked": "^16.0.0", "mermaid": "^10.6.1", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@playwright/test": "^1.53.2", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "playwright": "^1.53.2", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "typescript": "^5.2.2", "vite": "^5.0.8", "vite-plugin-singlefile": "^2.3.0"}, "overrides": {"rollup": "npm:@rollup/wasm-node"}}