### **User description**
**提交范围对比**: f50a2a...749631

**包含的提交 (50 个):**
- `2109f65d` Merge branch 'fix/camera-auth' into 'release/20250513' - <PERSON><PERSON><PERSON> (2025-05-14)
- `4896d07b` feat: 首页卡片和沉浸流低清晰度视频 - xiongluyang (2025-05-15)
- `cda2e098` fix: 玩法卡片视频低分辨率视频 - xiongluyang (2025-05-15)
- `c41e86cb` feat: 切换低分辨率视频添加网络判断 - xiongluyang (2025-05-15)
- `ad50c723` feat: 低分辨率视频字段 - xiongluyang (2025-06-04)
- `25a170ec` fix: 低分辩率字段问题 - xiongluyang (2025-06-04)
- `b7774aaa` feat: 添加低分辨率埋点 - xiongluyang (2025-06-05)
- `b9cbf91d` fix: 预加载低分辨率视频增加弱网条件 - xiongluyang (2025-06-05)
- `feac7733` Merge branch 'feat/low-resolution-video' into 'release/20250520' - xiongluyang (2025-06-05)
- `8b9b9c63` ai生成水印 - wandanni (2025-06-06)
- `2df19163` Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-06)
- `79897ee6` fix: android水印重叠，把ai水印移到screen组件里 - wandanni (2025-06-09)
- `e6c35bae` Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-09)
- `51167b80` fix: android水印重叠，Android键盘升起问题 - wandanni (2025-06-09)
- `8e73b23b` fix:android角色列表样式 - wandanni (2025-06-09)
- `d9f069a5` Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-09)
- `e00c5a77` fix:表情包入口底部重叠 - wandanni (2025-06-09)
- `fe990cb7` Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-09)
- `4036221c` fix:炖图问题 - wandanni (2025-06-09)
- `d8ec2262` Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-09)
- `cd943419` fix:炖图问题 - wandanni (2025-06-09)
- `38df6f79` Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-09)
- `75634c84` 炖图回退 - wandanni (2025-06-10)
- `29fe21ba` Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-10)
- `c79ba436` 炖图loading问题fix - wandanni (2025-06-10)
- `10aa0a47` Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-10)
- `04a57e03` 发布字数限制改成2000字 - wandanni (2025-06-23)
- `d323d5c7` Merge branch 'feat/danny_20250609' into 'release/20250623' - wandanni (2025-06-23)
- `5d4a1fe6` Merge branch 'release/20250520' into 'main' - xiongluyang (2025-06-25)
- `4fa0945d` fix: 修复微动拍图片同款sref报错 - xiongluyang (2025-06-25)
- `5ff93953` Merge branch 'fix/live-takesame-sref' into 'release/20250627' - xiongluyang (2025-06-26)
- `b5f5e395` feat: 添加一键做实物功能 - wandanni (2025-06-26)
- `4829a5c8` feat: 添加一键做实物功能 - wandanni (2025-06-26)
- `c3a8fa6e` Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-26)
- `6097f087` feat: 玩法页一键实物 - wandanni (2025-06-26)
- `bd4797e1` Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-26)
- `22ef1985` feat: 炖图预览页一键实物 - wandanni (2025-06-26)
- `855e0d08` Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-26)
- `a939edf9` feat: 详情页一键实物 - wandanni (2025-06-26)
- `42b35975` Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-26)
- `5b685d0e` feat: 一键实物埋点 - wandanni (2025-06-26)
- `ef76e2d4` feat: 一键实物埋点 - wandanni (2025-06-26)
- `9271c372` Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-26)
- `9483620e` feat: 去掉高校榜入口 - wandanni (2025-06-27)
- `d00de116` Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-27)
- `5a9306e5` feat: 去掉高校榜入口 - wandanni (2025-06-27)
- `97e3b3df` Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-27)
- `b5c2af75` fix: lint - wandanni (2025-06-27)
- `b8015fcd` Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-27)
- `74963122` Merge branch 'release/20250627' into 'main' - linyueqiang (2025-07-03)


___

### **PR Type**
Enhancement, Bug fix


___

### **Description**
本次更新包含多项功能增强和修复，主要集中在“一键做实物”新功能的引入、视频播放在弱网环境下的体验优化，以及AI生成内容的水印标识。

**QA 测试重点:**

- **“一键做实物”新功能:**
  - 在多个页面新增了“一键做实物”入口，包括图片详情页、玩法页、炖图预览页和发布流程中。
  - QA需验证点击这些入口能正确跳转至指定的外部表单链接，并检查各入口的埋点上报是否准确。

- **弱网视频播放优化:**
  - 在首页信息流和沉浸式视频流中，应用会根据设备性能和网络状况（弱网）自动选择播放低分辨率视频。
  - QA需在低端设备和模拟弱网环境下进行测试，验证视频加载速度和播放流畅度是否得到改善。
  - 重点测试沉浸式视频流中的播放失败重试逻辑，确认其是否会切换到低分辨率视频进行重试。

- **AI生成内容水印:**
  - 在多个AI相关页面底部新增了“内容由AI生成”的文字水印。
  - QA需检查机器人对话、平行世界、炖图、发布表情包等页面，确保水印正常显示，并在键盘弹出时能正确隐藏（尤其在Android上）。

- **其他用户体验优化:**
  - 发布作品时，故事描述的字数限制已从500字放宽至2000字。
  - 首页的“高校榜”入口已被移除，请确认该入口不再可见。
  - “炖图”预览页底部新增了“我的图集”按钮，用于保存图片。


___

### **Changes diagram**

```mermaid
flowchart LR
    subgraph Feature_Materialize [一键做实物]
        direction LR
        U1["用户"] --> B1{"详情页/发布页等"};
        B1 -- "点击'一键做实物'" --> F1["打开外部表单页"];
    end

    subgraph Feature_VideoOpt [视频播放优化]
        direction LR
        U2["用户"] --> App{"请求视频"};
        App --> C1{"设备/网络检测"};
        C1 -- "弱网/低端设备" --> V1["加载低分辨率视频"];
        C1 -- "正常" --> V2["加载高分辨率视频"];
        V1 --> P1["播放视频"];
        V2 --> P1;
    end
```


___



### **Changes walkthrough** 📝
<table><thead><tr><th></th><th align="left">Relevant files</th></tr></thead><tbody><tr><td><strong>Enhancement</strong></td><td><details><summary>13 files</summary><table>
<tr>
  <td><strong>src/components/materialize/useMaterialize.ts</strong><dd><code>新增“一键做实物”功能的业务逻辑钩子，处理打开外部链接和埋点上报。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="">+34/-0</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/materialize/MaterializeButton.tsx</strong><dd><code>新增“一键做实物”的通用按钮组件，用于不同页面的功能入口。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="">+41/-0</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/materialize/MaterlizeMask.tsx</strong><dd><code>新增“一键做实物”的底部遮罩组件，用于图片详情页的快捷入口。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="">+52/-0</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/aiTag/index.tsx</strong><dd><code>新增“内容由AI生成”的水印组件，并处理与键盘显隐的交互。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="">+49/-0</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/screen/index.tsx</strong><dd><code>为通用页面组件`Screen`添加`withWaterMark`属性，以支持显示AI生成水印。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="">+42/-35</a>&nbsp; </td>

</tr>

<tr>
  <td><strong>src/utils/cardUtils.ts</strong><dd><code>新增工具函数，用于根据设备性能和网络状况选择合适的视频播放地址。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="">+32/-0</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/livePhotoScreen/ImmersiveVideo.tsx</strong><dd><code>沉浸式视频流支持动态选择视频清晰度，并在播放失败时尝试使用低分辨率视频重试。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="">+63/-15</a>&nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/feedcard/ugcCard/VideoRender.tsx</strong><dd><code>信息流卡片中的视频渲染逻辑更新，以支持在弱网或低端设备上播放低分辨率视频。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="">+29/-7</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/livePhotoScreen/timeRecord.ts</strong><dd><code>优化网络带宽估算算法，采用指数加权移动平均法以获得更平滑的网速数据。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="">+30/-8</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>app/publish/index.tsx</strong><dd><code>将发布作品时故事描述的字数上限从500字提高到2000字。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="">+3/-2</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/rankListScreen/RankListButton.tsx</strong><dd><code>移除了首页的“高校榜”入口及其相关的气泡提示。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="">+0/-17</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/makePhoto/previewView/components/BottomActions.tsx</strong><dd><code>在炖图预览页的底部操作栏中，新增了“我的图集”大按钮用于保存图片。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="">+9/-5</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/publishEntry/index.tsx</strong><dd><code>在首页发布作品的浮层中集成了“一键做实物”功能入口。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="">+15/-4</a>&nbsp; &nbsp; </td>

</tr>
</table></details></td></tr><tr><td><strong>Additional files</strong></td><td><details><summary>39 files</summary><table>
<tr>
  <td><strong>app/bot/index.tsx</strong></td>
  <td><a href="">+28/-12</a>&nbsp; </td>

</tr>

<tr>
  <td><strong>app/make-photo/index.tsx</strong></td>
  <td><a href="">+5/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>app/parallel-world/[id].tsx</strong></td>
  <td><a href="">+9/-3</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>app/webview.tsx</strong></td>
  <td><a href="">+4/-0</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>package.json</strong></td>
  <td><a href="">+1/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>proto-gen.lock.json</strong></td>
  <td><a href="">+2/-2</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/detailScreen/imageContent/index.tsx</strong></td>
  <td><a href="">+5/-3</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/detailScreen/toFristDetailTag/index.tsx</strong></td>
  <td><a href="">+2/-2</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/feedcard/entryCard/GameTemplateCard.tsx</strong></td>
  <td><a href="">+3/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/feedcard/ugcCard/BBSCard.tsx</strong></td>
  <td><a href="">+0/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/goods/edit/components/material/index.tsx</strong></td>
  <td><a href="">+1/-0</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/goods/edit/index.tsx</strong></td>
  <td><a href="">+1/-0</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/goodsHome/components/GoodsDetailModal.tsx</strong></td>
  <td><a href="">+14/-3</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/livePhotoPublish/index.tsx</strong></td>
  <td><a href="">+2/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/livePhotoScreen/TakeSameSheet.tsx</strong></td>
  <td><a href="">+4/-2</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/livePhotoScreen/hooks/useVideoStatusUpdate.ts</strong></td>
  <td><a href="">+5/-0</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/playgoundScreen/meme/MemePublish.tsx</strong></td>
  <td><a href="">+3/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/rankListScreen/index.tsx</strong></td>
  <td><a href="">+0/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/role-create/generateRole/intex.tsx</strong></td>
  <td><a href="">+2/-0</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/icons/icon.tsx</strong></td>
  <td><a href="">+1/-0</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/makePhoto/bottomTab/index.tsx</strong></td>
  <td><a href="">+1/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/makePhoto/emojiPannel/EmojiPrview/index.tsx</strong></td>
  <td><a href="">+1/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/makePhoto/emojiPannel/index.tsx</strong></td>
  <td><a href="">+1/-0</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/makePhoto/loadingView/index.tsx</strong></td>
  <td><a href="">+7/-5</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/makePhoto/previewView/PreviewViewSimple.tsx</strong></td>
  <td><a href="">+1/-0</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/makePhoto/previewView/components/previewItem/SaveToAlbumLarge.tsx</strong></td>
  <td><a href="">+43/-0</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/makePhoto/previewView/index.tsx</strong></td>
  <td><a href="">+5/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/makePhoto/roleSelector/IPRoleSelector.tsx</strong></td>
  <td><a href="">+1/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/makePhoto/roleSelector/RoleList.tsx</strong></td>
  <td><a href="">+0/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/materialize/MaterializeXiaoli.tsx</strong></td>
  <td><a href="">+72/-0</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/publishEntry/GameEntryCard.tsx</strong></td>
  <td><a href="">+3/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/video/index.tsx</strong></td>
  <td><a href="">+26/-52</a>&nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/waterfall/WaterFall2.tsx</strong></td>
  <td><a href="">+12/-5</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/hooks/useChangeRoute.ts</strong></td>
  <td><a href="">+1/-0</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/hooks/useDevicePerformance.ts</strong></td>
  <td><a href="">+6/-2</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/store/makePhotoV2.ts</strong></td>
  <td><a href="">+4/-4</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/utils/device/network.tsx</strong></td>
  <td><a href="">+16/-0</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/utils/device/performanceTier.ts</strong></td>
  <td><a href="">+6/-0</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/utils/report/index.ts</strong></td>
  <td><a href="">+1/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>
</table></details></td></tr></tr></tbody></table>

___

<details><summary>Need help?</summary>- Type <code>/help how to ...</code> in the comments thread for any questions about PR-Agent usage.<br>- Check out the <a href='https://qodo-merge-docs.qodo.ai/usage-guide/'>documentation</a> for more information.</details>