[{"new_path": "app/bot/index.tsx", "old_path": "app/bot/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -1,7 +1,14 @@\n-import { useLatest, useMemoizedFn, useMount, useThrottleFn, useUnmount } from 'ahooks';\n+import {\n+  useLatest,\n+  useMemoizedFn,\n+  useMount,\n+  useThrottleFn,\n+  useUnmount\n+} from 'ahooks';\n import { useEffect, useMemo, useRef, useState } from 'react';\n import {\n   Dimensions,\n+  InteractionManager,\n   NativeScrollEvent,\n   NativeSyntheticEvent,\n   Platform,\n@@ -9,8 +16,7 @@ import {\n   TextInput,\n   TextInputKeyPressEventData,\n   TouchableOpacity,\n-  View,\n-  InteractionManager\n+  View\n } from 'react-native';\n import { ShadowedView } from 'react-native-fast-shadow';\n import { IOScrollView } from 'react-native-intersection-observer';\n@@ -18,11 +24,19 @@ import LinearGradient from 'react-native-linear-gradient';\n import { Icon, hideLoading, showLoading } from '@/src/components';\n import { hideToast, showToast } from '@/src/components';\n import { Image, Text } from '@/src/components';\n+import { AiTag } from '@/src/components/aiTag';\n import AudioPlayer, { clearSounds } from '@/src/components/audioPlayer';\n-import { InputStatus, RenderFlatListMessagesMemo, useBot } from '@/src/components/bot';\n+import {\n+  InputStatus,\n+  RenderFlatListMessagesMemo,\n+  useBot\n+} from '@/src/components/bot';\n import { AnimatedXiaoli } from '@/src/components/bot/components/animatedXiaoli';\n import { DeepThinkButton } from '@/src/components/bot/components/deepThinkButton';\n-import { BotEvent, BotEventBus } from '@/src/components/bot/components/utils/eventBus';\n+import {\n+  BotEvent,\n+  BotEventBus\n+} from '@/src/components/bot/components/utils/eventBus';\n import { BotStatus } from '@/src/components/bot/hooks';\n import { PagePerformance } from '@/src/components/common/pagePerformance';\n import { useSafeBottomArea } from '@/src/hooks';\n@@ -34,13 +48,15 @@ import { $flexRow } from '@/src/theme/variable';\n import { StyleSheet, dp2px } from '@/src/utils';\n import { reportExpo } from '@/src/utils/report';\n import { RecommendList } from '@Components/bot/components/recommend';\n-import { hideConfirm, showConfirm } from '@Components/popup/confirmModalGlobal/Confirm';\n+import {\n+  hideConfirm,\n+  showConfirm\n+} from '@Components/popup/confirmModalGlobal/Confirm';\n import { Screen } from '@Components/screen';\n import { useParams } from '../../src/hooks/useParams';\n import { ChatMessageContent } from '@/proto-registry/src/web/raccoon/chatbot/chatbot_pb';\n import { useShallow } from 'zustand/react/shallow';\n \n-\n const bg = require('@Assets/image/bot/bg.png');\n const deepseek = require('@Assets/image/bot/deepseek.png');\n \n@@ -483,6 +499,7 @@ export default function Bot() {\n     <PagePerformance pathname=\"bot/index\">\n       <Screen\n         theme=\"dark\"\n+        withWaterMark\n         preset=\"fixed\"\n         maskAreaShown={false}\n         safeAreaEdges={['bottom', 'top']}\n@@ -524,7 +541,7 @@ export default function Bot() {\n           showsVerticalScrollIndicator={true}\n           keyboardShouldPersistTaps=\"handled\"\n           scrollEventThrottle={200}\n-          onTouchStart={() => { \n+          onTouchStart={() => {\n             inputRef.current?.blur();\n           }}\n           onContentSizeChange={(width, height) => {\n@@ -602,8 +619,7 @@ export default function Bot() {\n           }}\n         />\n \n-        { \n-          animatedXiaoLiVisible &&\n+        {animatedXiaoLiVisible && (\n           <>\n             <AnimatedXiaoli\n               // 为什么要写两个组件呢？ 为了防止布局抖动\n@@ -632,7 +648,7 @@ export default function Bot() {\n               }}\n             />\n           </>\n-        }\n+        )}\n       </Screen>\n     </PagePerformance>\n   );\n@@ -731,4 +747,4 @@ const style = StyleSheet.create({\n     fontWeight: '600',\n     color: StyleSheet.darkColors.white[900]\n   }\n-});\n\\ No newline at end of file\n+});\n"}, {"new_path": "app/make-photo/index.tsx", "old_path": "app/make-photo/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -41,6 +41,7 @@ import {\n   reportExpo,\n   reportMakePhotoTrack\n } from '@/src/utils/report';\n+import { setTimeoutWorklet } from '@/src/utils/worklet';\n import { Image } from '@Components/image';\n import { ElementSuffix, MakePhotoEvents } from '@Components/makePhoto/constant';\n import { LoadingView, LoadingViewRef } from '@Components/makePhoto/loadingView';\n@@ -230,6 +231,7 @@ function MakePhoto() {\n   }, [inputVisible]);\n \n   const onBack = usePersistFn(() => {\n+    console.log('onBack----');\n     const { pageState, changePageState } = useMakePhotoStoreV2.getState();\n     const { editPageState } = useMakePhotoEdit.getState();\n     if (\n@@ -250,7 +252,9 @@ function MakePhoto() {\n       if (palyType === PlayType.drawing) {\n         loadingViewRef.current?.reset();\n         useMakePhotoEdit.getState().reset();\n-        changePageState(PageState.diy);\n+        setTimeout(() => {\n+          changePageState(PageState.diy);\n+        }, 200); // 这里是为了等photoLoading状态更新\n       }\n     } else {\n       showConfirm({\n"}, {"new_path": "app/parallel-world/[id].tsx", "old_path": "app/parallel-world/[id].tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -28,7 +28,8 @@ import ParallelWorldConsumer from '@/src/bizComponents/parallelWorld/screens/con\n import ParallelWorldFeed from '@/src/bizComponents/parallelWorld/screens/feed';\n import ParallelWorldMain from '@/src/bizComponents/parallelWorld/screens/main';\n import ParallelWorldPublish from '@/src/bizComponents/parallelWorld/screens/publish';\n-import { Image } from '@/src/components';\n+import { Image, Screen } from '@/src/components';\n+import { AiTag } from '@/src/components/aiTag';\n import AudioPlayer from '@/src/components/audioPlayer';\n import { audioControl } from '@/src/components/audioPlayer/control';\n import { PagePerformance } from '@/src/components/common/pagePerformance';\n@@ -288,7 +289,12 @@ export default function World() {\n \n   return (\n     <PagePerformance pathname=\"parallel-world/[id]\">\n-      <View style={{ flex: 1, justifyContent: 'center' }}>\n+      <Screen\n+        headerShown={false}\n+        safeAreaEdges={[]}\n+        StatusBarProps={{ backgroundColor: 'transparent' }}\n+        withWaterMark\n+      >\n         {curRoute?.route && !isParallelWorldLoading && (\n           <Animated.View style={[{ flex: 1 }, $screenFoldStyle_A]}>\n             {renderParallelPage(curRoute?.route, curRoute)}\n@@ -308,7 +314,7 @@ export default function World() {\n           contentFit=\"cover\"\n           style={[styles.$absoluteFull, styles.$bgImg]}\n         />\n-      </View>\n+      </Screen>\n     </PagePerformance>\n   );\n \n"}, {"new_path": "app/publish/index.tsx", "old_path": "app/publish/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -99,6 +99,7 @@ import { PartialMessage } from '@bufbuild/protobuf';\n import { ImageAspectRatio } from '@step.ai/proto-gen/raccoon/common/imagen_pb';\n import { useShallow } from 'zustand/react/shallow';\n \n+const STORY_LIMIT = 2000;\n const ADD_KEY = 9999;\n const $line: ViewStyle = {\n   borderTopWidth: 0.5,\n@@ -279,7 +280,7 @@ function Publish() {\n \n   const validStory = useMemo(() => {\n     // if (!story.length) return false;\n-    if (story.length > 500) return false;\n+    if (story.length > STORY_LIMIT) return false;\n     return true;\n   }, [story]);\n \n@@ -1096,7 +1097,7 @@ function Publish() {\n                       !validStory && story.length ? st.$error : null\n                     ]}\n                   >\n-                    字数({story.length}/500)\n+                    字数({story.length}/{STORY_LIMIT})\n                   </Text>\n                   {renderClose()}\n                 </View>\n"}, {"new_path": "app/webview.tsx", "old_path": "app/webview.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -11,6 +11,7 @@ import {\n   useWebviewReport\n } from '@/src/bizComponents/webviewScreen/hooks/webviewReport.hook';\n import { Icon, hideLoading, showToast } from '@/src/components';\n+import { AiTag } from '@/src/components/aiTag';\n import { PagePerformance } from '@/src/components/common/pagePerformance';\n import { hideShare, showCommonShare, showShare } from '@/src/components/share';\n import { shareCompPresets } from '@/src/components/share/channelConfig';\n@@ -52,6 +53,7 @@ export type WebviewScreenParams = {\n   screenParams?: string;\n   webviewParams?: string;\n   preloadScene?: string;\n+  withAiTag?: string;\n   darkThemeStatus?: string;\n   theme?: Theme;\n };\n@@ -74,6 +76,7 @@ export interface WebviewShareParams {\n export default function WebviewScreen() {\n   const {\n     url = '',\n+    withAiTag = '0',\n     title = '',\n     shareParams,\n     searchParams,\n@@ -401,6 +404,7 @@ export default function WebviewScreen() {\n     <PagePerformance pathname=\"webview\">\n       <Screen\n         theme={theme}\n+        withWaterMark={withAiTag === '1'}\n         title={showTitle}\n         screenStyle={{\n           backgroundColor: backgroundColor || themeColor.background.page\n"}, {"new_path": "package.json", "old_path": "package.json", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -60,7 +60,7 @@\n     \"@step.ai/devtools\": \"^0.3.2\",\n     \"@step.ai/eas-build-hooks\": \"^1.9.0\",\n     \"@step.ai/event-monitor\": \"^0.6.3\",\n-    \"@step.ai/expo-av\": \"^14.0.7-dev.2\",\n+    \"@step.ai/expo-av\": \"^14.1.0\",\n     \"@step.ai/expo-cocoapods\": \"^0.3.0\",\n     \"@step.ai/expo-extensions\": \"^2.0.0\",\n     \"@step.ai/expo-navigator\": \"^1.1.2\",\n"}, {"new_path": "proto-gen.lock.json", "old_path": "proto-gen.lock.json", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -1,13 +1,13 @@\n {\n   \"repos\": {\n     \"passport\": {\n-      \"commit\": \"289b69af42744028aca7d7312ad10c84db2695a6\"\n+      \"commit\": \"7b1cd0586df2eaa04ddc40efe8d3dd4772274322\"\n     },\n     \"step-bridge\": {\n       \"commit\": \"23d74262f05e9ecc75a94905bdae5381c998d9f3\"\n     },\n     \"raccoon\": {\n-      \"commit\": \"289b69af42744028aca7d7312ad10c84db2695a6\"\n+      \"commit\": \"7b1cd0586df2eaa04ddc40efe8d3dd4772274322\"\n     },\n     \"trpc-infra\": {\n       \"commit\": \"77fc3539f5d5f285f5a89ba2f20bfebef60c2561\"\n"}, {"new_path": "src/bizComponents/detailScreen/imageContent/index.tsx", "old_path": "src/bizComponents/detailScreen/imageContent/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -62,6 +62,7 @@ import {\n   MID_RATIO_SECTION,\n   getImageContentRatios\n } from './utils';\n+import { MaterlizeMask } from '@/src/components/materialize/MaterlizeMask';\n \n export type LikeCallbackConfig = {\n   doubleLike?: {\n@@ -709,8 +710,8 @@ export function ImageContent(props: ImageContentProps) {\n               protos={props.data?.protos}\n               style={{\n                 position: 'absolute',\n-                right: 12,\n-                bottom: 16\n+                right: 12, \n+                bottom: 54\n               }}\n               fromId={fromId as string}\n             />\n@@ -727,6 +728,7 @@ export function ImageContent(props: ImageContentProps) {\n             {validImages.type === ImageType.photo && (\n               <PKTag pks={pkTagData} photo={curImgInfo.curImg as PhotoInfo} />\n             )}\n+        <MaterlizeMask logParams={{game_type:`${detail?.gameType ?? ''}`,card_id:cardId??''}}/>\n           </View>\n         </GestureDetector>\n       </View>\n@@ -763,7 +765,7 @@ const st = StyleSheet.create({\n   },\n   $pagin: {\n     position: 'absolute',\n-    bottom: 6,\n+    bottom: 44,\n     width: '100%',\n     zIndex: 2\n   }\n"}, {"new_path": "src/bizComponents/detailScreen/toFristDetailTag/index.tsx", "old_path": "src/bizComponents/detailScreen/toFristDetailTag/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -1,9 +1,10 @@\n import { useMemoizedFn } from 'ahooks';\n import { useNavigation } from 'expo-router';\n import { useEffect, useMemo } from 'react';\n-import { StyleProp, Text, View, ViewStyle } from 'react-native';\n+import { StyleProp, View, ViewStyle } from 'react-native';\n import { Gesture, GestureDetector } from 'react-native-gesture-handler';\n import Animated, { FadeIn, FadeOut, runOnJS } from 'react-native-reanimated';\n+import { Text } from '@/src/components';\n import { CommonColor } from '@/src/theme/colors/common';\n import { createStyle } from '@/src/utils';\n import { reportClick, reportExpo } from '@/src/utils/report';\n@@ -101,7 +102,6 @@ const $styles = createStyle({\n     position: 'absolute',\n     backgroundColor: 'rgba(0, 0, 0, 0.3)',\n     left: 0,\n-    bottom: 16\n   },\n \n   innerContainer: {\n"}, {"new_path": "src/bizComponents/feedcard/entryCard/GameTemplateCard.tsx", "old_path": "src/bizComponents/feedcard/entryCard/GameTemplateCard.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -14,6 +14,7 @@ import LinearGradient from 'react-native-linear-gradient';\n import { Image, showToast } from '@/src/components';\n import { EntryMediaList } from '@/src/components/publishEntry/constant';\n import { useChangeRoute } from '@/src/hooks/useChangeRoute';\n+import { useDevicePerformance } from '@/src/hooks/useDevicePerformance';\n import { typography } from '@/src/theme';\n import { CardType, GameTemplateExtInfo, GameType } from '@/src/types';\n import { StyleSheet, dp2px } from '@/src/utils';\n@@ -47,6 +48,7 @@ export const GameTemplateCard = (\n   } = props;\n \n   const { gameType, displayImageUrl, displayVideoUrl } = data.card || {};\n+  const { isLowEndDevice } = useDevicePerformance();\n   const templateInfo = data.card?.imitationCardInfo\n     ?.value as GameTemplateExtInfo;\n   const {\n@@ -176,7 +178,7 @@ export const GameTemplateCard = (\n             alignItems: 'center'\n           }}\n         >\n-          {displayVideoUrl ? (\n+          {displayVideoUrl && !isLowEndDevice ? (\n             <View\n               style={{\n                 height: posterHeight,\n"}, {"new_path": "src/bizComponents/feedcard/ugcCard/BBSCard.tsx", "old_path": "src/bizComponents/feedcard/ugcCard/BBSCard.tsx", "deleted_file": true, "new_file": false, "renamed_file": false, "diff": "@@ -248,7 +248,6 @@ export function BBSCard(\n       y: e.nativeEvent.pageY - e.nativeEvent.locationY\n     });\n \n-    // @ts-expect-error 上报参数\n     reportClick('content_button', {\n       ...reportParams\n     });\n"}, {"new_path": "src/bizComponents/feedcard/ugcCard/VideoRender.tsx", "old_path": "src/bizComponents/feedcard/ugcCard/VideoRender.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -11,12 +11,17 @@ import {\n import { View } from 'react-native';\n import { Image, ImageProps } from '@/src/components';\n import { TWaterFallInternalEvent } from '@/src/components/waterfall/type';\n+import { useDevicePerformance } from '@/src/hooks/useDevicePerformance';\n import { PlainType } from '@/src/types';\n import { isIos } from '@/src/utils';\n+import { getVideoUrlFromCard } from '@/src/utils/cardUtils';\n+import { checkWeakNetwork } from '@/src/utils/device/network';\n import { ReportError, errorReport } from '@/src/utils/error-log';\n import { Event } from '@/src/utils/event';\n import { formatTosUrl } from '@/src/utils/getTosUrl';\n import { FeedRichCardInfo } from '../../feedScreen/type';\n+import { useVideoStatusUpdate } from '../../livePhotoScreen/hooks/useVideoStatusUpdate';\n+import { bindwidthRecorder } from '../../livePhotoScreen/timeRecord';\n import { LivePhotoExtInfo } from '@/proto-registry/src/web/raccoon/common/livephoto_pb.js';\n import { ReimagineExtInfo } from '@/proto-registry/src/web/raccoon/common/reimagine_pb.js';\n import { useIsFocused } from '@react-navigation/native';\n@@ -59,10 +64,17 @@ const VideoRender = forwardRef<{ stop: () => Promise<void> }, VideoRenderProps>(\n     const [isError, setIsError] = useState(false);\n     const videoRef = useRef<Video>(null);\n     const isFocused = useIsFocused();\n+    const { isLowEndDevice } = useDevicePerformance();\n+    const isWeakNewwork = useMemo(\n+      () => checkWeakNetwork(bindwidthRecorder.getEWMA()),\n+      []\n+    );\n \n     const [lastLoadPoster, setLastLoadPoster] = useState<string | undefined>();\n     const [lastLoadVideo, setLastLoadVideo] = useState<string | undefined>();\n \n+    const { onPlaybackStatusUpdate } = useVideoStatusUpdate();\n+\n     useImperativeHandle(ref, () => {\n       return {\n         async stop() {\n@@ -92,14 +104,23 @@ const VideoRender = forwardRef<{ stop: () => Promise<void> }, VideoRenderProps>(\n     }, [index, eventBus]);\n \n     const videoSource = useMemo(() => {\n-      const cardExtInfo = data?.card?.cardExtInfo?.value?.value;\n-\n-      const uri =\n-        data.card?.displayVideoUrl ||\n-        (cardExtInfo as LivePhotoExtInfo | ReimagineExtInfo)?.videoUrl ||\n-        (cardExtInfo as OtakudanceExtInfo)?.video?.url;\n+      const uri = getVideoUrlFromCard(\n+        data?.card,\n+        isLowEndDevice || isWeakNewwork\n+      );\n       return uri ? { uri } : void 0;\n-    }, [data.card]);\n+    }, [data.card?.id]); // isLowEndDevice不加入deps，防止清晰度切换。\n+\n+    // useEffect(() => {\n+    //   console.log('### isLowEndDevice', isLowEndDevice);\n+    //   if (showVideo && isLowEndDevice) {\n+    //     console.log(\n+    //       '### 使用低分辨率视频',\n+    //       data?.card?.displayVideoUrl,\n+    //       videoSource\n+    //     );\n+    //   }\n+    // }, [data?.card?.id, showVideo]);\n \n     const posterSource = useMemo(() => {\n       const cardExtInfo = data?.card?.cardExtInfo?.value?.value;\n@@ -157,6 +178,7 @@ const VideoRender = forwardRef<{ stop: () => Promise<void> }, VideoRenderProps>(\n             source={realSource}\n             shouldPlay={true}\n             isMuted={true}\n+            onPlaybackStatusUpdate={onPlaybackStatusUpdate}\n             usePoster\n             // posterSource={posterSource}\n             // PosterComponent={PosterComponent}\n"}, {"new_path": "src/bizComponents/goods/edit/components/material/index.tsx", "old_path": "src/bizComponents/goods/edit/components/material/index.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -9,6 +9,7 @@ import Animated, { FadeIn } from 'react-native-reanimated';\n import { uploadMakePhotoImg } from '@/src/api';\n import { goodsClient } from '@/src/api/goods';\n import { Text, hideLoading, showLoading, showToast } from '@/src/components';\n+import { AiTag } from '@/src/components/aiTag';\n import { PrimaryButton } from '@/src/components/primaryButton';\n import CreditWrapper from '@/src/components/v2/credit-wrapper';\n import { usePersistFn, useScreenSize } from '@/src/hooks';\n"}, {"new_path": "src/bizComponents/goods/edit/index.tsx", "old_path": "src/bizComponents/goods/edit/index.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -160,6 +160,7 @@ export const GoodsEdit = memo(() => {\n     <Screen\n       theme=\"dark\"\n       onBack={back}\n+      withWaterMark={editMode === EditMode.Material}\n       title={title}\n       screenStyle={styles.screen}\n       headerStyle={styles.header}\n"}, {"new_path": "src/bizComponents/goodsHome/components/GoodsDetailModal.tsx", "old_path": "src/bizComponents/goodsHome/components/GoodsDetailModal.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -54,6 +54,7 @@ import { GoodsProductType } from '@/proto-registry/src/web/raccoon/goods/common_\n import { GoodsView } from './GoodsView';\n import { GuestLike } from './GuestLike';\n import { MeLike } from './MeLike';\n+import { MaterializeButton } from '@/src/components/materialize/MaterializeButton';\n \n type Props = {\n   mode: GoodsDetailShowMode;\n@@ -556,12 +557,15 @@ export const GoodsDetailModal = memo(\n               {[GoodsDetailShowMode.Me, GoodsDetailShowMode.Create].includes(\n                 mode\n               ) && (\n+                <View style={styles.publishContainer}>\n+                  <MaterializeButton logParams={{goods_id:id??'',module:`${logParams.module}`}} />\n                 <Pressable onPress={onPublish} style={styles.publish}>\n                   <View style={styles.publishInner}>\n                     <Icon icon=\"publish\" size={15} />\n                     <Text style={styles.publishText}>发布作品</Text>\n                   </View>\n-                </Pressable>\n+                  </Pressable>\n+                </View>\n               )}\n             </Animated.View>\n             {showTopLikes && (\n@@ -670,6 +674,15 @@ const styles = StyleSheet.create({\n     pointerEvents: 'none',\n     bottom: 0\n   },\n+  publishContainer: {\n+    gap:15,\n+    alignItems: 'center',\n+    zIndex: 100, \n+    flexDirection: 'row', \n+    justifyContent: 'space-between',\n+    width: '100%',\n+    paddingHorizontal: 45,\n+  },\n   goodsContainer: { pointerEvents: 'none' },\n   wrapperPosition: {\n     backgroundColor: 'transparent',\n@@ -737,8 +750,6 @@ const styles = StyleSheet.create({\n     height: 50,\n     elevation: 100,\n     flexDirection: 'row',\n-    alignItems: 'center',\n-    zIndex: 100,\n     justifyContent: 'center',\n     alignSelf: 'center'\n   },\n"}, {"new_path": "src/bizComponents/livePhotoPublish/index.tsx", "old_path": "src/bizComponents/livePhotoPublish/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -60,6 +60,7 @@ import { useEditHandler } from './useEditHandler';\n import { usePlayHandler } from './usePlayHandler';\n import { usePublishHandler } from './usePublishHandler';\n import { getGameIdFromExtInfo, isOwnAudioPlayType } from './utils';\n+import { MaterializeXiaoli } from '@/src/components/materialize/MaterializeXiaoli';\n \n const SafeAreaEdges: ExtendedEdge[] = ['top', 'bottom'];\n \n@@ -238,7 +239,7 @@ const LivePhotoPublish: FC<Props> = props => {\n         </TouchableOpacity>\n       );\n     }\n-    return null;\n+    return <MaterializeXiaoli logParams={{module:'publish', game_type:gameType}}/>;\n   });\n \n   return (\n"}, {"new_path": "src/bizComponents/livePhotoScreen/ImmersiveVideo.tsx", "old_path": "src/bizComponents/livePhotoScreen/ImmersiveVideo.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -26,6 +26,7 @@ import {\n   useSafeBottomWithNav,\n   useScreenSize\n } from '@/src/hooks';\n+import { useDevicePerformance } from '@/src/hooks/useDevicePerformance';\n import { useRunOnce } from '@/src/hooks/useOneRunning';\n import { useParams } from '@/src/hooks/useParams';\n import { useBehaviorStore } from '@/src/store/behavior';\n@@ -35,10 +36,20 @@ import { useVideoFlowComment } from '@/src/store/video-flow-comment';\n import { darkColors } from '@/src/theme';\n import { GameType, RichCardInfo, VideoFLowScene } from '@/src/types';\n import { StyleSheet, isIos } from '@/src/utils';\n+import {\n+  getLowResolutionVideoUrl,\n+  getVideoUrlFromCard\n+} from '@/src/utils/cardUtils';\n+import { checkWeakNetwork } from '@/src/utils/device/network';\n import { ReportError, errorReport } from '@/src/utils/error-log';\n import { Event } from '@/src/utils/event';\n import { formatTosUrl } from '@/src/utils/getTosUrl';\n-import { report, reportClick, reportExpo } from '@/src/utils/report';\n+import {\n+  addCommonReportParams,\n+  report,\n+  reportClick,\n+  reportExpo\n+} from '@/src/utils/report';\n import {\n   IMMER_EVENT_KEYS,\n   TImmersiveEventKeys\n@@ -66,7 +77,7 @@ import { ScaleableVideo } from './ScaleableVideo';\n import { TakeSameSheet } from './TakeSameSheet';\n import { ActionsLayer } from './actionsLayer';\n import { TVIDEO_FLOW_EVENT_KEYS, VIDEO_FLOW_EVENTS } from './constants';\n-import { startPlayTimeRecorder } from './timeRecord';\n+import { bindwidthRecorder, startPlayTimeRecorder } from './timeRecord';\n \n interface LivePhotoScreenProps {\n   data?: RichCardInfo;\n@@ -273,7 +284,13 @@ export const ImmersiveVideo = memo(\n     const timeoutRef = useRef<NodeJS.Timeout>();\n     const isPlayingRef = useRef(false);\n     const retryTimeRef = useRef(0);\n-    const safeBottom = safePaddingBottom || useSafeBottomArea(20);\n+    const { isLowEndDevice } = useDevicePerformance();\n+    const isWeakNewwork = useMemo(\n+      () => checkWeakNetwork(bindwidthRecorder.getEWMA()),\n+      []\n+    );\n+    const defaultSafePadding = useSafeBottomArea(20);\n+    const safeBottom = safePaddingBottom || defaultSafePadding;\n \n     const bottomHeight = BOTTOM_TAB_HEIGHT + safeBottom;\n \n@@ -335,10 +352,35 @@ export const ImmersiveVideo = memo(\n       hideFollowGuideToast();\n     });\n \n-    const displayVideoUrl =\n-      card?.displayVideoUrl ||\n-      (card?.cardExtInfo?.value.value as LivePhotoExtInfo | undefined)\n-        ?.videoUrl;\n+    useEffect(() => {\n+      const lowResolution = Boolean(\n+        (isLowEndDevice || isWeakNewwork) && getLowResolutionVideoUrl(card)\n+      );\n+      addCommonReportParams('videofeed', {\n+        low_resolution: Number(lowResolution)\n+      });\n+    }, []);\n+\n+    const displayVideoUrl = useMemo(\n+      () => getVideoUrlFromCard(data?.card, isLowEndDevice || isWeakNewwork),\n+      [data?.card?.id]\n+    ); // isLowEndDevice不加入deps，防止清晰度切换。\n+\n+    // useEffect(() => {\n+    //   console.log(\n+    //     '### isLowEndDevice',\n+    //     isLowEndDevice,\n+    //     ', isWeakNewwork: ',\n+    //     isWeakNewwork\n+    //   );\n+    //   if (isCurrent && (isLowEndDevice || isWeakNewwork)) {\n+    //     console.log(\n+    //       '### 使用低分辨率视频',\n+    //       data?.card?.displayVideoUrl,\n+    //       displayVideoUrl\n+    //     );\n+    //   }\n+    // }, [data?.card?.id, isCurrent]);\n \n     const videoSource =\n       (shouldLoadVideo || shouldPlay) && displayVideoUrl\n@@ -398,28 +440,34 @@ export const ImmersiveVideo = memo(\n         //   card?.id,\n         //   index\n         // );\n+        const retryVideoSource = getVideoUrlFromCard(card, true);\n         if (isPlayingRef.current) {\n           clearTimeout(timeoutRef.current);\n           timeoutRef.current = undefined;\n         }\n-        if (shouldPlayRef.current && card?.id && videoSource) {\n+        if (shouldPlayRef.current && card?.id && retryVideoSource) {\n+          console.log('### 重试使用低分辩率');\n           // console.log('重试开始-----', retryTimeRef.current);\n           try {\n             await videoRef.current?.unloadAsync();\n-            await videoRef.current?.loadAsync(videoSource, {\n-              shouldPlay: shouldPlayRef.current,\n-              isMuted: isMutedRef.current,\n-              isLooping: true\n-            });\n+            await videoRef.current?.loadAsync(\n+              { uri: retryVideoSource },\n+              {\n+                shouldPlay: shouldPlayRef.current,\n+                isMuted: isMutedRef.current,\n+                progressUpdateIntervalMillis: 50,\n+                isLooping: true\n+              }\n+            );\n           } catch (error) {\n             console.log('retry error ====', error);\n             reportExpo('video_error', {\n               module: 'videofeed',\n               retryTime: retryTimeRef.current,\n-              media_uri: videoSource?.uri\n+              media_uri: retryVideoSource\n             });\n             errorReport('视频重试错误', ReportError.VIDEO_FLOW, error, {\n-              media_uri: videoSource?.uri,\n+              media_uri: retryVideoSource,\n               retryTime: retryTimeRef.current\n             });\n           }\n"}, {"new_path": "src/bizComponents/livePhotoScreen/TakeSameSheet.tsx", "old_path": "src/bizComponents/livePhotoScreen/TakeSameSheet.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -130,7 +130,8 @@ export const TakeSameSheet = ({ data, scene }: TakeSameSheetProps) => {\n       console.log(\n         '### 同款图片: ',\n         extInfo.basePhotoGameType,\n-        extInfo.templateName\n+        extInfo.templateName,\n+        extInfo.basePhotoId\n       );\n       if (extInfo && cardId) {\n         go2Create({\n@@ -145,7 +146,8 @@ export const TakeSameSheet = ({ data, scene }: TakeSameSheetProps) => {\n           gameParams: {\n             cardId,\n             // extra: card?.extra ?? '{}',\n-            photoId: extInfo.basePhotoId ?? ''\n+            photoId: extInfo.basePhotoId ?? '',\n+            photoUrl: data?.card?.displayImageUrl\n           },\n           params: {\n             source:\n"}, {"new_path": "src/bizComponents/livePhotoScreen/hooks/useVideoStatusUpdate.ts", "old_path": "src/bizComponents/livePhotoScreen/hooks/useVideoStatusUpdate.ts", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -2,6 +2,7 @@ import { useMemoizedFn } from 'ahooks';\n import { useCallback, useMemo, useRef } from 'react';\n import { KeyEqualToValue } from '@/src/types';\n import { Event } from '@/src/utils/event';\n+import { bindwidthRecorder } from '../timeRecord';\n import { AVPlaybackStatus } from '@step.ai/expo-av';\n \n export type TVIDEO_EVENTS_KEYS =\n@@ -53,12 +54,16 @@ export const useVideoStatusUpdate = () => {\n   const eventBus = useMemo(() => new Event<TVIDEO_EVENTS_KEYS>(), []);\n   const loadStateRef = useRef<boolean>(false);\n   // 只能使用hack方式判断启播\n+  const { updater } = bindwidthRecorder;\n   const playStateRef = useRef<PlayState>(PlayState.initial);\n   const onPlaybackStatusUpdate = useMemoizedFn((state: AVPlaybackStatus) => {\n     if (!state.isLoaded) {\n       loadStateRef.current = false;\n       eventBus.emit(VIDEO_EVENTS.error, state);\n     } else {\n+      if (state?.loadSpeed) {\n+        updater(state.loadSpeed);\n+      }\n       if (!loadStateRef.current) {\n         loadStateRef.current = true;\n         eventBus.emit(VIDEO_EVENTS.loaded, state);\n"}, {"new_path": "src/bizComponents/livePhotoScreen/timeRecord.ts", "old_path": "src/bizComponents/livePhotoScreen/timeRecord.ts", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -1,14 +1,36 @@\n-export const recorderFactory = (initialNumber = 3000) => {\n-  let recordTimes = 0;\n-  let total = 0;\n+import { BINDWIDTH_TIER } from '@/src/utils/device/network';\n+\n+/**\n+ * 指数加权移动平均：S[n] = alpha * a[n] + S[n] * (1 - alpha)\n+ * @param initialNumber 初始值\n+ * @param windowSize 移动平均的窗口大小\n+ * @param alpha 指数加权移动平均因子\n+ * @returns\n+ */\n+export const recorderFactory = (\n+  initialNumber = 3000,\n+  windowSize = 5,\n+  alpha = 0.5\n+) => {\n+  const window: number[] = [];\n+  let ewma = initialNumber;\n   return {\n-    updater: (duration: number) => {\n-      total += duration;\n-      recordTimes += 1;\n+    updater: (current: number) => {\n+      window.push(current);\n+      if (window.length > windowSize) {\n+        window.shift();\n+      }\n+      ewma = current * alpha + ewma * (1 - alpha);\n+    },\n+    getAVG: () => {\n+      if (window.length === 0) return initialNumber;\n+      const sum = window.reduce((acc, val) => acc + val, 0);\n+      return Math.round(sum / window.length);\n     },\n-    getAVG: () =>\n-      recordTimes === 0 ? initialNumber : Math.round(total / recordTimes)\n+    getEWMA: () => ewma\n   };\n };\n \n export const startPlayTimeRecorder = recorderFactory();\n+\n+export const bindwidthRecorder = recorderFactory(BINDWIDTH_TIER.THIRD_G);\n"}, {"new_path": "src/bizComponents/playgoundScreen/meme/MemePublish.tsx", "old_path": "src/bizComponents/playgoundScreen/meme/MemePublish.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -22,6 +22,7 @@ import { checkSecurity } from '@/src/api/utils';\n import { ErrorRes } from '@/src/api/websocket/stream_connect';\n import { TopicSelector } from '@/src/bizComponents/topicSelector';\n import { hideLoading, showLoading, showToast } from '@/src/components';\n+import { AiTag } from '@/src/components/aiTag';\n import { SaveButton } from '@/src/components/album/SaveButton';\n import { BounceView } from '@/src/components/animation';\n import { CustomBlurView } from '@/src/components/image/CustomBlurView';\n@@ -231,7 +232,7 @@ export default function MemePublish({\n           StyleSheet.rowStyle,\n           {\n             position: 'absolute',\n-            bottom: +(paddingBottom || 0) + 8,\n+            bottom: +(paddingBottom || 0) + 15,\n             width: '100%'\n           }\n         ]}\n@@ -344,6 +345,7 @@ export default function MemePublish({\n           setTopics([]);\n           safeGoBack();\n         }}\n+        withWaterMark\n         safeAreaEdges={['top']}\n       >\n         <KeyboardAvoidingView\n"}, {"new_path": "src/bizComponents/rankListScreen/RankListButton.tsx", "old_path": "src/bizComponents/rankListScreen/RankListButton.tsx", "deleted_file": true, "new_file": false, "renamed_file": false, "diff": "@@ -152,23 +152,6 @@ export const RankListButton = memo(function RankListButton() {\n         />\n         {hasNewRank && <View style={styles.redDot} />}\n       </TouchableOpacity>\n-      {!disableSchoolRank && !schoolRankVisited && (\n-        <TouchableOpacity\n-          onPress={nav2SchoolRank}\n-          style={{\n-            position: 'absolute',\n-            width: 113,\n-            height: 61,\n-            right: -16,\n-            bottom: -48\n-          }}\n-        >\n-          <Image\n-            source={SCHOOL_RANK_BUBBLE}\n-            style={{ width: '100%', height: '100%' }}\n-          />\n-        </TouchableOpacity>\n-      )}\n     </View>\n   );\n });\n"}, {"new_path": "src/bizComponents/rankListScreen/index.tsx", "old_path": "src/bizComponents/rankListScreen/index.tsx", "deleted_file": true, "new_file": false, "renamed_file": false, "diff": "@@ -595,7 +595,6 @@ export function RankListScreen({\n           bottom: 0\n         }}\n       />\n-      <SchoolRankEntry />\n     </Screen>\n   );\n }\n"}, {"new_path": "src/bizComponents/role-create/generateRole/intex.tsx", "old_path": "src/bizComponents/role-create/generateRole/intex.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -14,6 +14,7 @@ import Carousel, {\n import { ErrorRes } from '@/src/api/websocket/stream_connect';\n import UGCRolePhotoSet from '@/src/bizComponents/role-create/ugcRolePhotoSet';\n import { Icon, Text, showToast } from '@/src/components';\n+import { AiTag } from '@/src/components/aiTag';\n import { BaseModal } from '@/src/components/modal/BaseModal';\n import { Confirm } from '@/src/components/popup/confirmModalGlobal/CustomConfirm';\n import CreditWrapper from '@/src/components/v2/credit-wrapper';\n@@ -345,6 +346,7 @@ export const UGCRoleSelectModal = ({\n             </GestureHandlerRootView>\n           </GestureDetector>\n         </Animated.View>\n+        <AiTag />\n       </View>\n       {confirmVisible && (\n         <Confirm\n"}, {"new_path": "src/components/aiTag/index.tsx", "old_path": "src/components/aiTag/index.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -0,0 +1,49 @@\n+import { useEffect, useState } from 'react';\n+import { Platform, View } from 'react-native';\n+import { useKeyboard } from '@/src/hooks';\n+import { StyleSheet, isIos } from '@/src/utils';\n+import { Text } from '../text';\n+\n+export const AiTag = () => {\n+  const nativeShowKeyBoard = useKeyboard();\n+  const [showKeyboard, setShowKeyboard] = useState(false);\n+  useEffect(() => {\n+    if (Platform.OS === 'android') {\n+      setShowKeyboard(nativeShowKeyBoard);\n+    }\n+  }, [nativeShowKeyBoard]);\n+  return (\n+    <View\n+      style={[\n+        styles.container,\n+        {\n+          bottom: isIos ? 15 : 0\n+        }\n+      ]}\n+    >\n+      <Text style={[styles.text, { opacity: showKeyboard ? 0 : 1 }]}>\n+        内容由AI生成\n+      </Text>\n+    </View>\n+  );\n+};\n+\n+const styles = StyleSheet.create({\n+  container: {\n+    zIndex: 5000,\n+    position: 'absolute',\n+    width: '100%',\n+    alignItems: 'center',\n+    justifyContent: 'center',\n+    left: 0,\n+    right: 0,\n+    bottom: 15\n+  },\n+  text: {\n+    fontSize: 10,\n+    letterSpacing: -0.3,\n+    fontFamily: 'PingFang SC',\n+    color: 'rgba(255, 255, 255, 0.2)',\n+    textAlign: 'left'\n+  }\n+});\n"}, {"new_path": "src/components/icons/icon.tsx", "old_path": "src/components/icons/icon.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -130,6 +130,7 @@ export function Icon(props: IconProps) {\n }\n \n export const iconRegistry = {\n+  materialize: require('@Assets/icon/materize-icon.png'),\n   add_gray_bg: require('@Assets/icon/ip-list-add.png'),\n   flip: require('@Assets/icon/makephoto/icon-flip.png'),\n   puzzle: require('@Assets/icon/makephoto/puzzle.png'),\n"}, {"new_path": "src/components/makePhoto/bottomTab/index.tsx", "old_path": "src/components/makePhoto/bottomTab/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -172,8 +172,8 @@ export const BottomTab = memo(() => {\n   });\n \n   useEffect(() => {\n+    console.log('photoLoading----', photoLoading);\n     if (photoLoading) {\n-      console.log('changePageState effect', Date.now());\n       changePageState(PageState.effect);\n     }\n   }, [photoLoading]);\n"}, {"new_path": "src/components/makePhoto/emojiPannel/EmojiPrview/index.tsx", "old_path": "src/components/makePhoto/emojiPannel/EmojiPrview/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -545,7 +545,7 @@ function PreviewBottom({\n           width: width - 32,\n           left: 16,\n           right: 16,\n-          bottom: $containerInsets.paddingBottom ? 0 : 8\n+          bottom: 20\n         },\n         $animateStyle,\n         $customStyle\n"}, {"new_path": "src/components/makePhoto/emojiPannel/index.tsx", "old_path": "src/components/makePhoto/emojiPannel/index.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -194,6 +194,7 @@ export function EmojiPannel(props: PanelProps) {\n       theme=\"dark\"\n       title=\"炖表情\"\n       onBack={handleBack}\n+      withWaterMark={pageState === PageState.preview}\n       rightStyle={$rightWrap}\n       backgroundView={\n         <PannelBg customBg={BG} loading={loading} showLoading={showLoading} />\n"}, {"new_path": "src/components/makePhoto/loadingView/index.tsx", "old_path": "src/components/makePhoto/loadingView/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -84,9 +84,10 @@ export const LoadingView = forwardRef(\n       reset\n     }));\n \n-    const reset = useCallback(() => {\n+    const reset = useCallback(async () => {\n+      console.log('reset----');\n       resetRef.current = true;\n-      videoRef.current?.reset();\n+      await videoRef.current?.reset();\n       useMakePhotoStoreV2.setState({\n         photoLoading: false\n       });\n@@ -230,7 +231,8 @@ export const LoadingView = forwardRef(\n       }\n     });\n     const onAllFinished = usePersistFn(() => {\n-      videoRef.current?.reset();\n+      console.log('onAllFinished----');\n+      reset();\n       finishedRef.current = true;\n     });\n \n@@ -264,8 +266,8 @@ export const LoadingView = forwardRef(\n                 top: Number($containerInsets.paddingTop) + 10,\n                 left: 16\n               }}\n-              onPress={() => {\n-                reset();\n+              onPress={async () => {\n+                await reset();\n                 useMakePhotoStoreV2.getState().changePageState(PageState.diy);\n               }}\n             >\n"}, {"new_path": "src/components/makePhoto/previewView/PreviewViewSimple.tsx", "old_path": "src/components/makePhoto/previewView/PreviewViewSimple.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -313,6 +313,7 @@ export const PreviewViewSimple = memo(() => {\n             width: '100%',\n             height: '100%'\n           }}\n+          withWaterMark\n           headerStyle={{\n             height: HEADER_HEIGHT\n           }}\n"}, {"new_path": "src/components/makePhoto/previewView/components/BottomActions.tsx", "old_path": "src/components/makePhoto/previewView/components/BottomActions.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -30,13 +30,14 @@ import {\n   ACTIONS_HEIGHT,\n   ActionsConfig,\n   TemplateActions\n-} from './TemplateActions';\n+} from './TemplateActions'; \n+import { SaveToAlbumLarge } from './previewItem/SaveToAlbumLarge';\n \n const isIos = Platform.OS === 'ios';\n \n export const PANEL_HEIGHT = isIos ? 235 : 215;\n-export const BOTTOM_MARGIN = isIos ? 40 : 10;\n-const GAP_HEIGHT = 12;\n+export const BOTTOM_MARGIN = isIos ? 40 : 20;\n+const GAP_HEIGHT = 12;  \n const BUTTON_HEIGHT = 44;\n \n export const BOTTOM_ACTION_HEIGHT =\n@@ -52,9 +53,11 @@ export const BottomActions = memo(\n     onTextFocus,\n     onAddPuzzle,\n     onChangeIndex\n+    ,onSaveImage\n   }: {\n     currentIndex: number;\n     onNext: () => void;\n+    onSaveImage: () => void;\n     onShowPanel: () => void;\n     onHidePanel: () => void;\n     onCreateLive: () => void;\n@@ -132,9 +135,10 @@ export const BottomActions = memo(\n       const renderActionBtn = () => {\n         const text = photoLiveTemplate?.id ? '用当前图片生成' : '去发布';\n         return (\n-          <View style={[{ width: '100%' }, StyleSheet.centerStyle]}>\n+          <View style={[StyleSheet.centerStyle,{ width: '100%', gap:8, flexDirection:'row' }]}>\n+            <SaveToAlbumLarge onSaveImage={onSaveImage} />\n             <PrimaryButton\n-              width={330}\n+              width={220}\n               height={BUTTON_HEIGHT}\n               useDp2px\n               primaryBgProps={{\n"}, {"new_path": "src/components/makePhoto/previewView/components/previewItem/SaveToAlbumLarge.tsx", "old_path": "src/components/makePhoto/previewView/components/previewItem/SaveToAlbumLarge.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -0,0 +1,43 @@\n+import * as React from 'react';\n+import { TouchableOpacity } from 'react-native-gesture-handler';\n+import { Icon } from '@/src/components/icons';\n+import { StyleSheet } from '@/src/utils';\n+import { Text } from '@Components/text';\n+\n+type Props = {\n+  onSaveImage: () => void;\n+};\n+\n+export const SaveToAlbumLarge = React.memo(({ onSaveImage }: Props) => {\n+  return (\n+    <TouchableOpacity\n+      onPress={onSaveImage}\n+      style={[styles.parent, StyleSheet.rowStyle]}\n+    >\n+      <Icon size={16} icon=\"download\" />\n+      <Text style={styles.text}>我的图集</Text>\n+    </TouchableOpacity>\n+  );\n+});\n+\n+const styles = StyleSheet.create({\n+  text: { \n+    fontSize: 14,\n+    lineHeight: 18,\n+    fontWeight: \"600\",\n+    fontFamily: \"PingFang SC\",\n+    color: \"rgba(255, 255, 255, 0.9)\",\n+    textAlign: \"center\"\n+  },\n+  parent: {\n+      borderRadius: 100,\n+      width: 110,\n+      backgroundColor: 'rgba(255, 255, 255, 0.08)',\n+      paddingLeft: 16, \n+      paddingRight: 18,\n+      gap: 4,\n+      height: 42,\n+      alignItems: 'center',\n+      justifyContent: 'center'\n+  }\n+});\n"}, {"new_path": "src/components/makePhoto/previewView/index.tsx", "old_path": "src/components/makePhoto/previewView/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -25,6 +25,7 @@ import { dp2px } from '@/src/utils';\n import { Image } from '@Components/image';\n import { Screen } from '@Components/screen';\n import { StyleSheet } from '@Utils/StyleSheet';\n+import { AiTag } from '../../aiTag';\n import { GuideWrapper, showGuide } from '../../guide';\n import MadePhotoGuide, {\n   useMakePhotoGuide\n@@ -41,6 +42,7 @@ import { useShallow } from 'zustand/react/shallow';\n import { PREVIEW_PUZZLE } from './types';\n import { useImageSize } from './useImageSize';\n import { usePreviewHandler } from './usePreviewHandler';\n+import { MaterializeXiaoli } from '../../materialize/MaterializeXiaoli';\n \n const ADJUST_TEMPLATE = require('@Assets/makephoto/adjust-template.png');\n const HEADER_HEIGHT = dp2px(44);\n@@ -149,8 +151,9 @@ export const PreviewView = memo((props: PreviewViewProps) => {\n         headerStyle={{\n           height: HEADER_HEIGHT\n         }}\n+        withWaterMark\n         headerLeft={() => <Credits />}\n-        headerRight={() => <SaveToAlbum onSaveImage={saveImage} />}\n+        headerRight={() => <MaterializeXiaoli/>}\n         onBack={back}\n       >\n         <TemplateGuide />\n@@ -275,6 +278,7 @@ export const PreviewView = memo((props: PreviewViewProps) => {\n                 ? GROUP_MAKE_PHOTO_COUNT + currentIndex\n                 : currentIndex - 1\n           }\n+          onSaveImage={saveImage}\n           onNext={publish}\n           onShowPanel={onPanelShow}\n           onHidePanel={onPanelHide}\n"}, {"new_path": "src/components/makePhoto/roleSelector/IPRoleSelector.tsx", "old_path": "src/components/makePhoto/roleSelector/IPRoleSelector.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -305,7 +305,7 @@ const styles = StyleSheet.create({\n   },\n   roleListOuter: {\n     flex: 1,\n-    marginLeft: isIos ? 82 : 86\n+    marginLeft: 82\n   },\n   listOuter: { flex: 1, marginBottom: 70 },\n   containerOuter: {\n"}, {"new_path": "src/components/makePhoto/roleSelector/RoleList.tsx", "old_path": "src/components/makePhoto/roleSelector/RoleList.tsx", "deleted_file": true, "new_file": false, "renamed_file": false, "diff": "@@ -201,7 +201,6 @@ export const RoleList = forwardRef<\n   );\n   return (\n     <FlatList\n-      style={{ flex: 1, paddingRight: 8 }}\n       scrollEventThrottle={100}\n       ref={scrollRef}\n       initialNumToRender={9}\n"}, {"new_path": "src/components/materialize/MaterializeButton.tsx", "old_path": "src/components/materialize/MaterializeButton.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -0,0 +1,41 @@\n+import { StyleSheet } from \"@/src/utils\";\n+import { memo } from \"react\";\n+import { TouchableOpacity } from \"react-native\";\n+import { Icon } from \"../icons\";\n+import { Text } from \"../text\"; \n+import { <PERSON>minder } from \"../reminder\";\n+import { MaterializeParams, useMaterialize } from \"./useMaterialize\";\n+\n+export const MaterializeButton = memo(((params:MaterializeParams) => {\n+    const { openMaterializeForm } = useMaterialize(params);\n+\n+    return (\n+        <TouchableOpacity style={styles.container} onPress={openMaterializeForm}>\n+            <Icon icon=\"materialize\" size={15} />\n+            <Text style={styles.text}>一键做实物</Text>\n+            <Reminder text=\"谷子变实体\" style={{paddingTop:1}} top={-4} right={-10} />\n+        </TouchableOpacity>\n+    )\n+}));\n+\n+const styles = StyleSheet.create({\n+    container: {\n+        backgroundColor: \"#2a2a2e\",\n+        borderRadius: 40,\n+        width: '50%',\n+        flexDirection: \"row\",\n+        paddingHorizontal: 20,\n+        paddingVertical: 15,\n+        gap:6,\n+        justifyContent: 'center',\n+        alignItems: 'center',\n+    },\n+    text: {\n+        color: 'rgba(255, 255, 255, 0.90)',\n+        textAlign: 'center',\n+        fontSize: 15,\n+        fontStyle: 'normal',\n+        fontWeight: '600',\n+        lineHeight: 20,\n+    }\n+})\n\\ No newline at end of file\n"}, {"new_path": "src/components/materialize/MaterializeXiaoli.tsx", "old_path": "src/components/materialize/MaterializeXiaoli.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -0,0 +1,72 @@\n+import { StyleSheet } from \"@/src/utils\";\n+import { memo } from \"react\";\n+import { TouchableOpacity } from \"react-native\"; \n+import { Text } from \"../text\";  \n+import { MaterializeParams, useMaterialize } from \"./useMaterialize\"; \n+import { Image } from \"../image\";\n+const IMAGE_XIAOLI = require('@Assets/icon/materize-xiaoli.png');\n+const YELLOW_STAR = require('@Assets/icon/yellow-star.png');\n+export const MaterializeXiaoli = memo((params:MaterializeParams) => {\n+    const { openMaterializeForm } = useMaterialize(params);\n+\n+    return (\n+        <TouchableOpacity style={styles.container} onPress={openMaterializeForm}>\n+     \n+            <Text style={styles.text}>一键做实物</Text>\n+            <Image\n+                source={IMAGE_XIAOLI} \n+                style={styles.imageXiaoli}\n+                resizeMode=\"contain\"\n+            />\n+            <Image\n+                source={YELLOW_STAR} \n+                style={styles.imageYellowStar}\n+                resizeMode=\"contain\"\n+            />\n+        </TouchableOpacity>\n+    );\n+});\n+\n+const styles = StyleSheet.create({\n+    container: {\n+        backgroundColor: \"#ff6a3b\",\n+        borderTopRightRadius: 19,\n+        borderBottomRightRadius: 19,\n+        height:28,\n+        flexDirection: \"row\",\n+        paddingRight: 9,\n+        borderColor: '#fff',\n+        paddingLeft:25,\n+        borderWidth: 1, \n+        alignItems:'center',\n+        gap:2.5, \n+    },\n+    imageXiaoli: {\n+        position:'absolute',\n+        width: 32,\n+        left:-10,\n+        height: 33,\n+        \n+    },\n+    imageYellowStar: {\n+        position:'absolute',\n+        width: 10,\n+        left: -17,\n+        top:-5,\n+        height: 10,\n+    },\n+    text: { \n+        fontSize: 12,\n+        lineHeight: 18,\n+        fontWeight: \"600\",\n+        fontFamily: \"PingFang SC\",\n+        color: \"rgba(255, 255, 255, 0.9)\",\n+        textAlign: \"left\",\n+        textShadowColor: \"rgba(255, 37, 1, 0.28)\",\n+        textShadowOffset: {\n+        width: 0,\n+        height: 1\n+        },\n+        textShadowRadius: 1\n+    }\n+})\n\\ No newline at end of file\n"}, {"new_path": "src/components/materialize/MaterlizeMask.tsx", "old_path": "src/components/materialize/MaterlizeMask.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -0,0 +1,52 @@\n+import { StyleSheet } from \"@/src/utils\";\n+import { memo } from \"react\";\n+import { TouchableOpacity } from \"react-native\";\n+import { Icon } from \"../icons\";\n+import { Text } from \"../text\"; \n+import { MaterializeParams, useMaterialize } from \"./useMaterialize\";\n+import { Gesture, GestureDetector } from \"react-native-gesture-handler\";\n+import { runOnJS } from \"react-native-reanimated\";\n+\n+export const MaterlizeMask = memo(((params:MaterializeParams) => {\n+    const { openMaterializeForm } = useMaterialize(params);\n+    const gesture = Gesture.Tap().onBegin(() => {\n+        runOnJS(openMaterializeForm)();\n+      });\n+    return (\n+        <GestureDetector gesture={gesture}>\n+            <TouchableOpacity style={styles.container}>\n+                <Icon icon=\"materialize\" size={15} />\n+                <Text style={styles.text}>一键做实物 · 试试把图片换成实体谷子吧 </Text>\n+                <Icon icon=\"mall_arrow_right\" size={20}  color={'#fff'} containerStyle={styles.arrowRight}/>\n+            </TouchableOpacity>\n+        </GestureDetector>\n+    )\n+}));\n+\n+const styles = StyleSheet.create({\n+    container: {\n+        position: 'absolute',\n+        bottom: 0,\n+        backgroundColor: \"#00000066\", \n+        width: '100%',\n+        height: 38,\n+        flexDirection: \"row\",\n+        paddingStart:13,\n+        gap: 6,\n+        justifyContent:'flex-start',\n+        alignItems: 'center',\n+    },\n+    arrowRight: {  \n+        position: 'absolute',\n+        right: 12,\n+    },\n+    text: {  \n+        fontSize: 13,\n+        fontWeight: \"500\",\n+        fontFamily: \"PingFang SC\",\n+        color: \"#fff\",\n+        textAlign: \"left\",\n+        display: \"flex\",\n+        alignItems: \"center\", \n+    }\n+})\n\\ No newline at end of file\n"}, {"new_path": "src/components/materialize/useMaterialize.ts", "old_path": "src/components/materialize/useMaterialize.ts", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -0,0 +1,34 @@\n+import { Linking } from 'react-native';\n+import { useCallback } from 'react';\n+import { errorReport, ReportError } from '@/src/utils/error-log';\n+import { showToast } from '../toast';\n+import { reportClick } from '@/src/utils/report';\n+\n+const FORM_URL = 'https://wvixbzgc0u7.feishu.cn/share/base/form/shrcndPQVhq9knSiBSSjxbGllQB';\n+\n+export type MaterializeParams = {logParams?:Record<string,string>}\n+\n+export const useMaterialize = (params:MaterializeParams) => {\n+    const showErrorToast = useCallback(() => {\n+        showToast('出错啦～无法打开页面')\n+    },[]);\n+    const openMaterializeForm = useCallback(async () => {\n+        console.log('openMaterializeForm',params.logParams);\n+        reportClick('create_button', { ...(params.logParams ?? {}) });\n+        try {\n+            const supported = await Linking.canOpenURL(FORM_URL);\n+            if (supported) {\n+                await Linking.openURL(FORM_URL);\n+            } else {\n+                showErrorToast();\n+            }\n+        } catch (error) {\n+          showErrorToast();\n+           errorReport('openMaterializeForm',ReportError.REQUEST,error)\n+        }\n+    }, []);\n+\n+    return {\n+        openMaterializeForm,\n+    };\n+};\n\\ No newline at end of file\n"}, {"new_path": "src/components/publishEntry/GameEntryCard.tsx", "old_path": "src/components/publishEntry/GameEntryCard.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -7,10 +7,11 @@ import { Image } from '@/src/components';\n import { Text } from '@/src/components/text';\n import { LOGIN_SCENE } from '@/src/constants';\n import { useAuthState, usePersistFn } from '@/src/hooks';\n+import { useDevicePerformance } from '@/src/hooks/useDevicePerformance';\n import { useFontStore } from '@/src/store/font';\n import { useResourceStore } from '@/src/store/resource';\n import { typography } from '@/src/theme';\n-import { dp2px, isLowEndDevice } from '@/src/utils';\n+import { dp2px } from '@/src/utils';\n import { formatTosUrl } from '@/src/utils/getTosUrl';\n import { Reminder } from '../reminder';\n import { useIsFocused } from '@react-navigation/native';\n@@ -92,6 +93,7 @@ export const GameEntryCard = ({\n       getResource: state.getResource\n     }))\n   );\n+  const { isLowEndDevice } = useDevicePerformance();\n   const layoutInfo = LayoutInfo[scene];\n   const isFocus = useIsFocused();\n   const [error, setError] = useState(false);\n"}, {"new_path": "src/components/publishEntry/index.tsx", "old_path": "src/components/publishEntry/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -49,6 +49,7 @@ import { Source, reportClick, reportExpo } from '@/src/utils/report';\n import { Text } from '@Components/text';\n import { useAuthState } from '../../hooks/useAuthState';\n import { Icon } from '../icons';\n+import { MaterializeButton } from '../materialize/MaterializeButton';\n import { Image } from '../image';\n import { CustomBlurView } from '../image/CustomBlurView';\n import { SkinnedImage } from '../skin/SkinnedImage';\n@@ -373,7 +374,9 @@ function PublishEntryCom({\n                   </Animated.View>\n \n                   {publishEntry.visible ? (\n-                    <Pressable\n+                    <View style={$publishContainerWrapper}>\n+                      <MaterializeButton logParams={{module:'feed'}} />\n+                      <Pressable\n                       onPress={() => {\n                         publishEntry.onPress?.(publishEntry, { lishi: '1' });\n                         setVisible(false);\n@@ -387,8 +390,8 @@ function PublishEntryCom({\n                         <Text style={$publishText}>发布作品</Text>\n                       </Animated.View>\n                     </Pressable>\n+                  </View>\n                   ) : null}\n-\n                   {visible ? (\n                     <Pressable onPress={onClose}>\n                       <Animated.View\n@@ -450,7 +453,16 @@ const $entriesContainer: ViewStyle = {\n   justifyContent: 'flex-end',\n   height: '100%'\n };\n-\n+const $publishContainerWrapper: ViewStyle = {\n+    gap: 15,\n+    marginTop: 40,\n+    alignItems: 'center',\n+    zIndex: 100, \n+    flexDirection: 'row', \n+    justifyContent: 'space-between',\n+    width: '100%',\n+    paddingHorizontal: 45,\n+  }\n const $drawingBg: ImageStyle = {\n   width: dp2px(375),\n   height: dp2px(114),\n@@ -583,7 +595,6 @@ const $publishContainer: ViewStyle = {\n   paddingLeft: 20,\n   paddingRight: 24,\n   borderRadius: 40,\n-  marginTop: 40\n };\n \n const $publishText: TextStyle = {\n"}, {"new_path": "src/components/screen/index.tsx", "old_path": "src/components/screen/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -41,6 +41,7 @@ import { Text } from '@Components/text';\n import { StyleSheet } from '@Utils/StyleSheet';\n import dp2px from '../../utils/dp2px';\n import { SkeletonLoader } from '../SkeletonLoader';\n+import { AiTag } from '../aiTag';\n import { ScrollViewRef } from '@/app/detail/[id]';\n \n interface BaseScreenProps {\n@@ -71,6 +72,8 @@ interface BaseScreenProps {\n \n   maskAreaShown?: boolean;\n \n+  withWaterMark?: boolean;\n+\n   backButton?: boolean | ReactNode;\n \n   loading?: boolean;\n@@ -303,6 +306,7 @@ export const Screen = (props: ScreenProps) => {\n     keyboardOffset = 0,\n     safeAreaEdges = ['top', 'bottom'],\n     StatusBarProps,\n+    withWaterMark = false,\n     headerShown = true,\n     maskAreaShown = true,\n     backgroundView,\n@@ -335,42 +339,45 @@ export const Screen = (props: ScreenProps) => {\n   };\n \n   return (\n-    <View style={[$containerStyle, wholePageStyle]}>\n-      {backgroundView}\n-      <View\n-        style={[\n-          { position: 'relative', flex: 1, overflow: 'hidden' },\n-          $containerInsets,\n-          $screenStyleOverride\n-        ]}\n-        onStartShouldSetResponder={\n-          needDismissKeboard\n-            ? () => {\n-                Keyboard.dismiss();\n-                return false;\n-              }\n-            : undefined\n-        }\n-      >\n-        <StatusBar\n-          style={theme === 'light' ? 'dark' : 'light'}\n-          {...StatusBarProps}\n-        />\n-        {headerShown ? <Header {...props} themeColors={themeColors} /> : null}\n-        {useKeyboardAvoid ? (\n-          <KeyboardAvoidingView\n-            behavior={isIos ? 'padding' : undefined}\n-            keyboardVerticalOffset={keyboardOffset}\n-            {...KeyboardAvoidingViewProps}\n-            style={[{ flex: 1 }, KeyboardAvoidingViewProps?.style]}\n-          >\n-            {renderMain()}\n-          </KeyboardAvoidingView>\n-        ) : (\n-          <View style={flex1Style}>{renderMain()}</View>\n-        )}\n+    <>\n+      <View style={[$containerStyle, wholePageStyle]}>\n+        {backgroundView}\n+        <View\n+          style={[\n+            { position: 'relative', flex: 1, overflow: 'hidden' },\n+            $containerInsets,\n+            $screenStyleOverride\n+          ]}\n+          onStartShouldSetResponder={\n+            needDismissKeboard\n+              ? () => {\n+                  Keyboard.dismiss();\n+                  return false;\n+                }\n+              : undefined\n+          }\n+        >\n+          <StatusBar\n+            style={theme === 'light' ? 'dark' : 'light'}\n+            {...StatusBarProps}\n+          />\n+          {headerShown ? <Header {...props} themeColors={themeColors} /> : null}\n+          {useKeyboardAvoid ? (\n+            <KeyboardAvoidingView\n+              behavior={isIos ? 'padding' : undefined}\n+              keyboardVerticalOffset={keyboardOffset}\n+              {...KeyboardAvoidingViewProps}\n+              style={[{ flex: 1 }, KeyboardAvoidingViewProps?.style]}\n+            >\n+              {renderMain()}\n+            </KeyboardAvoidingView>\n+          ) : (\n+            <View style={flex1Style}>{renderMain()}</View>\n+          )}\n+        </View>\n       </View>\n-    </View>\n+      {withWaterMark && <AiTag />}\n+    </>\n   );\n };\n \n"}, {"new_path": "src/components/video/index.tsx", "old_path": "src/components/video/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -32,7 +32,7 @@ export interface VideoHandle {\n   next: () => Promise<void>;\n   play: () => void;\n   show: () => void;\n-  reset: () => void;\n+  reset: () => Promise<void>;\n   hide: () => void;\n   changeSources: (payload: VideoItem[]) => void;\n }\n@@ -72,13 +72,9 @@ export const Video = forwardRef<VideoHandle, VideoProps>((props, ref) => {\n   }));\n \n   const initLoad = usePersistFn(async () => {\n-    await videoBackRef.current\n-      ?.loadAsync(getVideoSource()[0].source, {\n-        shouldPlay: false\n-      })\n-      .then(() => {\n-        onLoadFinish(false);\n-      });\n+    await videoBackRef.current?.loadAsync(getVideoSource()[0].source, {\n+      shouldPlay: true\n+    });\n     preload();\n   });\n   // 初始化 预加载\n@@ -102,9 +98,12 @@ export const Video = forwardRef<VideoHandle, VideoProps>((props, ref) => {\n       }\n     }\n   }\n-  function onVideoError(err) {\n-    errorReport('ExpoVideo', ReportError.MAKE_PHOTO, err);\n-    onFinish(true);\n+  async function onVideoError(err, isFront) {\n+    errorReport(\n+      'ExpoVideo ' + (isFront ? 'front' : 'back'),\n+      ReportError.MAKE_PHOTO,\n+      err\n+    );\n   }\n \n   return (\n@@ -117,9 +116,7 @@ export const Video = forwardRef<VideoHandle, VideoProps>((props, ref) => {\n           ref={videoBackRef}\n           style={[StyleSheet.absoluteFill]}\n           resizeMode={ResizeMode.COVER}\n-          // eslint-disable-next-line react/jsx-no-bind\n-          onError={onVideoError}\n-          // eslint-disable-next-line react/jsx-no-bind\n+          onError={e => onVideoError(e, false)}\n           onPlaybackStatusUpdate={onVideoFinish}\n         />\n       </Animated.View>\n@@ -129,9 +126,7 @@ export const Video = forwardRef<VideoHandle, VideoProps>((props, ref) => {\n           style={[StyleSheet.absoluteFill]}\n           ref={videoFrontRef}\n           resizeMode={ResizeMode.COVER}\n-          // eslint-disable-next-line react/jsx-no-bind\n-          onError={onVideoError}\n-          // eslint-disable-next-line react/jsx-no-bind\n+          onError={e => onVideoError(e, true)}\n           onPlaybackStatusUpdate={onVideoFinish}\n         />\n       </Animated.View>\n@@ -142,21 +137,14 @@ export const Video = forwardRef<VideoHandle, VideoProps>((props, ref) => {\n     const nextIndex = index.current + 1;\n     if (nextIndex >= getVideoSource().length) return;\n     const showFront = showFrontVideo.current;\n-    if (showFront) {\n-      videoBackLoadStatus.current = false;\n-    } else {\n-      videoFrontLoadStatus.current = false;\n-    }\n     const nextItem = getVideoSource()[nextIndex];\n+\n+    console.log('preload----', showFront, nextIndex, nextItem);\n     const nextRef = showFront ? videoBackRef : videoFrontRef;\n-    await nextRef.current?.unloadAsync();\n-    await nextRef.current\n-      ?.loadAsync(nextItem.source, {\n-        shouldPlay: false\n-      })\n-      .then(() => {\n-        onLoadFinish(!showFront);\n-      });\n+    // await nextRef.current?.unloadAsync();\n+    await nextRef.current?.loadAsync(nextItem.source, {\n+      shouldPlay: false\n+    });\n   }\n \n   async function show() {\n@@ -174,26 +162,8 @@ export const Video = forwardRef<VideoHandle, VideoProps>((props, ref) => {\n     index.current = 0;\n     showFrontVideo.current = false;\n   }\n-  function onLoadFinish(isFront) {\n-    if (isFront) {\n-      videoFrontLoadStatus.current = true;\n-    } else {\n-      videoBackLoadStatus.current = true;\n-    }\n-    if (\n-      (showFrontVideo.current && isFront) ||\n-      (!showFrontVideo.current && !isFront)\n-    ) {\n-      play();\n-    }\n-  }\n   function play() {\n-    if (\n-      (showFrontVideo.current && videoFrontLoadStatus.current) ||\n-      (!showFrontVideo.current && videoBackLoadStatus.current)\n-    ) {\n-      return getCurrentVideo()?.playAsync();\n-    }\n+    return getCurrentVideo()?.playAsync();\n   }\n \n   function getVideoSource() {\n@@ -232,15 +202,19 @@ export const Video = forwardRef<VideoHandle, VideoProps>((props, ref) => {\n       : videoBackRef.current;\n   }\n \n-  function reset() {\n+  function resetStatus() {\n     opacityFrontVal.value = 0;\n     opacityVal.value = 0;\n     index.current = 0;\n     videoBackLoadStatus.current = false;\n     videoFrontLoadStatus.current = false;\n     showFrontVideo.current = false;\n-    videoBackRef?.current?.unloadAsync(); \n-    videoFrontRef?.current?.unloadAsync();\n+  }\n+\n+  async function reset() {\n+    resetStatus();\n+    await videoBackRef?.current?.unloadAsync();\n+    await videoFrontRef?.current?.unloadAsync();\n     // resetRef.current = true;\n   }\n \n"}, {"new_path": "src/components/waterfall/WaterFall2.tsx", "old_path": "src/components/waterfall/WaterFall2.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -17,12 +17,16 @@ import {\n import { View } from 'react-native';\n import { CellCard } from '@/src/bizComponents/feedcard/cellcard';\n import { CellCardScene } from '@/src/bizComponents/feedcard/types';\n+import { bindwidthRecorder } from '@/src/bizComponents/livePhotoScreen/timeRecord';\n import { useAuthStore } from '@/src/store/authInfo';\n import { useBehaviorStore } from '@/src/store/behavior';\n import { useHistoryStore } from '@/src/store/histroy';\n import { CommonColor } from '@/src/theme/colors/common';\n import { Theme } from '@/src/theme/colors/type';\n import { CardInfo, CardType, GameType } from '@/src/types';\n+import { getVideoUrlFromCard } from '@/src/utils/cardUtils';\n+import { checkWeakNetwork } from '@/src/utils/device/network';\n+import { getIsLowEndDeviceFromCache } from '@/src/utils/device/performanceTier';\n import { ReportError, errorReport } from '@/src/utils/error-log';\n import { Event } from '@/src/utils/event';\n import { preloadVideo } from '@/src/utils/preloadVideo';\n@@ -276,12 +280,15 @@ export const WaterFall2 = forwardRef(\n             card?.type === CardType.VIDEO &&\n             cellcardScene === CellCardScene.HOME\n           ) {\n-            const uri =\n-              card?.displayVideoUrl ||\n-              (card.cardExtInfo?.value.value as LivePhotoExtInfo | undefined)\n-                ?.videoUrl;\n+            const uri = getVideoUrlFromCard(\n+              card,\n+              getIsLowEndDeviceFromCache() ||\n+                checkWeakNetwork(bindwidthRecorder.getEWMA())\n+            );\n             console.log('### preload Video:  ', uri);\n-            uri && preloadVideo([{ uri }]);\n+            if (uri) {\n+              preloadVideo([{ uri }]);\n+            }\n           }\n         });\n       },\n"}, {"new_path": "src/hooks/useChangeRoute.ts", "old_path": "src/hooks/useChangeRoute.ts", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -814,6 +814,7 @@ export const useChangeRoute = () => {\n               const params: Partial<WebviewScreenParams> = {\n                 url: `${DECISION_H5_URL}?id=${target}&${qs.stringify({ ...queryParams, cardId })}`,\n                 title: '',\n+                withAiTag: '1',\n                 webviewParams: JSON.stringify(decisionWebviewParams),\n                 screenParams: JSON.stringify(decisionWebviewScreenParams),\n                 ...queryParams\n"}, {"new_path": "src/hooks/useDevicePerformance.ts", "old_path": "src/hooks/useDevicePerformance.ts", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -1,8 +1,9 @@\n-import { useEffect, useState } from 'react';\n+import { useEffect, useMemo, useState } from 'react';\n import {\n   clearPerformanceCache,\n   forceRefreshDevicePerformance,\n   getDevicePerformanceDetails,\n+  getIsLowEndDeviceFromCache,\n   getManualDeviceTier,\n   setManualDeviceTier\n } from '../utils/device/performanceTier';\n@@ -77,7 +78,10 @@ interface DevicePerformanceHook {\n  * ```\n  */\n export function useDevicePerformance(): DevicePerformanceHook {\n-  const [isLowEndDevice, setIsLowEndDevice] = useState<boolean>(false);\n+  const initialIsLowEndDevice = useMemo(getIsLowEndDeviceFromCache, []);\n+  const [isLowEndDevice, setIsLowEndDevice] = useState<boolean>(\n+    initialIsLowEndDevice\n+  );\n   const [performanceDetails, setPerformanceDetails] =\n     useState<DevicePerformanceInfo | null>(null);\n   const [deviceTier, setDeviceTier] = useState<DeviceTier | null>(null);\n"}, {"new_path": "src/store/makePhotoV2.ts", "old_path": "src/store/makePhotoV2.ts", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -908,8 +908,9 @@ export const useMakePhotoStoreV2 = create<States & Actions>()((set, get) => ({\n   },\n \n   takePhoto(extraInfo) {\n+    console.log('takePhoto----');\n     const id = uuid();\n-    set({ makePhotoGenId: id });\n+    set({ makePhotoGenId: id, photos: [], regenIndex: 0 });\n     const { style, getRoles, sref, photoSize } = get();\n     const roles = getRoles();\n \n@@ -1090,9 +1091,7 @@ export const useMakePhotoStoreV2 = create<States & Actions>()((set, get) => ({\n \n     set({\n       $pendingPhoto,\n-      photoLoading: true,\n-      photos: [],\n-      regenIndex: 0\n+      photoLoading: true\n     });\n     reportTimeStart('takephoto_end-expo', 'duration');\n   },\n@@ -1103,6 +1102,7 @@ export const useMakePhotoStoreV2 = create<States & Actions>()((set, get) => ({\n \n     let resolved = 0;\n     let rejected = 0;\n+    console.log('regenPhoto----', curIdx);\n \n     set({ photoLoading: true });\n     let firstPhoto: PartialMessage<PhotoProgress> | null = null;\n"}, {"new_path": "src/utils/cardUtils.ts", "old_path": "src/utils/cardUtils.ts", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -0,0 +1,32 @@\n+import { CardInfo, PlainType } from '../types';\n+import { LivePhotoExtInfo } from '@/proto-registry/src/web/raccoon/common/livephoto_pb';\n+import { ReimagineExtInfo } from '@/proto-registry/src/web/raccoon/common/reimagine_pb';\n+import { VideoResolution } from '@/proto-registry/src/web/raccoon/common/video_pb';\n+\n+export const getVideoUrlFromCard = (\n+  card?: PlainType<CardInfo>,\n+  isLowEndDevice?: boolean\n+) => {\n+  const videoUrl = card?.displayVideoUrl || getExtVideoUrl(card?.cardExtInfo);\n+  const lowResolutionUrl = getLowResolutionVideoUrl(card) || videoUrl;\n+\n+  return isLowEndDevice && lowResolutionUrl ? lowResolutionUrl : videoUrl;\n+};\n+\n+export const getExtVideoUrl = (\n+  cardExtInfo?: PlainType<CardInfo>['cardExtInfo']\n+) => {\n+  switch (cardExtInfo?.value.case) {\n+    case 'otakudance':\n+      return cardExtInfo.value.value.video?.url;\n+    default:\n+      return (cardExtInfo?.value.value as LivePhotoExtInfo | ReimagineExtInfo)\n+        .videoUrl;\n+  }\n+};\n+\n+export const getLowResolutionVideoUrl = (card?: PlainType<CardInfo>) =>\n+  card?.cardVideos\n+    ?.filter(i => i.resolution !== VideoResolution.VIDEO_RESOLUTION_ORIGINAL)\n+    ?.sort((a, b) => Number(a.resolution) - Number(b.resolution))[0]\n+    ?.displayVideoUrl;\n"}, {"new_path": "src/utils/device/network.tsx", "old_path": "src/utils/device/network.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -12,3 +12,19 @@ export const checkNetWork = async () => {\n     showToast('小狸走丢了');\n   }\n };\n+\n+/**\n+ * KB/s\n+ */\n+export const BINDWIDTH_TIER = {\n+  EDGE: 256 / 8, // 256Kbps\n+  DSL: 1024 / 8, // 1Mbps\n+  THIRD_G: 2048 / 8, // 2Mbps\n+  FOURTH_G: 16384 / 8, // 16Mbps\n+  WIFT: 65536 / 8 // 64Mbps\n+};\n+\n+export const checkWeakNetwork = (\n+  current: number,\n+  base = BINDWIDTH_TIER.THIRD_G\n+) => current < base;\n"}, {"new_path": "src/utils/device/performanceTier.ts", "old_path": "src/utils/device/performanceTier.ts", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -447,6 +447,12 @@ export function clearPerformanceCache(): void {\n   S_MEMORY_CACHE = null;\n }\n \n+/**\n+ * 从缓存中获取，主要用于hook首次执行的初始值设置\n+ */\n+export const getIsLowEndDeviceFromCache = () =>\n+  S_MEMORY_CACHE?.info?.tier === 'low';\n+\n /**\n  * 手动设置设备等级（仅在当前App生命周期内有效）\n  * 主要用于开发调试，不会持久化到存储中\n"}, {"new_path": "src/utils/report/index.ts", "old_path": "src/utils/report/index.ts", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -298,7 +298,7 @@ export const reportExpo = (\n export const reportClick = (\n   element: string,\n   params?: Record<string, string | number | boolean | undefined> & {\n-    module: MODULE_TYPE;\n+    module?: MODULE_TYPE;\n   },\n   type?: string\n ) => {\n"}]