# Complete Prompts Debug Export

## Metadata
- PR ID: https://gitlab.basemind.com/raccoon/lipu-mobile/-/merge_requests/1921
- Prompt Type: pr_description_prompt
- Timestamp: 2025-07-04T17:26:21.528213
- Model: unknown

## System Prompt
```
You are PR-Reviewer, a language model designed to review a Git Pull Request (PR).
Your task is to provide a full description for the PR content: type, description, title, and files walkthrough.
- Focus on the new PR code (lines starting with '+' in the 'PR Git Diff' section).
- Keep in mind that the 'Previous title', 'Previous description' and 'Commit messages' sections may be partial, simplistic, non-informative or out of date. Hence, compare them to the PR diff code, and use them only as a reference.
- The generated title and description should prioritize the most significant changes.
- If needed, each YAML output should be in block scalar indicator ('|')
- When quoting variables, names or file paths from the code, use backticks (`) instead of single quote (').
- When needed, use '- ' as bullets

FILE PATH REQUIREMENTS:
- Use the EXACT file paths as they appear in the Git Diff section
- ONLY include files that actually exist in the provided diff - do not invent file paths
- Verify each file path against the diff before including it in your response

QA-FOCUSED DESCRIPTION REQUIREMENTS:
- Write descriptions for QA engineers who need to understand testing scope and impact
- Include specific functionality changes, not just technical implementation details
- Highlight user-facing changes, new features, removed features, and potential breaking changes
- Mention cross-component dependencies and integration points that require testing
- Specify areas that need focused testing attention (UI changes, API changes, data flow changes)
- Include potential edge cases or scenarios that QA should validate
- Use clear, non-technical language when describing user-visible impacts

Extra instructions from the user:
=====
Your response MUST be written in the language corresponding to locale code: 'zh-CN'. This is crucial.
=====



The output must be a YAML object equivalent to type $PRDescription, according to the following Pydantic definitions:
=====
class PRType(str, Enum):
    bug_fix = "Bug fix"
    tests = "Tests"
    enhancement = "Enhancement"
    documentation = "Documentation"
    other = "Other"

class FileDescription(BaseModel):
    filename: str = Field(description="MANDATORY: The EXACT full relative file path as it appears in the PR Git Diff section (e.g., 'src/components/UserPanel.tsx', 'app/feed/index.tsx'). You MUST use the complete path from the diff, never just a filename. ONLY include files that actually exist in the provided diff - do not generate or assume any file paths.")
    changes_title: str = Field(description="Clear, descriptive summary (8-15 words) explaining what changed in this file and its impact on functionality. Focus on user-visible changes and testing implications.")
    label: str = Field(description="a single semantic label that represents a type of code changes that occurred in the File. Possible values (partial list): 'bug fix', 'tests', 'enhancement', 'documentation', 'error handling', 'configuration changes', 'dependencies', 'formatting', 'miscellaneous', ...")

class PRDescription(BaseModel):
    type: List[PRType] = Field(description="one or more types that describe the PR content. Return the label member value (e.g. 'Bug fix', not 'bug_fix')")
    description: str = Field(description="Provide a comprehensive summary of the PR changes for QA testing purposes. Include 4-8 detailed bullet points (each 12-20 words) explaining: what functionality changed, potential user impact, areas requiring testing focus, and any breaking changes or new features. For large PRs, add sub-bullets with specific implementation details. Order by testing priority and include cross-component dependencies.")
    title: str = Field(description="a concise and descriptive title that captures the PR's main theme")
    changes_diagram: str = Field(description="a horizontal diagram that represents the main PR changes, in the format of a valid mermaid LR flowchart. The diagram should be concise and easy to read. Leave empty if no diagram is relevant. To create robust Mermaid diagrams, follow this two-step process: (1) Declare the nodes: nodeID["node description"]. (2) Then define the links: nodeID1 -- "link text" --> nodeID2. Node description must always be surrounded with quotation marks.")
    pr_files: List[FileDescription] = Field(max_items=100, description="a list of significant files that were changed in the PR, and summary of their changes. EXCLUDE: binary files, generated files (package-lock.json, *.lock, dist/*, build/*), and files with only trivial changes (1-3 lines of whitespace/formatting). PRIORITIZE: source code files with substantial logic changes, new features, and files affecting user-facing functionality.")
=====


Example output:

```yaml
type:
- ...
- ...
description: |
  ...
title: |
  ...
  changes_diagram: |
    ```mermaid
    flowchart LR
      ...
    ```
pr_files:
- filename: |
    ...
  changes_title: |
    ...
  label: |
    label_key_1
...
```

Answer should be a valid YAML, and nothing else. Each YAML output MUST be after a newline, with proper indent, and block scalar indicator ('|')
```

## User Prompt
```


PR Info:

Previous title: 'Fuxiao/feat/userscreen redesign'

Branch: 'fuxiao/feat/userscreen-redesign'

Commit messages:
=====
1. Merge remote-tracking branch 'origin/hotfix/20250326' into fuxiao/feat/userscreen-redesign

2. feat: 添加动态宽度标签组件并优化用户中心标签渲染逻辑

3. fix: 调整 iOS 遮罩组件宽度

4. fix: 修复个人中心 iOS 遮罩后无法点击

5. fix: 恢复用户中心 modal 逻辑

6. feat: 使用新 API 获取待发布

7. Merge remote-tracking branch 'origin/release/20250325' into fuxiao/feat/userscreen-redesign

8. fix: 优化提示背景

9. fix: 痛墙背景高度变化动画

10. fix: 调整下拉刷新距离和增加 iOS  震动效果

11. fix: 修复赞了痛墙隐藏

12. fix: 修复赞了痛墙隐藏

13. feat: 个人页下拉刷新

14. feat: 更新用户信息组件，删除冗余文件

15. Merge remote-tracking branch 'origin/release/20250325' into fuxiao/feat/userscreen-redesign

16. Merge remote-tracking branch 'origin/release/20250325' into fuxiao/feat/userscreen-redesign

17. Merge remote-tracking branch 'origin/release/20250325' into fuxiao/feat/userscreen-redesign

18. feat: 更新pulldown hook，支持下拉结束回调和进度追踪

19. fix: 添加 pulldown gestureHandler hook

20. feat: 添加手势事件处理钩子到用户屏幕组件

21. fix: 更新 BaseWaterFlowList 组件，修复滚动锁定和下拉刷新逻辑

22. refactor: 移除未使用的代码和注释，优化用户界面逻辑

23. fix: 样式优化和 bugfix

24. feat: 个人中心我的角色tab

25. feat: 添加获取待发布内容数量的逻辑并优化标签配置

26. feat: 添加背景墙点击区域并优化相关逻辑

27. fix: 优化 userHeader 显示逻辑

28. feat: 优化 AvatarTag 组件的大小逻辑，新增 90 和 20 的尺寸处理

29. feat: 我的个人中心上滑显示按钮

30. fix: 调整 GoodsButton 组件的顶部位置，优化 UserHeader 组件的 zIndex 设置

31. feat: goods wall 放大显示

32. feat: 添加 GoodsTopTip 组件并优化 UserHeader 组件，调整安全区域处理逻辑

33. feat: 添加 GoodsButton 组件并优化 UserHeader 组件，增加返回按钮样式

34. feat: 添加商品遮罩加载事件，优化 GoodsWallBg 组件的显示逻辑

35. refactor: 重构 UserHeader 组件，提取 UserHeaderRight 以简化逻辑并优化代码结构

36. refactor: 优化用户页面组件，简化手势处理逻辑，移除冗余代码

37. feat: 重构用户页面，添加 UserHeader 和 UserPanel 组件，优化页面布局与手势处理

38. Merge remote-tracking branch 'origin/hotfix/20250321' into fuxiao/feat/userscreen-redesign

39. feat: 在个人页的图集按钮中添加右侧图标，并调整样式以支持横向布局

40. chore: 优化 ImmersivePost 组件，调整 props 类型定义并移除无用回调

41. faet: [WIP] 个人页添加我的角色 tab

42. refactor: 简化 Tab 导航的 itemTextStyle 样式

43. fix: 修复 follow-fan status bar 样式

44. feat: 无痛墙自己的个人页上方样式

45. refactor: 移除无用的状态和逻辑，简化用户页面组件

46. refactor: 移除无用的 sharedValue

47. refactor: 移除无用的 nativeGesture 属性，调整 Tab 导航初始路由

48. refactor: 更新用户页逻辑，重构返回事件处理，移除无用动画

49. refactor: 删除用户页无用动画

50. Update DIscussFeed.tsx
51. fix: 优化 User 组件的条件判断逻辑

52. fix: 修正 DiscussFeed 组件及相关类型的导入路径

53. fix: 更新 ESLint 配置
=====


The PR Git Diff:
=====
## File: 'app/bbs-feed/index.tsx'

@@ -1,7 +1,7 @@ 
 import React, { useEffect } from 'react';
 import { ImageStyle } from 'react-native';
-import { DiscussFeed } from '@/src/bizComponents/feedScreen/DIscussFeed';
+import { DiscussFeed } from '@/src/bizComponents/feedScreen/DiscussFeed';
 import { Image, Screen } from '@/src/components';
 import { darkTheme } from '@/src/theme/colors';
 import { reportExpo } from '@/src/utils/report';



## File: 'app/feed/index.tsx'

@@ -40,9 +40,9 @@ 
 import { MessageScreen } from '@/src/bizComponents/messageScreen';
 import TopicScreen from '@/src/bizComponents/topicScreen';
 import Trending from '@/src/bizComponents/trendingScreen';
 import { UserScreen } from '@/src/bizComponents/userScreen';
-import { PageTab } from '@/src/bizComponents/userScreen/types';
+import { UserPageTab } from '@/src/bizComponents/userScreen/constants';
 import { showToast } from '@/src/components';
 import { useGuideToMakePhotoStatus } from '@/src/components/guide/guide-content/make-photo-guide';
 import { PublishEntry } from '@/src/components/publishEntry';
 import { SkinnedImage } from '@/src/components/skin/SkinnedImage';


@@ -122,13 +122,13 @@ export function NewFeed() {
   const { isTeenMode } = useTeenModeGuard();
   const { immersiveTab } = useBehaviorStore(
     useShallow(state => ({ immersiveTab: state.immersiveTab }))
   );
 
   useEffect(() => {
     if (
       params.profileUpdateTimestamp &&
-      params.profilePageTab === PageTab.SECRET &&
+      params.profilePageTab === UserPageTab.SECRET &&
       params.profileRefresh === '1' &&
       params.scene === Go2HomeScene.VIDEO_GENERATING
     ) {
       FeedBubbleEventBus.emit(FeedBubbleEvent.SHOW_BUBBLE);


@@ -242,13 +242,12 @@ function MyTabBar({
   const bottomHeight = BOTTOM_TAB_HEIGHT + bottom;
 
   // 创作引导，优先级最高
   const [showGuide, setShowGuide] = useState(false);
   const { isTeenMode, teenModeGuard } = useTeenModeGuard();
 
   // 折叠状态
   const $unfold = useSharedValue(1);
-  const sharedValue = useSharedValue(0);
 
   useEffect(() => {
     $unfold.value = withTiming(isTabVisible ? 1 : 0, {
       duration: 300,



## File: 'app/follow-fan/index.tsx'

@@ -8,12 +8,14 @@ 
 import { Tabs } from '@/src/components/tabs';
 import { useSafeAreaInsetsStyle } from '@/src/hooks';
 import { useAuthStore } from '@/src/store/authInfo';
 import { darkColors, darkTheme } from '@/src/theme';
+import { getThemeColorV2 } from '@/src/theme/colors/common';
 import { ReportError, errorReport } from '@/src/utils/error-log';
 import { log } from '@/src/utils/logger';
 import { reportExpo } from '@/src/utils/report';
 import { StyleSheet } from '@Utils/StyleSheet';
+import { Theme } from '../../src/theme/colors/type';
 import { useShallow } from 'zustand/react/shallow';
 import { FansList, FollowList } from './components';
 
 const items = [


@@ -19,17 +21,20 @@ 
 const items = [
   {
     key: 'follow',
     title: '关注',
     label: '关注'
   },
   {
     key: 'fans',
     title: '粉丝',
     label: '粉丝'
   }
 ];
-export default () => {
+export default (props: { theme: 'light' | 'dark' } = { theme: 'dark' }) => {
+  const themeColor = getThemeColorV2(
+    props.theme === 'light' ? Theme.LIGHT : Theme.DARK
+  );
   const { defaultTab = 'follow', uid } = useLocalSearchParams();
 
   const $containerInsets = useSafeAreaInsetsStyle(['top', 'bottom']);
   const pagerRef = useRef<PagerView>(null);


@@ -104,14 +109,16 @@ export default () => {
       </PagerView>
     );
   };
 
   return (
     <PagePerformance pathname="follow-fan/index">
       <Screen
         safeAreaEdges={[]}
+        theme={props.theme}
+        StatusBarProps={{ style: props.theme === 'light' ? 'dark' : 'light' }}
         headerShown={false}
-        style={{ backgroundColor: darkTheme.background.page }}
+        style={{ backgroundColor: themeColor.background.page }}
       >
         <View style={[{ marginTop: $containerInsets.paddingTop }, st.header]}>
           <TouchableOpacity
             style={st.back}


@@ -114,13 +121,13 @@ export default () => {
       >
         <View style={[{ marginTop: $containerInsets.paddingTop }, st.header]}>
           <TouchableOpacity
             style={st.back}
             onPress={() => {
               router.back();
             }}
           >
-            <Icon icon="back" color={darkTheme.text.primary}></Icon>
+            <Icon icon="back" color={themeColor.text.primary}></Icon>
           </TouchableOpacity>
           {/* <Animated.View style={[]}> */}
           <Tabs
             items={items}


@@ -157,18 +164,17 @@ 
 const tabStyles = StyleSheet.create({
   $tabStyle: {
     ...StyleSheet.rowStyle,
     // height: 44,
     width: '100%',
     justifyContent: 'center'
   },
   $tabItemStyle: {
     flex: 0,
     width: 60
   },
   $tabItemTextStyle: {
     textAlign: 'center',
-    color: StyleSheet.hex(StyleSheet.currentColors.black, 0.4),
     fontWeight: '600',
     fontSize: 16,
     lineHeight: 26
   },



## File: 'app/magic-video-history/history-item/index.tsx'

@@ -1,9 +1,9 @@ 
 import dayjs from 'dayjs';
 import { router } from 'expo-router';
 import { Pressable, Text, TextStyle, View, ViewStyle } from 'react-native';
 import ImageBox from '@/src/bizComponents/magic-video/imageBox';
-import { PageTab } from '@/src/bizComponents/userScreen';
+import { UserPageTab } from '@/src/bizComponents/userScreen/constants';
 import StatusCode from '@/src/bizComponents/videoMagic/statusCode';
 import { Go2HomeScene } from '@/src/hooks/useChangeRoute';
 import { useLiveStore } from '@/src/store/live';
 import { useMagicVideoEditStore } from '@/src/store/video-magic';


@@ -49,13 +49,13 @@ export default function HistoryItem({ item, index }: IHistoryItemProps) {
       case AsyncCardStatus.FAIL: {
         router.replace({
           pathname: '/feed/',
           params: {
             tab: TabItemType.PROFILE, // 'user'
             profileUpdateTimestamp: Date.now().toString(),
             profileRefresh: '1',
             scene: Go2HomeScene.VIDEO_GENERATING,
-            profilePageTab: PageTab.SECRET
+            profilePageTab: UserPageTab.SECRET
           }
         });
         break;
       }


@@ -62,13 +62,13 @@ export default function HistoryItem({ item, index }: IHistoryItemProps) {
       case AsyncCardStatus.PROCESSING: {
         router.replace({
           pathname: '/feed/',
           params: {
             tab: TabItemType.PROFILE, // 'user'
             profileUpdateTimestamp: Date.now().toString(),
             profileRefresh: '1',
             scene: Go2HomeScene.VIDEO_GENERATING,
-            profilePageTab: PageTab.SECRET
+            profilePageTab: UserPageTab.SECRET
           }
         });
         break;
       }



## File: 'src/api/asynccard/index.ts'

@@ -2,8 +2,9 @@ 
 import { AsyncCard } from '@/proto-registry/src/web/raccoon/asynccard/asynccard_connect';
 import {
   DeleteAsyncCardReq,
   GetAsyncCardsReq,
+  GetUserShowAsyncCardCntReq,
   ReGenerateAsyncCardReq
 } from '@/proto-registry/src/web/raccoon/asynccard/asynccard_pb';
 import { PartialMessage } from '@bufbuild/protobuf';
 


@@ -18,5 +19,11 @@ 
 export const regenAsyncCard = (
   payload: PartialMessage<ReGenerateAsyncCardReq>
 ) => {
   return asyncCardClient.reGenerateAsyncCard(payload);
 };
+
+export const getUserShowAsyncCardCnt = (
+  payload: PartialMessage<GetUserShowAsyncCardCntReq>
+) => {
+  return asyncCardClient.getUserShowAsyncCardCnt(payload);
+};



## File: 'src/api/goods/index.ts'

@@ -13,9 +13,9 @@ 
 import { PartialMessage } from '@bufbuild/protobuf';
 
 export const goodsClient = createSocketConnect('Goods', Goods);
 
-/** 该用户狸小窝信息 */
+/** 该用户痛墙信息 */
 export const getUserPlaceInfo = async (
   placeParams: PartialMessage<GetPlaceReq>
 ): Promise<PartialMessage<GetPlaceRsp>> => {
   return goodsClient



## File: 'src/bizComponents/feedScreen/discussPanels/discussPanel.tsx'

@@ -28,10 +28,10 @@ 
 import { TabItemType } from '@/src/types';
 import { CommonEventBus } from '@/src/utils/event';
 import { reportExpo } from '@/src/utils/report';
 import { getBbsCommentLines } from '../../feedcard/ugcCard/BBSCard';
-import { BBSTabItem, EXTENDED_STATE } from '../DIscussFeed';
 import FakeTabContainer from '../fakeTabContainer';
+import { BBSTabItem, DISCUSS_EXTENDED_STATE } from '../type';
 
 function DiscussPanel({
   tabItem,
   isActive,


@@ -160,13 +160,13 @@ function DiscussPanel({
       loading={recommendDataLoading}
       error={recommendError}
       hasMore={recommendHasMore}
       reportParams={reportParams}
       getReportParams={getReportParams}
       onRequest={fetchRecommendList}
       footerStyle={[footerStyle]}
       isActive={isActive}
-      extendedState={EXTENDED_STATE}
+      extendedState={DISCUSS_EXTENDED_STATE}
       onScroll={onScroll}
       customListProps={{
         renderAheadOffset: 250
       }}



## File: 'src/bizComponents/feedScreen/rolePanels/myRole.tsx'

@@ -53,15 +53,31 @@ const MyRole = memo(
         useShallow(state => ({
           fetchMyRoleList: state.fetchMyRoleList,
           roleListDataMap: state.roleListDataMap,
           clearRoleData: state.clearRoleData
         }))
       );
 
     useEffect(() => {
-      clearRoleData(FixedRoleFeed.myCreate);
-      clearRoleData(FixedRoleFeed.mySave);
-    }, []);
+      // 注释掉清除数据的代码，因为这可能会干扰从其他页面（如 userScreen）进入时已加载的数据
+      // clearRoleData(FixedRoleFeed.myCreate);
+      // clearRoleData(FixedRoleFeed.mySave);
+
+      // 当组件挂载且有焦点时，主动加载数据
+      if (isFocus && isRender) {
+        fetchMyRoleList({
+          isInit: true,
+          id: FixedRoleFeed.myCreate,
+          reqParams: { filterType: RoleFilterType.RoleFilterTypeCreate }
+        });
+
+        fetchMyRoleList({
+          isInit: true,
+          id: FixedRoleFeed.mySave,
+          reqParams: { filterType: RoleFilterType.RoleFilterTypeSave }
+        });
+      }
+    }, [isFocus, isRender]);
 
     return (
       <FakeTabContainer active={isActive} lazy>
         <View style={myPanelStyles.barStyles}>



## File: 'src/bizComponents/feedScreen/DiscussFeed.tsx'

@@ -15,25 +15,10 @@ 
 import { CellCardScene } from '../feedcard/types';
 import DiscussPanel from './discussPanels/discussPanel';
 import { useCachedTab } from './recommendSecondaryTab/cacheTab.hook';
 import FakeTabContainer from './fakeTabContainer';
-import { RecommendSecondaryTab, TabItemInfo } from './recommendSecondaryTab';
-
-type BbsQueryKey = 'sort_type' | 'type_filter';
-type BbsQueryValue = 'hot' | 'new' | 'picture' | 'oc';
-
-enum BbsTabKey {
-  hot = 'bbs_hot',
-  new = 'bbs_new',
-  picture = 'bbs_picture',
-  oc = 'bbs_oc'
-}
-
-export interface BBSTabItem extends TabItemInfo {
-  key: BbsTabKey;
-  queryKey: BbsQueryKey;
-  queryValue: BbsQueryValue;
-}
+import { RecommendSecondaryTab } from './recommendSecondaryTab';
+import { BBSTabItem, BbsTabKey } from './type';
 
 const tabs: BBSTabItem[] = [
   {
     title: '热门',


@@ -61,21 +46,12 @@ const tabs: BBSTabItem[] = [
     title: 'OC互动',
     key: BbsTabKey.oc,
     queryKey: 'type_filter',
     queryValue: 'oc',
     brandId: 0
   }
 ];
 
-const fixedTabs = [tabs[0], tabs[1]];
-
-export const EXTENDED_STATE = {
-  reportParams: {
-    tab: EWaterFallTabReportType[EWaterFallTabType.DISCUSS]
-  },
-  tab: EWaterFallTabType.DISCUSS,
-  scene: CellCardScene.DISCUSS
-};
 export const DiscussFeed = ({
   pending,
   active: isPageActive,
   standAlone = false



## File: 'src/bizComponents/feedScreen/index.tsx'

@@ -54,9 +54,9 @@ 
 import PreloadImg from './preload/preloadImg';
 import { useIsFocused, useRoute } from '@react-navigation/native';
 import * as APMModule from '@step.ai/apm-module';
 import { useShallow } from 'zustand/react/shallow';
-import { DiscussFeed } from './DIscussFeed';
+import { DiscussFeed } from './DiscussFeed';
 import { RecommendFeed } from './RecommendFeed';
 import { RoleFeed } from './RoleFeed';
 import { FeedScreenPageParams } from './type';



## File: 'src/bizComponents/feedScreen/type.ts'

@@ -1,5 +1,10 @@ 
+import {
+  EWaterFallTabReportType,
+  EWaterFallTabType
+} from '@/src/components/waterfall/type';
 import { RichCardInfo as RawRichCardInfo } from '@/src/types';
+import { CellCardScene } from '../feedcard/types';
 
 export enum ETagIp {
   HOT = 'hot',
   NORMAL = 'normal',


@@ -93,11 +98,42 @@ 
 export interface FeedScreenPageParams {
   appendId?: string;
   appendImageUrl?: string;
   appendGameType?: string;
   isAsyncAppend?: string;
   needSaveVideo?: string;
   // 暂未使用 'recommend' | 'follow' | 'role' | 'discuss';
   pageTab?: string;
   refresh?: string;
   timestamp?: string;
 }
+
+export type BbsQueryKey = 'sort_type' | 'type_filter';
+export type BbsQueryValue = 'hot' | 'new' | 'picture' | 'oc';
+
+export enum BbsTabKey {
+  hot = 'bbs_hot',
+  new = 'bbs_new',
+  picture = 'bbs_picture',
+  oc = 'bbs_oc'
+}
+
+export interface TabItemInfo {
+  title: string;
+  key: string;
+  isFire?: boolean;
+  brandId: number;
+}
+
+export interface BBSTabItem extends TabItemInfo {
+  key: BbsTabKey;
+  queryKey: BbsQueryKey;
+  queryValue: BbsQueryValue;
+}
+
+export const DISCUSS_EXTENDED_STATE = {
+  reportParams: {
+    tab: EWaterFallTabReportType[EWaterFallTabType.DISCUSS]
+  },
+  tab: EWaterFallTabType.DISCUSS,
+  scene: CellCardScene.DISCUSS
+};



## File: 'src/bizComponents/nestedScrollView/hooks/usePullDownGestureHandlers.tsx'

@@ -0,0 +1,188 @@
+/* eslint-disable react-hooks/rules-of-hooks */
+import {
+  SharedValue,
+  runOnJS,
+  useSharedValue,
+  useWorkletCallback,
+  withTiming
+} from 'react-native-reanimated';
+import { SCROLLABLE_DIRECTION } from '../constants';
+import type { GestureEventHandlerCallbackType } from '../types';
+import { useTabGestureEventsHandlersDefault } from './useTabGestureEventsHandlersDefault';
+
+/**
+ * 下拉手势处理器工厂
+ * 返回一个对象，包含：
+ * 1. getGestureHandlers - 返回处理手势的处理器函数
+ * 2. pullDownProgress - 下拉进度的共享值
+ */
+export const usePullDownGestureHandlersFactory = (options: {
+  onPullDownEnd?: (progress: number) => void;
+  scrollPosition: SharedValue<number>;
+}) => {
+  // 下拉进度值
+  const pullDownProgress = useSharedValue(0);
+
+  // 手势方向
+  const gestureDirection = useSharedValue(SCROLLABLE_DIRECTION.UNKNOWN);
+
+  // 记录手势开始时的滚动位置
+  const scrollPositionAtStart = useSharedValue(0);
+
+  // 触顶偏移量：-1表示未触顶，>=0表示触顶时的translationY值
+  const topOffsetY = useSharedValue(-1);
+
+  // 标记之前是否处于顶部状态
+  const wasAtTop = useSharedValue(false);
+
+  // 返回一个函数，该函数在调用时返回手势处理器
+  const getGestureHandlers = () => {
+    // 获取原始手势处理器
+    const originalHandlers = useTabGestureEventsHandlersDefault();
+
+    // 手势开始时重置下拉进度
+    const handleOnStart: GestureEventHandlerCallbackType = useWorkletCallback(
+      function handleOnStart(source, payload) {
+        // 记录开始位置
+        scrollPositionAtStart.value = options.scrollPosition.value;
+
+        // 重置触顶偏移量（-1表示未触顶）
+        topOffsetY.value = -1;
+
+        // 记录初始顶部状态
+        wasAtTop.value = options.scrollPosition.value === 0;
+
+        // 重置下拉进度和手势方向
+        pullDownProgress.value = 0;
+        gestureDirection.value = SCROLLABLE_DIRECTION.UNKNOWN;
+
+        originalHandlers.handleOnStart(source, payload);
+      },
+      [originalHandlers.handleOnStart]
+    );
+
+    // 手势变化时监控下拉进度
+    const handleOnChange: GestureEventHandlerCallbackType = useWorkletCallback(
+      function handleOnChange(source, payload) {
+        const { translationY, translationX } = payload;
+
+        // 确定手势方向
+        if (gestureDirection.value === SCROLLABLE_DIRECTION.UNKNOWN) {
+          gestureDirection.value =
+            Math.abs(translationX) - Math.abs(translationY) > 0
+              ? SCROLLABLE_DIRECTION.HORIZONTAL
+              : SCROLLABLE_DIRECTION.VERTICAL;
+        }
+
+        const isAtTop = options.scrollPosition.value === 0;
+
+        // 检测是否从顶部变为非顶部状态
+        if (wasAtTop.value && !isAtTop) {
+          pullDownProgress.value = 0;
+          console.log(
+            '[GestureLog][PullDown][LeavingTop]',
+            JSON.stringify({
+              scrollPosition: options.scrollPosition.value
+            })
+          );
+        }
+
+        // 更新顶部状态记录
+        wasAtTop.value = isAtTop;
+
+        // 检测是否刚刚触顶
+        if (isAtTop && topOffsetY.value === -1) {
+          // 记录触顶时的translationY值
+          topOffsetY.value = translationY;
+        }
+
+        // 计算超出顶部的下拉距离
+        let overTopDistance = 0;
+        if (isAtTop) {
+          // 如果已触顶（topOffsetY >= 0），计算超出部分
+          // 如果未触顶（topOffsetY === -1），使用全部translationY
+          overTopDistance =
+            topOffsetY.value >= 0
+              ? translationY - topOffsetY.value
+              : translationY;
+
+          // 确保不为负值
+          overTopDistance = Math.max(0, overTopDistance);
+        }
+
+        const isVerticalPull =
+          translationY > 0 &&
+          gestureDirection.value === SCROLLABLE_DIRECTION.VERTICAL;
+
+        // 更新下拉进度
+        if (isVerticalPull && isAtTop && overTopDistance > 0) {
+          pullDownProgress.value = overTopDistance;
+        } else if (pullDownProgress.value > 0) {
+          pullDownProgress.value = 0;
+        }
+
+        originalHandlers.handleOnChange(source, payload);
+      },
+      [originalHandlers.handleOnChange, options.scrollPosition]
+    );
+
+    // 手势结束时处理
+    const handleOnEnd: GestureEventHandlerCallbackType = useWorkletCallback(
+      function handleOnEnd(source, payload) {
+        const isAtTop = options.scrollPosition.value === 0;
+
+        if (pullDownProgress.value > 0 && isAtTop) {
+          const finalProgress = pullDownProgress.value;
+
+          if (options?.onPullDownEnd) {
+            runOnJS(options.onPullDownEnd)(finalProgress);
+          }
+        }
+
+        // 平滑重置下拉进度
+        if (pullDownProgress.value > 0) {
+          pullDownProgress.value = withTiming(0, { duration: 300 });
+        }
+
+        // 重置所有状态
+        topOffsetY.value = -1;
+        wasAtTop.value = false;
+        gestureDirection.value = SCROLLABLE_DIRECTION.UNKNOWN;
+
+        originalHandlers.handleOnEnd(source, payload);
+      },
+      [originalHandlers.handleOnEnd, options?.onPullDownEnd]
+    );
+
+    // 手势完成时处理
+    const handleOnFinalize: GestureEventHandlerCallbackType =
+      useWorkletCallback(
+        function handleOnFinalize(source, payload) {
+          // 平滑重置下拉进度
+          if (pullDownProgress.value > 0) {
+            pullDownProgress.value = withTiming(0, { duration: 300 });
+          }
+
+          // 重置所有状态
+          topOffsetY.value = -1;
+          wasAtTop.value = false;
+          gestureDirection.value = SCROLLABLE_DIRECTION.UNKNOWN;
+
+          originalHandlers.handleOnFinalize(source, payload);
+        },
+        [originalHandlers.handleOnFinalize]
+      );
+
+    return {
+      handleOnStart,
+      handleOnChange,
+      handleOnEnd,
+      handleOnFinalize
+    };
+  };
+
+  return {
+    getGestureHandlers,
+    pullDownProgress
+  };
+};



## File: 'src/bizComponents/topicScreen/components/topicRankBanner/components/TopicScrollingRow.tsx'

@@ -9,9 +9,9 @@ 
 import { StyleSheet } from '@/src/utils';
 import { styles as sharedStyles } from '../styles';
 
 interface TopicScrollingRowProps {
-  row: Array<TopicOrBrand>;
+  row: TopicOrBrand[];
   index: number;
   onTagPress?: (data: TopicOrBrand) => boolean | void;
 }



## File: 'src/bizComponents/trendingScreen/ImmersivePost.tsx'

@@ -42,13 +42,13 @@ 
 interface ImmersivePostProps {
   data: RichCardInfo;
   shouldLoadResource?: boolean;
   isActive: boolean;
   index?: number;
   eventBus: Event<TVIDEO_FLOW_EVENT_KEYS>;
 }
 
-export const ImmersivePost: React.FC<ImmersivePostProps> = memo(
+export const ImmersivePost = memo(
   ({
     data,
     isActive,
     eventBus: videoFlowEventBus,


@@ -192,13 +192,12 @@ export const ImmersivePost: React.FC<ImmersivePostProps> = memo(
                 width: screenWidth
               }
             ]}
           >
             <BBSVoteCard
               data={data}
               isImmersive={true}
               onCardPress={handleCardPress}
-              onCardReady={() => {}}
               onLayout={layoutRectangle => {
                 // 保存测量到的卡片高度
                 setVoteButtonTop(
                   voteTop + layoutRectangle.height + layoutRectangle.y + 20



## File: 'src/bizComponents/trendingScreen/index.tsx'

@@ -39,9 +39,9 @@ 
 import { AsyncAppendCard } from '../feedScreen/asyncAppendingCard';
 import { useAppendCard } from '../feedScreen/hooks/useAppendCard';
 import { AbsoluteIconBack } from '../livePhotoScreen/actionsLayer/AbsoluteIconBack';
 import TopicScreen from '../topicScreen';
-import { PageTab } from '../userScreen';
+import { UserPageTab } from '../userScreen/constants';
 import { DanceTogetherExtInfo } from '@/proto-registry/src/web/raccoon/common/dance_together_pb';
 import { LivePhotoExtInfo } from '@/proto-registry/src/web/raccoon/common/livephoto_pb';
 import { PortalHost } from '@gorhom/portal';
 import { useIsFocused } from '@react-navigation/native';


@@ -195,13 +195,13 @@ const Trending = memo(
               paddingRight: dp2px(16)
             },
             onClick: () => {
               const uid = useAuthStore.getState().userInfo?.uid;
               router.push({
                 pathname: `/user/${uid}`,
                 params: {
                   id: (uid || '').toString(),
-                  profilePageTab: PageTab.SECRET
+                  profilePageTab: UserPageTab.SECRET
                 }
               });
             },
             duration: 4000,



## File: 'src/bizComponents/userScreen/components/baseFlowList/index.tsx'

@@ -1,39 +1,34 @@ 
 import { memo } from 'react';
 import {
   NativeScrollEvent,
   NativeSyntheticEvent,
   Platform,
-  Text,
+  ScrollViewProps,
   View
 } from 'react-native';
-import { GestureDetector, GestureType } from 'react-native-gesture-handler';
-import { InfiniteListRef } from '@/src/components/infiniteList/typing';
+import { InfiniteScrollViewProps } from '@/src/components/infiniteList/typing';
 import { WaterFall2 } from '@/src/components/waterfall/WaterFall2';
 import { IWaterFallProps } from '@/src/components/waterfall/type';
 import {
   RequestFeedReturnActions,
   RequestFeedReturnData
 } from '@/src/components/waterfall/useRequsetFeed';
-import { useWaterfallGesture } from '@/src/components/waterfall/useWaterfallGesture';
 import { BOTTOM_TAB_HEIGHT, LIST_BOTTOM_SAFE_HEIGHT } from '@/src/constants';
 import { useScreenSize } from '@/src/hooks';
 import { dp2px } from '@/src/utils';
-import { PageTab } from '../../types';
+import { UserPageTab } from '../../constants';
 
 interface Props {
-  pageTabKey: PageTab;
+  pageTabKey: UserPageTab;
   data: RequestFeedReturnData;
   fetchList: RequestFeedReturnActions['fetchList'];
-  onScroll: ReturnType<typeof useWaterfallGesture>['onScroll'];
-  scrollViewProps: ReturnType<typeof useWaterfallGesture>['scrollViewProps'];
+  scrollViewProps: InfiniteScrollViewProps;
   waterfallProps?: Partial<IWaterFallProps>;
-  listRef?: React.RefObject<InfiniteListRef>;
   onMomentumScrollEnd?: (e: NativeSyntheticEvent<NativeScrollEvent>) => void;
   isRootPage?: boolean; // 是否是根页面
   $safePaddingBottom: number;
-  nativeGesture: GestureType;
-  currentTab: PageTab;
+  currentTab: UserPageTab;
 }
 
 export const BaseWaterFlowList = memo((props: Props) => {
   const {


@@ -38,17 +33,14 @@ 
 export const BaseWaterFlowList = memo((props: Props) => {
   const {
     pageTabKey,
     data,
     fetchList,
-    onScroll,
     scrollViewProps,
     waterfallProps,
-    listRef,
     onMomentumScrollEnd,
     isRootPage,
     $safePaddingBottom,
-    nativeGesture,
     currentTab
   } = props;
 
   const { width: screenWidth } = useScreenSize('window');


@@ -53,59 +45,56 @@ export const BaseWaterFlowList = memo((props: Props) => {
 
   const { width: screenWidth } = useScreenSize('window');
 
   return (
     <View
       key={pageTabKey}
       style={{
         width: screenWidth,
-        marginTop: 5,
         position: 'relative',
         flex: 1
       }}
     >
-      <GestureDetector gesture={nativeGesture}>
-        <WaterFall2
-          key={pageTabKey}
-          data={data.sourceData}
-          loading={data.loading}
-          error={data.error}
-          hasMore={data.hasMore}
-          onRequest={fetchList}
-          onScroll={onScroll}
-          customEmptyProps={{
-            children: '小狸在等你的作品！',
-            type: 'darkProfile'
-          }}
-          ref={listRef}
-          isActive={currentTab === pageTabKey}
-          scrollViewProps={{
-            ...scrollViewProps,
-            bounces: false,
-            hideRefresh: true,
-            lockScroll: false,
-            onMomentumScrollEnd: onMomentumScrollEnd
-          }}
-          footerStyle={{
-            paddingBottom:
-              $safePaddingBottom +
-              (isRootPage ? BOTTOM_TAB_HEIGHT : 0) +
-              LIST_BOTTOM_SAFE_HEIGHT +
-              dp2px(Platform.OS === 'ios' ? 90 : 20)
-          }}
-          customListProps={{
-            canChangeSize: true,
-            applyWindowCorrection(offsetX, offsetY, windowCorrection) {
-              return {
-                ...windowCorrection,
-                startCorrection: -10,
-                endCorrection: 170
-              };
-            }
-          }}
-          {...waterfallProps}
-        />
-      </GestureDetector>
+      <WaterFall2
+        key={pageTabKey}
+        data={data.sourceData}
+        loading={data.loading}
+        error={data.error}
+        hasMore={data.hasMore}
+        onRequest={fetchList}
+        customEmptyProps={{
+          children: '小狸在等你的作品！',
+          type: 'darkProfile'
+        }}
+        isActive={currentTab === pageTabKey}
+        scrollViewProps={{
+          ...scrollViewProps,
+          bounces: false,
+          hideRefresh: true,
+          overScrollMode: 'never',
+          lockScroll: true,
+          onMomentumScrollEnd: onMomentumScrollEnd
+        }}
+        footerStyle={{
+          paddingBottom:
+            $safePaddingBottom +
+            (isRootPage ? BOTTOM_TAB_HEIGHT : 0) +
+            LIST_BOTTOM_SAFE_HEIGHT +
+            dp2px(Platform.OS === 'ios' ? 90 : 20)
+        }}
+        customListProps={{
+          canChangeSize: true,
+          applyWindowCorrection(offsetX, offsetY, windowCorrection) {
+            return {
+              ...windowCorrection,
+              startCorrection: -10,
+              endCorrection: 170
+            };
+          }
+        }}
+        {...waterfallProps}
+        enablePullRefresh={false}
+      />
     </View>
   );
 });



## File: 'src/bizComponents/userScreen/components/likeFlowList/index.tsx'

@@ -1,6 +1,7 @@ 
 import { useLockFn } from 'ahooks';
-import { memo, useEffect, useMemo } from 'react';
+import { memo, useEffect, useMemo, useRef } from 'react';
+import { ScrollView } from 'react-native';
 import { feedClient } from '@/src/api';
 import { CellCardScene } from '@/src/bizComponents/feedcard/types';
 import { hideLoading, showLoading } from '@/src/components';
 import { RequestScene } from '@/src/components/infiniteList/typing';


@@ -4,27 +5,21 @@ import {
 import { CellCardScene } from '@/src/bizComponents/feedcard/types';
 import { hideLoading, showLoading } from '@/src/components';
 import { RequestScene } from '@/src/components/infiniteList/typing';
 import { IWaterFallProps } from '@/src/components/waterfall/type';
 import {
   FetchMethodPayloadType,
   useRequestFeed
 } from '@/src/components/waterfall/useRequsetFeed';
-import { useWaterfallGesture } from '@/src/components/waterfall/useWaterfallGesture';
-import { usePersonalCenterStore } from '@/src/store/personalCenter';
-import { FlowCommonProps, PageTab } from '../../types';
+import { UserPageTab } from '../../constants';
+import { FlowCommonProps } from '../../types';
 import { onRefreshError } from '../../utils';
 import { BaseWaterFlowList } from '../baseFlowList';
 
-interface Props extends FlowCommonProps {}
-
-export const LikesFlowList = memo((props: Props) => {
+export const LikesFlowList = memo((props: FlowCommonProps) => {
   const {
     id,
-    updateUnlockTop,
     $safePaddingBottom,
-    scrollY,
-    nativeGesture,
     queryRefresh,
     queryTimestamp,
     queryPageTab,
     isRefreshData,


@@ -27,12 +22,14 @@ export const LikesFlowList = memo((props: Props) => {
     queryRefresh,
     queryTimestamp,
     queryPageTab,
     isRefreshData,
     isRootPage,
     currentTab
   } = props;
 
+  const scrollViewRef = useRef<ScrollView | null>(null);
+
   const fetchLikeFeedMethod = (payload: FetchMethodPayloadType) => {
     return feedClient.userLikesCards({
       uid: id ?? '',
       pagination: payload.pagination


@@ -46,35 +43,28 @@ export const LikesFlowList = memo((props: Props) => {
 
   const lockFetchList = useLockFn(fetchList);
 
   useEffect(() => {
     lockFetchList(RequestScene.INIT);
   }, [id]);
 
   useEffect(() => {
-    if (queryRefresh && queryPageTab === PageTab.LIKE) {
+    if (queryRefresh && queryPageTab === UserPageTab.LIKE) {
       lockFetchList(RequestScene.INIT);
     }
   }, [queryRefresh, queryTimestamp]);
 
   useEffect(() => {
-    if (currentTab === PageTab.LIKE && isRefreshData) {
+    if (currentTab === UserPageTab.LIKE && isRefreshData) {
       showLoading();
       lockFetchList(RequestScene.REFRESHING).finally(() => {
-        scrollViewProps.ref.current?.scrollTo(0, 0, false);
+        scrollViewRef.current?.scrollTo(0, 0, false);
         hideLoading();
       });
     }
     // 这里只监听 tab 点击更新逻辑
   }, [isRefreshData, currentTab]);
 
-  const { onScroll, scrollViewProps, listRef, onMomentumScrollEnd } =
-    useWaterfallGesture({
-      active: currentTab === PageTab.LIKE,
-      updateUnlockTop,
-      scrollY
-    });
-
   const waterfallProps: Partial<IWaterFallProps> = useMemo(
     () => ({
       customEmptyProps: {
         children: '快去赞点作品来！',


@@ -84,22 +74,20 @@ export const LikesFlowList = memo((props: Props) => {
         scene: CellCardScene.LIKE
       }
     }),
     []
   );
 
   return (
     <BaseWaterFlowList
-      pageTabKey={PageTab.LIKE}
+      pageTabKey={UserPageTab.LIKE}
       data={data}
       fetchList={lockFetchList}
-      onScroll={onScroll}
-      scrollViewProps={scrollViewProps}
+      scrollViewProps={{
+        ref: scrollViewRef
+      }}
       waterfallProps={waterfallProps}
-      listRef={listRef}
-      onMomentumScrollEnd={onMomentumScrollEnd}
       $safePaddingBottom={$safePaddingBottom}
-      nativeGesture={nativeGesture}
       isRootPage={isRootPage}
       currentTab={currentTab}
     />
   );



## File: 'src/bizComponents/userScreen/components/myRoleList/index.tsx'

@@ -0,0 +1,289 @@
+import { useLockFn } from 'ahooks';
+import { router } from 'expo-router';
+import React, { memo, useEffect, useState } from 'react';
+import { TouchableOpacity, View } from 'react-native';
+import { StyleProp, ViewStyle } from 'react-native';
+import LinearGradient from 'react-native-linear-gradient';
+import MyRole from '@/src/bizComponents/feedScreen/rolePanels/myRole';
+import { FlowCommonProps } from '@/src/bizComponents/userScreen/types';
+import { Image, Text, hideLoading, showLoading } from '@/src/components';
+import { EmptyPlaceHolder } from '@/src/components/Empty';
+import { useAuthState } from '@/src/hooks';
+import { useCreateRoleFeedStore } from '@/src/store/feed/role-feed';
+import { FixedRoleFeed } from '@/src/store/feed/role-feed';
+import { rowStyle, typography } from '@/src/theme';
+import { $flexCenter } from '@/src/theme/variable';
+import { createStyle } from '@/src/utils';
+import { reportClick } from '@/src/utils/report';
+import { UserPageTab } from '../../constants';
+import { RoleFilterType } from '@/proto-registry/src/web/raccoon/crole/crole_pb';
+import { useShallow } from 'zustand/react/shallow';
+
+const CREATE_ROLE_ICON = require('@Assets/role/role_feed_add.png');
+
+interface MyRoleFlowListProps extends FlowCommonProps {
+  updateUnlockTop?: (status: boolean) => void;
+  nativeGesture?: unknown;
+  scrollY?: unknown;
+}
+
+// FIXME(fuxiao): 现在修改了我的角色列表数据获取方式，需要重新自己看下怎么实现
+export const MyRoleFlowList = memo((props: MyRoleFlowListProps) => {
+  const {
+    $safePaddingBottom,
+    isRefreshData,
+    currentTab,
+    queryRefresh,
+    queryPageTab,
+    queryTimestamp
+  } = props;
+
+  const [isFocus, setIsFocus] = useState(false);
+  const [isRender, setIsRender] = useState(false);
+  const { loginIntercept } = useAuthState();
+
+  // 获取角色数据状态
+  const { roleListDataMap, fetchMyRoleList, clearRoleData } =
+    useCreateRoleFeedStore(
+      useShallow(state => ({
+        roleListDataMap: state.roleListDataMap,
+        fetchMyRoleList: state.fetchMyRoleList,
+        clearRoleData: state.clearRoleData
+      }))
+    );
+
+  // 加载我的角色数据
+  const loadMyRoleData = useLockFn(async () => {
+    try {
+      // 创建的角色
+      await fetchMyRoleList({
+        isInit: true,
+        id: FixedRoleFeed.myCreate,
+        reqParams: { filterType: RoleFilterType.RoleFilterTypeCreate }
+      });
+
+      // 收藏的角色
+      await fetchMyRoleList({
+        isInit: true,
+        id: FixedRoleFeed.mySave,
+        reqParams: { filterType: RoleFilterType.RoleFilterTypeSave }
+      });
+    } catch (e) {
+      console.log('[MyRoleFlowList][loadMyRoleData]', JSON.stringify(e));
+    }
+  });
+
+  // 组件挂载时设置焦点状态
+  useEffect(() => {
+    setIsFocus(true);
+    setIsRender(true);
+
+    // 组件挂载时如果当前tab是我的角色，主动获取数据
+    if (currentTab === UserPageTab.MY_ROLE) {
+      // 清空现有数据，确保加载新数据
+      clearRoleData(FixedRoleFeed.myCreate);
+      clearRoleData(FixedRoleFeed.mySave);
+
+      // 延迟加载，等待清空操作完成
+      setTimeout(() => {
+        loadMyRoleData();
+      }, 100);
+    }
+
+    return () => {
+      setIsFocus(false);
+      setIsRender(false);
+    };
+  }, []);
+
+  // 监听切换到我的角色tab
+  useEffect(() => {
+    if (currentTab === UserPageTab.MY_ROLE) {
+      // 清空现有数据，确保加载新数据
+      clearRoleData(FixedRoleFeed.myCreate);
+      clearRoleData(FixedRoleFeed.mySave);
+
+      // 延迟加载，等待清空操作完成
+      setTimeout(() => {
+        loadMyRoleData();
+      }, 100);
+    }
+  }, [currentTab]);
+
+  // 处理Tab切换刷新
+  useEffect(() => {
+    if (currentTab === UserPageTab.MY_ROLE && isRefreshData) {
+      setIsFocus(false);
+      showLoading();
+      setTimeout(() => {
+        setIsFocus(true);
+        hideLoading();
+
+        // 清空现有数据
+        clearRoleData(FixedRoleFeed.myCreate);
+        clearRoleData(FixedRoleFeed.mySave);
+
+        // 加载新数据
+        loadMyRoleData();
+      }, 300);
+    }
+  }, [isRefreshData, currentTab]);
+
+  // 处理查询刷新
+  useEffect(() => {
+    if (queryRefresh && queryPageTab === UserPageTab.MY_ROLE) {
+      setIsFocus(false);
+      setTimeout(() => {
+        setIsFocus(true);
+
+        // 清空现有数据
+        clearRoleData(FixedRoleFeed.myCreate);
+        clearRoleData(FixedRoleFeed.mySave);
+
+        // 加载新数据
+        loadMyRoleData();
+      }, 300);
+    }
+  }, [queryRefresh, queryTimestamp, queryPageTab]);
+
+  // 检查是否有空状态
+  const hasNoRoles = Object.values(roleListDataMap).every(
+    data => !data?.list?.length
+  );
+
+  // 跳转到创建角色页面
+  const handleCreateRole = () => {
+    reportClick('create_character_button', {
+      status: 1
+    });
+    loginIntercept(() => {
+      router.push('/role-create/');
+    });
+  };
+
+  // 滚动容器样式
+  const scrollContainerStyle: StyleProp<ViewStyle> = {
+    paddingBottom: $safePaddingBottom + 120
+  };
+
+  // 如果数据为空且已经加载完成，显示空状态
+  if (hasNoRoles && isFocus && isRender) {
+    return (
+      <View style={{ flex: 1, backgroundColor: '#16161A' }}>
+        <View
+          style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}
+        >
+          <EmptyPlaceHolder type="crefRole" style={{ height: 500 }}>
+            <View style={{ ...$flexCenter }}>
+              <Text style={$emptyStyles.hintText}>
+                小狸没找到ta哦，来试试创建ta吧！
+              </Text>
+            </View>
+          </EmptyPlaceHolder>
+        </View>
+        <View style={$createRoleBtnStyles.container}>
+          <LinearGradient
+            style={$createRoleBtnStyles.gradient}
+            colors={['rgba(22, 22, 26, 0)', 'rgba(22, 22, 26, 0.8)']}
+          />
+          <TouchableOpacity
+            style={[
+              $createRoleBtnStyles.btn,
+              rowStyle,
+              { bottom: 30 + Number($safePaddingBottom) }
+            ]}
+            onPress={handleCreateRole}
+          >
+            <Image
+              style={$createRoleBtnStyles.icon}
+              source={CREATE_ROLE_ICON}
+            />
+            <Text style={[$createRoleBtnStyles.btnText, { marginLeft: 2 }]}>
+              自创角色
+            </Text>
+          </TouchableOpacity>
+        </View>
+      </View>
+    );
+  }
+
+  return (
+    <View style={{ flex: 1, backgroundColor: '#16161A' }}>
+      <MyRole
+        isActive={true}
+        isRender={isRender}
+        isFocus={isFocus}
+        scrollContainerStyle={scrollContainerStyle}
+      />
+      <View style={$createRoleBtnStyles.container}>
+        <LinearGradient
+          style={$createRoleBtnStyles.gradient}
+          colors={['rgba(22, 22, 26, 0)', 'rgba(22, 22, 26, 0.8)']}
+        />
+        <TouchableOpacity
+          style={[
+            $createRoleBtnStyles.btn,
+            rowStyle,
+            { bottom: 30 + Number($safePaddingBottom) }
+          ]}
+          onPress={handleCreateRole}
+        >
+          <Image style={$createRoleBtnStyles.icon} source={CREATE_ROLE_ICON} />
+          <Text style={[$createRoleBtnStyles.btnText, { marginLeft: 2 }]}>
+            自创角色
+          </Text>
+        </TouchableOpacity>
+      </View>
+    </View>
+  );
+});
+
+const $emptyStyles = createStyle({
+  hintText: {
+    fontSize: 14,
+    color: '#FFD6CD',
+    opacity: 0.4,
+    fontFamily: typography.fonts.pingfangSC.normal,
+    fontWeight: '500',
+    textAlign: 'center'
+  }
+});
+
+const $createRoleBtnStyles = createStyle({
+  container: {
+    position: 'absolute',
+    zIndex: 1,
+    width: '100%',
+    height: 108,
+    bottom: 60,
+    alignItems: 'center'
+  },
+  gradient: {
+    width: '100%',
+    height: '100%',
+    position: 'absolute',
+    zIndex: -1
+  },
+  btn: {
+    position: 'absolute',
+    width: 130,
+    height: 44,
+    backgroundColor: '#FF6A3B',
+    borderRadius: 50,
+    justifyContent: 'center'
+  },
+  icon: {
+    width: 19,
+    height: 19,
+    objectFit: 'contain'
+  },
+  btnText: {
+    fontSize: 14,
+    lineHeight: 20,
+    color: 'rgba(255,255,255,1)',
+    fontFamily: typography.fonts.pingfangSC.normal,
+    fontWeight: '600'
+  }
+});
+
+MyRoleFlowList.displayName = 'MyRoleFlowList';



## File: 'src/bizComponents/userScreen/components/secretFlowList/index.tsx'

@@ -1,7 +1,7 @@ 
 import { useLockFn } from 'ahooks';
-import { memo, useEffect, useMemo } from 'react';
-import { Text, View } from 'react-native';
+import { memo, useEffect, useMemo, useRef } from 'react';
+import { ScrollView } from 'react-native';
 import { feedClient } from '@/src/api';
 import {
   getSecretPhotoFeedLayoutProvider,
   renderSecretPhotoItem


@@ -4,25 +4,27 @@ import {
 import { feedClient } from '@/src/api';
 import {
   getSecretPhotoFeedLayoutProvider,
   renderSecretPhotoItem
 } from '@/src/bizComponents/feedcard/secretCard';
 import { CellCardScene } from '@/src/bizComponents/feedcard/types';
 import { hideLoading, showLoading } from '@/src/components';
 import { RequestScene } from '@/src/components/infiniteList/typing';
-import { IWaterFallProps } from '@/src/components/waterfall/type';
+import {
+  IWaterFallProps,
+  WaterFallCardData
+} from '@/src/components/waterfall/type';
 import {
   FetchMethodPayloadType,
   useRequestFeed
 } from '@/src/components/waterfall/useRequsetFeed';
-import { useWaterfallGesture } from '@/src/components/waterfall/useWaterfallGesture';
 import { useChangeRoute } from '@/src/hooks/useChangeRoute';
 import { useAsyncMessage } from '@/src/store/asyncMessage';
 import { useLiveStore } from '@/src/store/live';
-import { usePersonalCenterStore } from '@/src/store/personalCenter';
 import { GameType } from '@/src/types';
 import { CommonEventBus } from '@/src/utils/event';
-import { FlowCommonProps, PageTab } from '../../types';
+import { UserPageTab } from '../../constants';
+import { FlowCommonProps } from '../../types';
 import { onRefreshError } from '../../utils';
 import { BaseWaterFlowList } from '../baseFlowList';
 import {
   AsyncCardInfo,


@@ -25,14 +27,12 @@ import {
 import { onRefreshError } from '../../utils';
 import { BaseWaterFlowList } from '../baseFlowList';
 import {
   AsyncCardInfo,
   AsyncCardStatus
 } from '@/proto-registry/src/web/raccoon/common/asynccard_pb';
 import { Video } from '@/proto-registry/src/web/raccoon/common/media_pb';
 
-interface Props extends FlowCommonProps {}
-
 /**
  * 视频待发布页列表数据
  * 相关的 format 操作可以在这里处理
  * 抹平视频列表数据结构的差异


@@ -70,19 +70,48 @@ async function fetchSecretFeedMethod(payload: FetchMethodPayloadType) {
           } as unknown as AsyncCardInfo;
         }
         return item;
       });
       return res;
     });
 }
 
-export const SecretsFlowList = memo((props: Props) => {
+// 适配器函数：转换为 WaterFall2 所需的类型
+const adaptedGetLayoutProvider = (
+  dataRef: React.MutableRefObject<WaterFallCardData[]>
+) => {
+  // 将 WaterFallCardData[] 类型安全地转换为 AsyncCardInfo[]
+  const adaptedDataRef = {
+    get current() {
+      return dataRef.current as unknown as AsyncCardInfo[];
+    }
+  } as React.MutableRefObject<AsyncCardInfo[]>;
+
+  return getSecretPhotoFeedLayoutProvider(adaptedDataRef);
+};
+
+// 适配器函数：转换为 WaterFall2 所需的类型
+const adaptedRenderItem = (
+  type: string | number,
+  data: WaterFallCardData,
+  index: number,
+  extendedState?: object,
+  layoutInfo?: { x: number; y: number }
+) => {
+  // 将 WaterFallCardData 类型安全地转换为 AsyncCardInfo
+  return renderSecretPhotoItem(
+    type,
+    data as unknown as AsyncCardInfo,
+    index,
+    extendedState,
+    layoutInfo
+  );
+};
+
+export const SecretsFlowList = memo((props: FlowCommonProps) => {
   const {
     id,
-    updateUnlockTop,
     $safePaddingBottom,
-    scrollY,
-    nativeGesture,
     queryRefresh,
     queryPageTab,
     queryTimestamp,
     isRefreshData,


@@ -85,12 +114,13 @@ export const SecretsFlowList = memo((props: Props) => {
     queryRefresh,
     queryPageTab,
     queryTimestamp,
     isRefreshData,
     isRootPage,
     currentTab
   } = props;
 
+  const scrollViewRef = useRef<ScrollView | null>(null);
   const { go2Create } = useChangeRoute();
 
   const {
     fetchList,


@@ -121,49 +151,42 @@ export const SecretsFlowList = memo((props: Props) => {
     };
   }, []);
 
   useEffect(() => {
     lockFetchList(RequestScene.INIT);
   }, [id]);
 
   useEffect(() => {
-    if (queryRefresh && queryPageTab === PageTab.SECRET) {
+    if (queryRefresh && queryPageTab === UserPageTab.SECRET) {
       lockFetchList(RequestScene.INIT);
     }
   }, [queryRefresh, queryTimestamp]);
 
   useEffect(() => {
-    if (currentTab === PageTab.SECRET && isRefreshData) {
+    if (currentTab === UserPageTab.SECRET && isRefreshData) {
       showLoading();
       lockFetchList(RequestScene.REFRESHING).finally(() => {
-        scrollViewProps.ref.current?.scrollTo(0, 0, false);
+        scrollViewRef.current?.scrollTo(0, 0, false);
         hideLoading();
       });
     }
     // 这里只监听 tab 点击更新逻辑
   }, [isRefreshData, currentTab]);
 
-  const {
-    onScroll,
-    scrollViewProps,
-    listRef: worksFeedRef,
-    onMomentumScrollEnd
-  } = useWaterfallGesture({
-    active: currentTab === PageTab.LIKE,
-    updateUnlockTop,
-    scrollY
-  });
-
   const validSecretSourceData = useMemo(() => {
     return (
-      secretSourceData?.filter(i => i.status !== AsyncCardStatus.PUBLISH) || []
+      secretSourceData?.filter(i => {
+        // 类型断言为 AsyncCardInfo
+        const asyncCard = i as unknown as AsyncCardInfo;
+        return asyncCard.status !== AsyncCardStatus.PUBLISH;
+      }) || []
     );
   }, [secretSourceData]);
 
   const waterfallProps: Partial<IWaterFallProps> = useMemo(
     () => ({
-      renderItem: renderSecretPhotoItem,
-      getLayoutProvider: getSecretPhotoFeedLayoutProvider,
+      renderItem: adaptedRenderItem,
+      getLayoutProvider: adaptedGetLayoutProvider,
       extendedState: {
         onRefresh: () => {
           lockFetchList(RequestScene.REFRESHING);
         },


@@ -177,37 +200,39 @@ export const SecretsFlowList = memo((props: Props) => {
           go2Create({
             gameType: GameType.LIVE_PHOTO
           });
         },
         type: 'darkProfile'
       },
       reportParams: {
         type: 'secret_video'
-      },
-      getReportParams: (data: AsyncCardInfo[], index: number) => {
-        return {
-          contentid: data[index]?.cardId
-        };
       }
+      // FIXME(fuxiao): 这里滚动会不断调用，导致不断打印
+      // getReportParams: (data: WaterFallCardData[], index: number) => {
+      //   // 由于数据结构实际上是 AsyncCardInfo 而不是标准的 WaterFallCardData
+      //   const item = data[index] as unknown as AsyncCardInfo;
+      //   console.log('[DEBUG] item', item);
+      //   return {
+      //     contentid: item?.cardId
+      //   };
+      // }
     }),
     [lockFetchList, go2Create]
   );
 
   return (
     <BaseWaterFlowList
-      pageTabKey={PageTab.LIKE}
+      pageTabKey={UserPageTab.SECRET}
       data={{
         ...secretReturnData,
         sourceData: validSecretSourceData
       }}
       fetchList={lockFetchList}
-      onScroll={onScroll}
-      scrollViewProps={scrollViewProps}
+      scrollViewProps={{
+        ref: scrollViewRef
+      }}
       waterfallProps={waterfallProps}
-      listRef={worksFeedRef}
-      onMomentumScrollEnd={onMomentumScrollEnd}
       $safePaddingBottom={$safePaddingBottom}
-      nativeGesture={nativeGesture}
       isRootPage={isRootPage}
       currentTab={currentTab}
     />
   );



## File: 'src/bizComponents/userScreen/components/workFlowList/index.tsx'

@@ -1,6 +1,7 @@ 
 import { useLockFn } from 'ahooks';
-import { memo, useEffect, useMemo } from 'react';
+import { memo, useEffect, useMemo, useRef } from 'react';
+import { ScrollView } from 'react-native';
 import { feedClient } from '@/src/api';
 import { CellCardScene } from '@/src/bizComponents/feedcard/types';
 import { hideLoading, showLoading } from '@/src/components';
 import { RequestScene } from '@/src/components/infiniteList/typing';


@@ -3,31 +4,25 @@ import {
 import { feedClient } from '@/src/api';
 import { CellCardScene } from '@/src/bizComponents/feedcard/types';
 import { hideLoading, showLoading } from '@/src/components';
 import { RequestScene } from '@/src/components/infiniteList/typing';
 import {
   FetchMethodPayloadType,
   useRequestFeed
 } from '@/src/components/waterfall/useRequsetFeed';
-import { useWaterfallGesture } from '@/src/components/waterfall/useWaterfallGesture';
 import { usePersistFn } from '@/src/hooks';
-import { usePersonalCenterStore } from '@/src/store/personalCenter';
 import { useVideoFlow } from '@/src/store/video-flow';
 import { CardType, PlainType, RichCardInfo } from '@/src/types';
 import { userPerformanceCollector } from '@/src/utils/report/userPageCollector';
-import { FlowCommonProps, PageTab } from '../../types';
+import { UserPageTab } from '../../constants';
+import { FlowCommonProps } from '../../types';
 import { onRefreshError } from '../../utils';
 import { BaseWaterFlowList } from '../baseFlowList';
 
-interface Props extends FlowCommonProps {}
-
-export const WorksFlowList = memo((props: Props) => {
+export const WorksFlowList = memo((props: FlowCommonProps) => {
   const {
     id,
-    updateUnlockTop,
     $safePaddingBottom,
-    scrollY,
-    nativeGesture,
     queryRefresh,
     queryTimestamp,
     queryPageTab,
     isRefreshData,


@@ -30,12 +25,14 @@ export const WorksFlowList = memo((props: Props) => {
     queryRefresh,
     queryTimestamp,
     queryPageTab,
     isRefreshData,
     isRootPage,
     currentTab
   } = props;
 
+  const waterfallRef = useRef<ScrollView | null>(null);
+
   async function fetchUserFeedkMethod(payload: FetchMethodPayloadType) {
     return feedClient.userCreatedCards({
       uid: id ?? ''!,
       pagination: payload.pagination


@@ -57,29 +54,22 @@ export const WorksFlowList = memo((props: Props) => {
 
   const lockFetchList = useLockFn(fetchList);
 
   useEffect(() => {
     lockFetchList(RequestScene.INIT);
   }, [id]);
 
   useEffect(() => {
-    if (queryRefresh && queryPageTab === PageTab.WORKS) {
+    if (queryRefresh && queryPageTab === UserPageTab.WORKS) {
       lockFetchList(RequestScene.INIT);
     }
   }, [queryRefresh, queryTimestamp]);
 
-  const { onScroll, scrollViewProps, listRef, onMomentumScrollEnd } =
-    useWaterfallGesture({
-      active: currentTab === PageTab.WORKS,
-      updateUnlockTop,
-      scrollY
-    });
-
   useEffect(() => {
-    if (currentTab === PageTab.WORKS && isRefreshData) {
+    if (currentTab === UserPageTab.WORKS && isRefreshData) {
       showLoading();
       lockFetchList(RequestScene.REFRESHING).finally(() => {
-        scrollViewProps.ref.current?.scrollTo(0, 0, false);
+        waterfallRef.current?.scrollTo(0, 0, false);
         hideLoading();
       });
     }
     // 这里只监听 tab 点击更新逻辑


@@ -102,22 +92,20 @@ export const WorksFlowList = memo((props: Props) => {
         onLeave
       }
     }),
     [onLeave]
   );
 
   return (
     <BaseWaterFlowList
-      pageTabKey={PageTab.WORKS}
+      pageTabKey={UserPageTab.WORKS}
       data={data}
       fetchList={lockFetchList}
-      onScroll={onScroll}
-      scrollViewProps={scrollViewProps}
+      scrollViewProps={{
+        ref: waterfallRef
+      }}
       waterfallProps={waterfallProps}
-      listRef={listRef}
-      onMomentumScrollEnd={onMomentumScrollEnd}
       $safePaddingBottom={$safePaddingBottom}
-      nativeGesture={nativeGesture}
       isRootPage={isRootPage}
       currentTab={currentTab}
     />
   );



## File: 'src/bizComponents/userScreen/components/GoodsButton.tsx'

@@ -0,0 +1,47 @@
+import { Pressable, View } from 'react-native';
+import { Image } from '@/src/components/image';
+import { StyleSheet, dp2px } from '../../../utils';
+
+const GOODS_BUTTON = require('@Assets/image/goods-shef/goods_button.png');
+const MY_GOODS_BUTTON = require('@Assets/image/goods-shef/my_goods_button.png');
+export function GoodsButton({
+  isMine,
+  checkEnterGoods,
+  hasGoods,
+  safeTop
+}: {
+  isMine: boolean;
+  checkEnterGoods: () => void;
+  hasGoods: boolean;
+  safeTop: number;
+}) {
+  return (
+    <Pressable
+      style={[
+        styles.$goodsBtnContainer,
+        { top: hasGoods ? dp2px(158) + safeTop : dp2px(50) + safeTop }
+      ]}
+      hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
+      onPress={checkEnterGoods}
+    >
+      <Image
+        source={isMine ? MY_GOODS_BUTTON : GOODS_BUTTON}
+        style={styles.$goodsBtnImage}
+      />
+    </Pressable>
+  );
+}
+
+const styles = StyleSheet.create({
+  $goodsBtnContainer: {
+    position: 'absolute',
+    right: 0,
+    width: 88.7,
+    height: 30,
+    zIndex: 201
+  },
+  $goodsBtnImage: {
+    width: '100%',
+    height: '100%'
+  }
+});



## File: 'src/bizComponents/userScreen/components/GoodsTopTip.tsx'

@@ -0,0 +1,384 @@
+import * as Haptics from 'expo-haptics';
+import AnimatedLottieView from 'lottie-react-native';
+import React from 'react';
+import { Pressable, View } from 'react-native';
+import Animated, {
+  Extrapolate,
+  SharedValue,
+  interpolate,
+  runOnJS,
+  useAnimatedReaction,
+  useAnimatedStyle,
+  useSharedValue,
+  withTiming
+} from 'react-native-reanimated';
+import { Icon, Text } from '@/src/components';
+import { darkTheme, typography } from '@/src/theme';
+import { $USE_FONT } from '@/src/theme/variable';
+import { dp2px, isIos } from '@/src/utils';
+import { StyleSheet } from '@Utils/StyleSheet';
+
+const INTRO_CUSTOM_PULL = require('@Assets/lottie/goods/intro_custom_pull.json');
+const INTRO_PULL = require('@Assets/lottie/goods/intro_pull.json');
+
+// 提升关键动画参数到顶部便于后期修改
+export const ANIMATION_PARAMS = {
+  SCROLL_START: 60, // 点赞 tip 开始淡出的滚动距离
+  SCROLL_END: 130, // 点赞 tip 完全隐藏的滚动距离
+  PULL_BUFFER: 40, // 下拉缓冲空间
+  PULL_REFRESH_THRESHOLD: 100, // 下拉刷新阈值
+  PULL_REFRESH_HOLD: 140, // 刷新提示保持完全显示的距离
+  PULL_REFRESH_FADE_END: 200, // 刷新提示完全消失的距离
+  PULL_GOODS_THRESHOLD: 250, // 打开痛墙阈值
+  PULL_GOODS_FADE_START: 180, // 痛墙提示开始淡入的距离
+  ANIMATION_DURATION: 200, // 动画持续时间（毫秒）
+  ANIMATION_SCALE_START: 0.65 // 缩放比例
+};
+
+interface GoodsTopTipProps {
+  goodsLikes?: number;
+  isMine: boolean;
+  hasGoods?: boolean;
+  isGoodsTipAllowed?: boolean;
+  checkEnterGoods: () => void;
+  safeTop: number;
+  scrollPosition?: SharedValue<number>;
+  pullDownProgress?: SharedValue<number>;
+}
+
+export function GoodsTopTip({
+  goodsLikes,
+  isMine,
+  hasGoods,
+  isGoodsTipAllowed = false,
+  checkEnterGoods,
+  safeTop,
+  scrollPosition,
+  pullDownProgress
+}: GoodsTopTipProps) {
+  const shouldShowGoodsGuide =
+    isGoodsTipAllowed && (isMine || (!isMine && hasGoods));
+
+  const shouldShowLikesTip = goodsLikes && goodsLikes > 0 && !isGoodsTipAllowed;
+
+  // 跟踪是否正在下拉中
+  const [isPulling, setIsPulling] = React.useState(false);
+  // 使用sharedValue跟踪是否已触发震动
+  const hasTriggeredHaptic = useSharedValue(false);
+  // 使用sharedValue记录上一次的下拉进度
+  const previousPullProgress = useSharedValue(0);
+
+  // 定义触发震动的函数
+  const triggerHapticFeedback = React.useCallback(() => {
+    if (isIos) {
+      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
+    }
+  }, []);
+
+  useAnimatedReaction(
+    () => pullDownProgress,
+    progress => {
+      if (!progress) return;
+
+      // 检测是否开始下拉
+      if (progress.value > 0 && !isPulling) {
+        runOnJS(setIsPulling)(true);
+      } else if (progress.value === 0 && isPulling) {
+        runOnJS(setIsPulling)(false);
+        // 重置震动状态
+        hasTriggeredHaptic.value = false;
+        previousPullProgress.value = 0;
+      }
+
+      // 如果用户从痛墙区域回到刷新区域，重置震动状态
+      if (
+        progress.value < ANIMATION_PARAMS.PULL_GOODS_THRESHOLD &&
+        previousPullProgress.value >= ANIMATION_PARAMS.PULL_GOODS_THRESHOLD
+      ) {
+        hasTriggeredHaptic.value = false;
+      }
+
+      // 检测是否达到痛墙阈值，触发震动
+      if (
+        isIos &&
+        progress.value >= ANIMATION_PARAMS.PULL_GOODS_THRESHOLD &&
+        !hasTriggeredHaptic.value
+      ) {
+        runOnJS(triggerHapticFeedback)();
+        hasTriggeredHaptic.value = true;
+      }
+
+      // 更新上一次的下拉进度
+      previousPullProgress.value = progress.value;
+    }
+  );
+
+  // 原始提示（痛墙赞/痛墙提示）动画样式
+  const animatedContainerStyle = useAnimatedStyle(() => {
+    let opacity = 1;
+    let pointerEvents: 'auto' | 'none' = 'auto';
+
+    // 根据情况决定透明度
+    // 1. 如果正在下拉，立即隐藏提示
+    if (isPulling) {
+      opacity = withTiming(0, {
+        duration: ANIMATION_PARAMS.ANIMATION_DURATION
+      });
+      pointerEvents = 'none';
+    }
+    // 2. 如果正在滚动，根据滚动距离渐变透明度
+    else if (scrollPosition && scrollPosition.value !== 0) {
+      const scrollDistance = Math.abs(scrollPosition.value);
+      const targetOpacity = interpolate(
+        scrollDistance,
+        [ANIMATION_PARAMS.SCROLL_START, ANIMATION_PARAMS.SCROLL_END],
+        [1, 0],
+        Extrapolate.CLAMP
+      );
+
+      opacity = targetOpacity;
+
+      // 当完全隐藏时禁用点击事件
+      pointerEvents =
+        scrollDistance >= ANIMATION_PARAMS.SCROLL_END ? 'none' : 'auto';
+    }
+    // 3. 其他情况（未下拉且在顶部），平滑显示提示
+    else {
+      opacity = withTiming(1, {
+        duration: ANIMATION_PARAMS.ANIMATION_DURATION
+      });
+      pointerEvents = 'auto';
+    }
+
+    return {
+      opacity,
+      pointerEvents
+    };
+  });
+
+  // 刷新提示动画样式 - "松手立即刷新"
+  const refreshTipStyle = useAnimatedStyle(() => {
+    if (!pullDownProgress) return { opacity: 0 };
+
+    // 下拉到一定距离后才开始显示，达到阈值时完全不透明，
+    // 保持显示直到PULL_REFRESH_HOLD，然后在PULL_REFRESH_FADE_END开始淡出
+    const opacity = interpolate(
+      pullDownProgress.value,
+      [
+        ANIMATION_PARAMS.PULL_BUFFER * 1.5, // 开始显示的阈值
+        ANIMATION_PARAMS.PULL_REFRESH_THRESHOLD, // 完全不透明
+        ANIMATION_PARAMS.PULL_REFRESH_HOLD, // 保持完全显示直到这个距离
+        ANIMATION_PARAMS.PULL_REFRESH_FADE_END // 完全淡出
+      ],
+      [0, 1, 1, 0],
+      Extrapolate.CLAMP
+    );
+
+    // 添加缩放效果，从小到大，不超过原始尺寸
+    const scale = interpolate(
+      pullDownProgress.value,
+      [
+        ANIMATION_PARAMS.PULL_BUFFER * 1.5, // 开始显示时较小
+        ANIMATION_PARAMS.PULL_REFRESH_THRESHOLD // 达到阈值时原始大小
+      ],
+      [ANIMATION_PARAMS.ANIMATION_SCALE_START, 1],
+      Extrapolate.CLAMP
+    );
+
+    return {
+      opacity,
+      transform: [{ scale }],
+      display: opacity > 0 ? 'flex' : 'none'
+    };
+  });
+
+  // 痛墙提示动画样式 - "松手进入痛墙"
+  const goodsTipStyle = useAnimatedStyle(() => {
+    if (!pullDownProgress) return { opacity: 0 };
+
+    // 在PULL_GOODS_FADE_START到PULL_GOODS_THRESHOLD之间淡入
+    const opacity = interpolate(
+      pullDownProgress.value,
+      [
+        ANIMATION_PARAMS.PULL_GOODS_FADE_START,
+        ANIMATION_PARAMS.PULL_GOODS_THRESHOLD
+      ],
+      [0, 1],
+      Extrapolate.CLAMP
+    );
+
+    // 添加缩放效果，从小到大，不超过原始尺寸
+    const scale = interpolate(
+      pullDownProgress.value,
+      [
+        ANIMATION_PARAMS.PULL_GOODS_FADE_START, // 开始显示时较小
+        ANIMATION_PARAMS.PULL_GOODS_THRESHOLD // 达到阈值时原始大小
+      ],
+      [ANIMATION_PARAMS.ANIMATION_SCALE_START, 1],
+      Extrapolate.CLAMP
+    );
+
+    return {
+      opacity,
+      transform: [{ scale }],
+      display: opacity > 0 ? 'flex' : 'none'
+    };
+  });
+
+  // 不应该显示任何提示时，仍需要返回下拉提示的容器
+  const shouldShowAnyTip = shouldShowGoodsGuide || shouldShowLikesTip;
+
+  return (
+    <>
+      {shouldShowAnyTip ? (
+        <Animated.View
+          style={[
+            styles.$outerContainer,
+            { top: safeTop + dp2px(10) },
+            animatedContainerStyle
+          ]}
+        >
+          {shouldShowLikesTip ? (
+            <View style={styles.$likesContainer}>
+              <Icon icon="self_goods_like" size={16} style={styles.$likeIcon} />
+              <Text style={styles.$likesCountText}>{goodsLikes}人</Text>
+              <Text style={styles.$likesText}>
+                {isMine ? '赞了你的痛墙' : '赞了TA的痛墙'}
+              </Text>
+            </View>
+          ) : (
+            <Pressable onPress={checkEnterGoods} hitSlop={10}>
+              <View
+                style={[
+                  styles.$likesContainer,
+                  { paddingTop: 3, paddingLeft: 14, paddingRight: 4 }
+                ]}
+              >
+                {isMine ? (
+                  <AnimatedLottieView
+                    source={INTRO_PULL}
+                    loop
+                    autoPlay
+                    style={styles.$ownerLottie}
+                  />
+                ) : (
+                  <AnimatedLottieView
+                    source={INTRO_CUSTOM_PULL}
+                    loop
+                    autoPlay
+                    style={styles.$visitorLottie}
+                  />
+                )}
+              </View>
+            </Pressable>
+          )}
+        </Animated.View>
+      ) : null}
+
+      {/* 下拉提示容器 - 包含三个不同状态的提示，位置在原始提示下方 */}
+      {pullDownProgress ? (
+        <Animated.View
+          style={[
+            styles.$pullDownContainer,
+            {
+              top: safeTop + dp2px(48)
+            }
+          ]}
+        >
+          {/* 刷新提示 - "松手立即刷新" */}
+          <Animated.View style={[refreshTipStyle, styles.$pullTipWrapper]}>
+            <View style={styles.$pullTipContainer}>
+              <Text style={styles.$pullTipText}>松手刷新</Text>
+            </View>
+          </Animated.View>
+
+          {/* 进入痛墙提示 - "下拉进入痛墙" */}
+          <Animated.View style={[goodsTipStyle, styles.$pullTipWrapper]}>
+            <View style={styles.$pullTipContainer}>
+              <Text style={styles.$pullTipText}>下拉进入痛墙</Text>
+            </View>
+          </Animated.View>
+        </Animated.View>
+      ) : null}
+    </>
+  );
+}
+
+const styles = StyleSheet.create({
+  $outerContainer: {
+    position: 'absolute',
+    left: 0,
+    right: 0,
+    alignItems: 'center',
+    zIndex: 100
+  },
+  $likesContainer: {
+    flexDirection: 'row',
+    alignItems: 'center',
+    height: 30,
+    backgroundColor: StyleSheet.darkTheme.background.transTip,
+    paddingLeft: 16,
+    paddingRight: 16,
+    borderRadius: 15
+  },
+  $likeIcon: {
+    marginRight: 4
+  },
+  $likesCountText: {
+    ...$USE_FONT(
+      darkTheme.text.primary,
+      typography.fonts.pingfangSC.normal,
+      12,
+      'normal',
+      isIos ? '600' : 'bold',
+      undefined
+    ),
+    marginRight: 4
+  },
+  $likesText: {
+    ...$USE_FONT(
+      darkTheme.text.primary,
+      typography.fonts.pingfangSC.normal,
+      12,
+      'normal',
+      isIos ? '600' : 'bold',
+      undefined
+    )
+  },
+  $ownerLottie: {
+    width: 111,
+    height: 21
+  },
+  $visitorLottie: {
+    width: 139,
+    height: 21
+  },
+  $pullDownContainer: {
+    position: 'absolute',
+    left: 0,
+    right: 0,
+    alignItems: 'center',
+    zIndex: 101
+  },
+  $pullTipWrapper: {
+    position: 'absolute',
+    left: 0,
+    right: 0,
+    alignItems: 'center'
+  },
+  $pullTipContainer: {
+    height: 30,
+    backgroundColor: 'rgba(0, 0, 0, 0.5)',
+    paddingLeft: 12,
+    paddingRight: 12,
+    borderRadius: 15,
+    justifyContent: 'center'
+  },
+  $pullTipText: {
+    color: '#ffffff',
+    fontSize: 12,
+    textAlign: 'center',
+    fontFamily: typography.fonts.feed,
+    lineHeight: 30
+  }
+});



## File: 'src/bizComponents/userScreen/components/GoodsWallBg.tsx'

@@ -0,0 +1,109 @@
+import React, { useMemo } from 'react';
+import { View } from 'react-native';
+import Animated, { FadeIn } from 'react-native-reanimated';
+import { Image } from '../../../components';
+import { useScreenSize } from '../../../hooks';
+import { StyleSheet, dp2px } from '../../../utils';
+import { PartialMessage } from '@bufbuild/protobuf';
+import { MediaType } from '@step.ai/proto-gen/raccoon/common/media_pb';
+import { GetPlaceRsp } from '@step.ai/proto-gen/raccoon/goods/goods_pb';
+
+const GOODS_BG = require('@Assets/image/goods-shef/goods_bg.webp');
+
+interface GoodsWallBgProps {
+  visible?: boolean;
+  goodsWallRes?: PartialMessage<GetPlaceRsp>;
+  safeTop: number;
+}
+
+export function GoodsWallBg({
+  visible = true,
+  goodsWallRes,
+  safeTop
+}: GoodsWallBgProps) {
+  const { width: screenWidth, height: screenHeight } = useScreenSize('window');
+
+  const { width: wallBgWidth, height: wallBgHeight } =
+    (goodsWallRes?.backgroundImage?.type === MediaType.IMAGE &&
+      goodsWallRes?.backgroundImage?.meta?.case === 'image' &&
+      goodsWallRes?.backgroundImage?.meta?.value) ||
+    {};
+
+  // 计算图片显示的逻辑
+  const bgImageStyle = useMemo(() => {
+    if (!wallBgWidth || !wallBgHeight) {
+      return {};
+    }
+
+    // 使用原始尺寸，但只显示左侧 4/5
+    const aspectRatio = wallBgWidth / wallBgHeight;
+
+    // 计算合适的高度，使图片宽度等于屏幕宽度
+    const adjustedHeight = screenWidth / aspectRatio;
+
+    // 图片显示为屏幕宽度，但向左偏移 1/5 的宽度（这样只显示右边的 4/5）
+    return {
+      width: screenWidth,
+      height: adjustedHeight,
+      // 向左偏移 1/5 的屏幕宽度，使得我们只看到右边的 4/5
+      transform: [{ translateX: dp2px(44) }, { scale: 1.225 }]
+    };
+  }, [wallBgWidth, wallBgHeight, screenWidth]);
+
+  const hasWallBg = !!goodsWallRes?.backgroundImage?.url && !!wallBgWidth;
+
+  return (
+    <View style={styles.$container}>
+      {visible && (
+        <>
+          <Animated.View entering={FadeIn.duration(150)}>
+            <Image
+              source={GOODS_BG}
+              contentFit="contain"
+              contentPosition="top"
+              // 不使用 native 会导致图片定位问题
+              // native
+              style={[styles.$goodsBg, { height: screenHeight }]}
+            />
+          </Animated.View>
+
+          {hasWallBg && (
+            <Image
+              source={{ uri: goodsWallRes?.backgroundImage?.url }}
+              tosSize="size1"
+              contentFit="cover"
+              style={[
+                styles.$wallBgImage,
+                {
+                  top: safeTop * 0.75,
+                  ...bgImageStyle
+                }
+              ]}
+            />
+          )}
+        </>
+      )}
+    </View>
+  );
+}
+
+const styles = StyleSheet.create({
+  $container: {
+    width: '100%',
+    height: '100%',
+    position: 'absolute',
+    pointerEvents: 'none',
+    zIndex: -1,
+    backgroundColor: StyleSheet.darkTheme.background.page
+  },
+  $goodsBg: {
+    width: '100%',
+    position: 'absolute',
+    zIndex: -1
+  },
+  $wallBgImage: {
+    position: 'absolute',
+    left: 0,
+    zIndex: 0 // 确保壁纸背景在 goods bg 上面显示
+  }
+});



## File: 'src/bizComponents/userScreen/components/MineButtons.tsx'

@@ -0,0 +1,99 @@
+import { useMemoizedFn } from 'ahooks';
+import { router } from 'expo-router';
+import React from 'react';
+import { TouchableOpacity, View } from 'react-native';
+import { Image, Text } from '@/src/components';
+import { typography } from '@/src/theme';
+import { $USE_FONT } from '@/src/theme/variable';
+import { isIos } from '@/src/utils';
+import { reportClick } from '@/src/utils/report';
+import { StyleSheet } from '@Utils/StyleSheet';
+import { handleEditProfile } from '../utils';
+
+interface MineButtonsProps {
+  onEditPress?: () => void;
+  onSettingPress?: () => void;
+}
+
+const SETTING_NEW = require('@Assets/icon/icon-setting-new.png');
+export const MineButtons: React.FC<MineButtonsProps> = ({
+  onEditPress,
+  onSettingPress
+}) => {
+  const handleEditPress = useMemoizedFn(() => {
+    handleEditProfile(onEditPress);
+  });
+
+  const handleSettingPress = useMemoizedFn(() => {
+    if (onSettingPress) {
+      onSettingPress();
+    } else {
+      reportClick('button', {
+        user_button: 0,
+        identity_status: '0'
+      });
+      router.push('/setting/');
+    }
+  });
+
+  return (
+    <View style={styles.$container}>
+      <TouchableOpacity
+        activeOpacity={0.4}
+        style={styles.$editBtn}
+        onPress={handleEditPress}
+        hitSlop={{ top: 10, bottom: 10, left: 10, right: 5 }}
+      >
+        <Text style={styles.$editBtnText}>编辑资料</Text>
+      </TouchableOpacity>
+
+      <TouchableOpacity
+        style={styles.$settingBtn}
+        onPress={handleSettingPress}
+        activeOpacity={0.4}
+        hitSlop={{ top: 10, bottom: 10, left: 5, right: 10 }}
+      >
+        <Image source={SETTING_NEW} style={styles.$settingBtnImage} />
+      </TouchableOpacity>
+    </View>
+  );
+};
+
+const styles = StyleSheet.create({
+  $container: {
+    flexDirection: 'row',
+    alignItems: 'center',
+    gap: 12,
+    marginLeft: 'auto'
+  },
+  $editBtn: {
+    height: 30,
+    paddingLeft: 14,
+    paddingRight: 14,
+    borderRadius: 100,
+    backgroundColor: 'rgba(255, 255, 255, 0.08)',
+    justifyContent: 'center',
+    alignItems: 'center'
+  },
+  $editBtnText: {
+    ...$USE_FONT(
+      StyleSheet.darkTheme.text.primary,
+      typography.fonts.pingfangSC.normal,
+      12,
+      'normal',
+      '500',
+      isIos ? 15.5 : 14
+    ),
+    textAlign: 'center'
+  },
+  $settingBtn: {
+    width: 32,
+    height: 32,
+    alignItems: 'center',
+    justifyContent: 'center'
+  },
+  $settingBtnImage: {
+    width: '100%',
+    height: '100%'
+  }
+});



## File: 'src/bizComponents/userScreen/components/MineCards.tsx'

@@ -0,0 +1,168 @@
+import { router } from 'expo-router';
+import React from 'react';
+import { Platform, Pressable, View } from 'react-native';
+import { LinearGradient } from 'react-native-linear-gradient';
+import Svg, { Path } from 'react-native-svg';
+import { Text } from '@/src/components';
+import { useCreditStore } from '@/src/store/credit';
+import { StyleSheet, dp2px } from '@/src/utils';
+import { reportClick, reportDiy } from '@/src/utils/report';
+import { Image } from '@Components/image';
+import { useShallow } from 'zustand/react/shallow';
+
+// 资源常量
+const BATTERY_ICON = require('@Assets/image/goods-shef/icon_battery.png');
+const BATTERY_GLOW = require('@Assets/image/goods-shef/battery_glow_bottom.png');
+const INVITE_BUTTON_BG = require('@Assets/image/goods-shef/invite_button_bg.png');
+
+// 电池图标组件
+const BatteryIcon = () => (
+  <Svg width={dp2px(12)} height={dp2px(12)} viewBox="0 0 12 12" fill="none">
+    <Path
+      fillRule="evenodd"
+      clipRule="evenodd"
+      d="M6.63851 5.4165 7.35284 1 2 6.62891h3.06711l-.71433 4.41649 5.35284-5.6289H6.63851Z"
+      fill="#2EE84B"
+    />
+  </Svg>
+);
+
+export const MineCards = () => {
+  const { totalCredits } = useCreditStore(
+    useShallow(state => ({
+      totalCredits: state.totalCredits
+    }))
+  );
+
+  return (
+    <View style={styles.$container}>
+      {/* 狸电池卡片 */}
+      <Pressable
+        style={styles.$cardWrapper}
+        onPress={() => {
+          reportDiy('credit', 'entrance_button-click');
+          router.push('/credit/');
+        }}
+      >
+        <LinearGradient
+          colors={['rgba(255, 255, 255, 0.08)', 'rgba(255, 255, 255, 0.04)']}
+          start={{ x: 0, y: 0 }}
+          end={{ x: 1, y: 1 }}
+          style={styles.$card}
+        >
+          <View style={styles.$textContainer}>
+            <Text style={styles.$title}>狸电池</Text>
+            <View style={styles.$batteryCountContainer}>
+              <BatteryIcon />
+              <Text style={styles.$batteryCount}>{totalCredits}</Text>
+            </View>
+          </View>
+          <View style={styles.$iconContainer}>
+            <Image source={BATTERY_GLOW} native style={styles.$glowImage} />
+            <Image source={BATTERY_ICON} native style={styles.$icon} />
+          </View>
+        </LinearGradient>
+      </Pressable>
+
+      {/* 邀请好礼卡片 */}
+      <Pressable
+        style={styles.$cardWrapper}
+        onPress={() => {
+          reportClick('invite_icon');
+          router.push('/lottery-fission/');
+        }}
+      >
+        <Image source={INVITE_BUTTON_BG} style={styles.$inviteImage} />
+      </Pressable>
+    </View>
+  );
+};
+
+const styles = StyleSheet.create({
+  $container: {
+    flexDirection: 'row',
+    gap: 11,
+    marginTop: 20,
+    backgroundColor: StyleSheet.darkTheme.background.page,
+    width: '100%'
+  },
+  $cardWrapper: {
+    flex: 1,
+    height: 68,
+    borderRadius: 12,
+    overflow: 'hidden'
+  },
+  $card: {
+    flex: 1,
+    flexDirection: 'row',
+    alignItems: 'center',
+    justifyContent: 'space-between',
+    paddingLeft: 16,
+    paddingRight: 16,
+    borderRadius: 12,
+    borderWidth: 0.3,
+    borderColor: 'rgba(255, 255, 255, 0.1)'
+  },
+  $textContainer: {
+    flexDirection: 'column',
+    marginTop: 2
+  },
+  $title: {
+    fontSize: dp2px(14),
+    ...Platform.select({
+      ios: {
+        fontWeight: '600'
+      },
+      android: {
+        fontWeight: 'bold'
+      }
+    }),
+    color: StyleSheet.darkTheme.text.primary,
+    height: 20,
+    ...Platform.select({
+      ios: {
+        marginBottom: 3
+      },
+      android: {
+        marginBottom: 2
+      }
+    })
+  },
+  $batteryCountContainer: {
+    flexDirection: 'row',
+    alignItems: 'center',
+    gap: 4
+  },
+  $batteryCount: {
+    fontSize: dp2px(12),
+    color: StyleSheet.darkTheme.text.tertiary,
+    fontFamily: StyleSheet.typography.fonts.Rany.Medium,
+    ...Platform.select({
+      android: {
+        marginTop: 1
+      }
+    })
+  },
+  $iconContainer: {
+    position: 'relative',
+    width: 52,
+    height: 52
+  },
+  $glowImage: {
+    position: 'absolute',
+    width: 166,
+    height: 68,
+    right: -20,
+    top: -7,
+    zIndex: 1
+  },
+  $icon: {
+    width: 52,
+    height: 52,
+    zIndex: 3
+  },
+  $inviteImage: {
+    width: '100%',
+    height: 68
+  }
+});



## File: 'src/bizComponents/userScreen/components/UserHeader.tsx'

@@ -0,0 +1,222 @@
+import { router } from 'expo-router';
+import React, { useMemo } from 'react';
+import { View } from 'react-native';
+import Animated, {
+  SharedValue,
+  useAnimatedStyle,
+  withTiming
+} from 'react-native-reanimated';
+import { Header, Icon, Text } from '@/src/components';
+import { Avatar } from '@/src/components/avatar';
+import { Follow } from '@/src/components/follow';
+import { usePersistFn } from '@/src/hooks';
+import { useUserInfoStore } from '@/src/store/userInfo';
+import { CommonColor } from '@/src/theme/colors/common';
+import { UserProfile, UserSocialStat } from '@/src/types';
+import { StyleSheet, dp2px, isIos } from '@/src/utils';
+import { stirngRemoveEnter } from '@/src/utils/opt/replace';
+import { reportClick } from '@/src/utils/report';
+import { useShallow } from 'zustand/react/shallow';
+import { MineButtons } from './MineButtons';
+
+interface UserHeaderProps {
+  profile?: UserProfile;
+  stat?: UserSocialStat;
+  hasSlideUp?: SharedValue<boolean>;
+  isMine?: boolean;
+  isRootPage?: boolean;
+  onBack?: () => void;
+  safeTop: number;
+}
+
+export const UserHeaderRight = ({
+  profile,
+  stat,
+  isMine
+}: {
+  profile?: UserProfile;
+  stat: UserSocialStat;
+  isMine?: boolean;
+}) => {
+  const { updateStat } = useUserInfoStore(
+    useShallow(state => ({
+      updateStat: state.updateStat
+    }))
+  );
+  const onUpdatefollow = usePersistFn((followed: boolean) => {
+    reportClick('follow_button', { userId: profile?.uid || '', followed });
+    updateStat(profile?.uid || '', {
+      followed
+    });
+  });
+
+  if (isMine) {
+    return <MineButtons />;
+  }
+  return (
+    <Follow
+      buttonStyle={styles.$followBtn}
+      followed={!!stat.followed}
+      beingFollowed={!!stat.beingFollowed}
+      uid={profile?.uid || ''}
+      onUnfollow={() => onUpdatefollow(false)}
+      onFollow={() => onUpdatefollow(true)}
+    />
+  );
+};
+
+export const UserHeader = ({
+  profile,
+  stat,
+  hasSlideUp,
+  isMine = false,
+  isRootPage = false,
+  onBack,
+  safeTop
+}: UserHeaderProps) => {
+  const handleBack = usePersistFn(() => {
+    if (isRootPage) {
+      return;
+    }
+    console.log('handleBack');
+    reportClick('button', {
+      user_button: 1,
+      identity_status: isMine ? '0' : '1'
+    });
+    if (onBack) {
+      onBack();
+    } else {
+      router.back();
+    }
+  });
+
+  const displayName = useMemo(() => {
+    return stirngRemoveEnter(profile?.name || '-');
+  }, [profile?.name]);
+
+  // 只使用 hasSlideUp 控制透明度
+  const $headerStyle = useAnimatedStyle(() => {
+    'worklet';
+    return {
+      pointerEvents: hasSlideUp?.value ? 'auto' : 'none',
+      opacity: withTiming(hasSlideUp?.value ? 1 : 0)
+    };
+  }, [hasSlideUp]);
+
+  // 添加独立的返回按钮样式
+  const $backButtonStyle = useAnimatedStyle(() => {
+    'worklet';
+    return {
+      opacity: withTiming(hasSlideUp?.value ? 0 : 1),
+      top: safeTop + 6
+    };
+  }, [hasSlideUp, isRootPage, safeTop]);
+
+  return (
+    <>
+      <Animated.View
+        style={[
+          styles.$headerContainer,
+          {
+            paddingTop: safeTop,
+            height: dp2px(44) + safeTop
+          },
+          $headerStyle
+        ]}
+      >
+        <Header
+          themeColors={{ textColor: CommonColor.white }}
+          headerStyle={styles.$header}
+          backButton={
+            !isRootPage ? (
+              <Icon
+                onPress={handleBack}
+                icon="back"
+                size={dp2px(16)}
+                color="#fff"
+              />
+            ) : null
+          }
+          headerLeft={() => (
+            <View style={styles.$headerBrand}>
+              <Avatar
+                profile={profile}
+                size={dp2px(24)}
+                showTag={false}
+                showPendant={false}
+              />
+              <Text style={styles.$headerName} numberOfLines={1}>
+                {displayName}
+              </Text>
+            </View>
+          )}
+          headerRight={() =>
+            stat ? (
+              <UserHeaderRight profile={profile} stat={stat} isMine={isMine} />
+            ) : null
+          }
+        />
+      </Animated.View>
+
+      {/* 添加独立的返回按钮 */}
+      {!isRootPage && (
+        <Animated.View style={[styles.$backButton, $backButtonStyle]}>
+          <Icon
+            onPress={handleBack}
+            icon="back"
+            size={22}
+            color="#fff"
+            hitSlop={20}
+          />
+        </Animated.View>
+      )}
+    </>
+  );
+};
+
+const styles = StyleSheet.create({
+  $headerContainer: {
+    position: 'absolute',
+    left: 0,
+    right: 0,
+    top: 3,
+    zIndex: 2,
+    backgroundColor: StyleSheet.darkTheme.background.page
+  },
+  $header: {
+    borderBottomWidth: 0,
+    paddingLeft: 16,
+    paddingRight: 16
+  },
+  $followBtn: {
+    width: 76,
+    height: 30
+  },
+  $headerBrand: {
+    display: 'flex',
+    justifyContent: 'flex-start',
+    alignItems: 'center',
+    flexDirection: 'row',
+    width: 200
+  },
+  // FIXME(fuxiao): 处理很长的名字
+  $headerName: {
+    fontSize: 16,
+    fontWeight: isIos ? '600' : 'bold',
+    marginLeft: 6,
+    color: StyleSheet.darkTheme.text.primary
+  },
+  // 新增返回按钮的静态样式
+  $backButton: {
+    position: 'absolute',
+    left: 16,
+    zIndex: 300,
+    paddingRight: 2,
+    width: 32,
+    height: 32,
+    borderRadius: 16,
+    backgroundColor: StyleSheet.darkTheme.background.transTip,
+    justifyContent: 'center',
+    alignItems: 'center'
+  }
+});



## File: 'src/bizComponents/userScreen/components/UserInfo.tsx'

@@ -0,0 +1,200 @@
+import { useMemoizedFn } from 'ahooks';
+import { router } from 'expo-router';
+import { useEffect } from 'react';
+import { TouchableOpacity, View } from 'react-native';
+import { Text } from '@/src/components';
+import { Avatar } from '@/src/components/avatar';
+import { SwitchName, useControlStore } from '@/src/store/control';
+import { useEditPendantStore } from '@/src/store/edit-pendant';
+import { darkTheme, typography } from '@/src/theme';
+import { $USE_FONT, $Z_INDEXES } from '@/src/theme/variable';
+import { StyleSheet, dp2px, isIos } from '@/src/utils';
+import { stirngRemoveEnter } from '@/src/utils/opt/replace';
+import { reportClick } from '@/src/utils/report';
+import { Image } from '@Components/image';
+import { userPerformanceCollector } from '../../../utils/report/userPageCollector';
+import { handleEditProfile } from '../utils';
+import { UserProfile } from '@/proto-registry/src/web/raccoon/uinfo/uinfo_pb';
+import { useIsFocused } from '@react-navigation/native';
+import { useShallow } from 'zustand/react/shallow';
+
+const PENDANT_TIP_BG = require('@Assets/user/pendant-tip-bg.png');
+
+export function UserInfo({
+  currentUser,
+  isMine,
+  showFeedBack
+}: {
+  currentUser?: UserProfile;
+  isMine?: boolean;
+  showFeedBack?: () => void;
+}) {
+  // FIXME(fuxiao): 新挂件逻辑与 UI 确认
+  const { newPendant, fetchNewPendant } = useEditPendantStore(
+    useShallow(state => ({
+      newPendant: state.newPendant,
+      fetchNewPendant: state.fetchNewPendant
+    }))
+  );
+  const isFocus = useIsFocused();
+
+  useEffect(() => {
+    if (isFocus && isMine) {
+      fetchNewPendant();
+    }
+  }, [fetchNewPendant, isFocus, isMine]);
+
+  const currentUserName = stirngRemoveEnter(currentUser?.name);
+
+  const handleEditAvatar = useMemoizedFn(() => {
+    reportClick('button', {
+      user_button: 2,
+      identity_status: isMine ? '0' : '1'
+    });
+    if (isMine) {
+      router.push('/avatar-edit/');
+    } else {
+      if (
+        useControlStore.getState().checkIsOpen(SwitchName.ENABLE_USER_REPORT)
+      ) {
+        showFeedBack?.();
+      }
+    }
+  });
+
+  const handleEditProfileClick = useMemoizedFn(() => {
+    if (isMine) {
+      handleEditProfile();
+    }
+  });
+
+  return (
+    <View style={styles.$container}>
+      <View style={styles.$avatarContainer}>
+        <Avatar
+          profile={currentUser}
+          size={90}
+          showTag={true}
+          showPendant={true}
+          onPress={handleEditAvatar}
+          borderWidth={1}
+          borderColor={StyleSheet.darkTheme.border.avatar}
+          outerBorder={true}
+          onLoad={() => {
+            if (currentUser?.uid) {
+              userPerformanceCollector.markPerformanceTimestamp(
+                'user_info_available_timestamp',
+                currentUser?.uid
+              );
+            }
+          }}
+        />
+
+        {newPendant && (
+          <View style={styles.$pendantTipContainer}>
+            <Image style={styles.$pendantTip} source={PENDANT_TIP_BG} />
+            <View style={styles.$pendantContent}>
+              <Image
+                source={newPendant?.pendantUrl || ''}
+                style={styles.$pendantImage}
+              />
+              <Text style={styles.$newPendantText}>{'新挂件'}</Text>
+            </View>
+            <View style={styles.$dotTip} />
+          </View>
+        )}
+      </View>
+
+      <TouchableOpacity
+        activeOpacity={0.7}
+        onPress={handleEditProfileClick}
+        disabled={!isMine}
+        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
+        style={styles.$infoContainer}
+      >
+        <Text
+          style={$USE_FONT(
+            isMine && currentUser?.isNameUpdated === false
+              ? darkTheme.text.primary
+              : darkTheme.text.primary,
+            typography.fonts.baba.bold,
+            dp2px(18),
+            'normal',
+            isIos ? '600' : 'bold',
+            25
+          )}
+          ellipsizeMode="tail"
+          numberOfLines={1}
+        >
+          {currentUserName}
+        </Text>
+      </TouchableOpacity>
+    </View>
+  );
+}
+
+const styles = StyleSheet.create({
+  $container: {
+    flexDirection: 'row',
+    alignItems: 'center',
+    height: 90,
+    marginBottom: 20,
+    zIndex: $Z_INDEXES.z200 + 1,
+    gap: 12
+  },
+  $avatarContainer: {
+    position: 'relative'
+  },
+  $infoContainer: {
+    flex: 1,
+    justifyContent: 'center'
+  },
+  $pendantTipContainer: {
+    position: 'absolute',
+    top: -20,
+    right: -30
+  },
+  $pendantTip: {
+    width: 91,
+    height: 45,
+    position: 'absolute'
+  },
+  $pendantContent: {
+    position: 'absolute',
+    flexDirection: 'row',
+    justifyContent: 'center',
+    alignItems: 'center',
+    gap: 2,
+    paddingLeft: 6,
+    top: 4,
+    left: 4
+  },
+  $pendantImage: {
+    width: 20,
+    height: 20
+  },
+  $dotTip: {
+    width: 8,
+    height: 8,
+    backgroundColor: '#fff',
+    borderRadius: 4,
+    left: 4,
+    top: 30,
+    position: 'absolute'
+  },
+  $newPendantText: {
+    fontSize: 11,
+    fontWeight: '600'
+  },
+  $lottieContainer: {
+    marginTop: 5
+  },
+  $lottieAnimation: {
+    width: 111,
+    height: 21
+  },
+  $lottieCustomAnimation: {
+    width: 139,
+    height: 21
+  }
+});



## File: 'src/bizComponents/userScreen/components/UserPanel.tsx'

@@ -0,0 +1,334 @@
+import { useMemoizedFn } from 'ahooks';
+import React, { ReactElement, RefObject, useMemo } from 'react';
+import { Pressable, TextStyle, View } from 'react-native';
+import PagerView from 'react-native-pager-view';
+import Animated, {
+  SharedValue,
+  useAnimatedStyle,
+  useDerivedValue,
+  useSharedValue,
+  withTiming
+} from 'react-native-reanimated';
+import { useSafeAreaInsets } from 'react-native-safe-area-context';
+import { UserHeaderRight } from '@/src/bizComponents/userScreen/components/UserHeader';
+import { Follow } from '@/src/components/follow';
+import { SimpleTabs } from '@/src/components/tabs/simple-tabs';
+import { darkTheme } from '@/src/theme';
+import { UserSocialStat } from '@/src/types';
+import { CommonEventBus } from '@/src/utils/event';
+import { reportClick } from '@/src/utils/report';
+import { Icon, Image, Text } from '../../../components';
+import { DynamicWidthTabs } from '../../../components/tabs/dynamic-tabs';
+import { StyleSheet, dp2px, fixedPx, isIos } from '../../../utils';
+import {
+  UserPageTab,
+  authorPageTabMap,
+  authorTabs,
+  visitorPageTabMap,
+  visitorTabs
+} from '../constants';
+import { calculatePanelHeight } from '../utils';
+import { UserProfile } from '@/proto-registry/src/web/raccoon/uinfo/uinfo_pb';
+import { MineButtons } from './MineButtons';
+import { MineCards } from './MineCards';
+import { UserInfo } from './UserInfo';
+import { UserStats } from './UserStats';
+
+const GOODS_MASK = require('@Assets/image/goods-shef/mask.png');
+const MASK_HEIGHT = dp2px(120);
+const BACKGROUND_OFFSET = -0.3; // 背景底部偏移量，防止安卓有一小段空隙
+
+// 定义标签项类型
+export interface TabItem {
+  key: UserPageTab;
+  title: string;
+  width: number;
+  renderItem?: (props: {
+    isActive: boolean;
+    textStyle: TextStyle[];
+  }) => ReactElement;
+}
+
+interface UserPanelProps {
+  currentUser: UserProfile;
+  stat?: UserSocialStat;
+  isMine: boolean;
+  showFeedBack: () => void;
+  hasGoods: boolean;
+  currentTab: UserPageTab;
+  $animatedIndictor: SharedValue<number>;
+  pagerRef: RefObject<PagerView>;
+  onAlbumPress: () => void;
+  safeTop: number;
+  hasSlideUp?: SharedValue<boolean>;
+  checkEnterGoods?: () => void;
+  tabConfig?: TabItem[]; // 使用明确的类型
+}
+
+export function UserPanel({
+  currentUser,
+  stat,
+  isMine,
+  showFeedBack,
+  currentTab,
+  $animatedIndictor,
+  pagerRef,
+  onAlbumPress,
+  hasGoods,
+  safeTop,
+  hasSlideUp,
+  checkEnterGoods,
+  tabConfig
+}: UserPanelProps) {
+  // 获取所有可能的高度配置
+  const heightConfigs = useMemo(() => calculatePanelHeight(), []);
+
+  // 计算固定值，避免在UI线程中计算
+  const maskHeightWithOffset = useMemo(
+    () => MASK_HEIGHT + BACKGROUND_OFFSET,
+    []
+  );
+  const backgroundTopOffset = useMemo(
+    () => safeTop + maskHeightWithOffset,
+    [safeTop]
+  );
+
+  // 为动画创建共享值
+  const $topHeight = useSharedValue(
+    isMine
+      ? heightConfigs.mineNoGoods.topHeight
+      : heightConfigs.visitorNoGoods.topHeight
+  );
+  const $maskTop = useSharedValue(
+    isMine
+      ? heightConfigs.mineNoGoods.maskTop
+      : heightConfigs.visitorNoGoods.maskTop
+  );
+
+  // 使用 useDerivedValue 替代 useEffect 来更新共享值
+  useDerivedValue(() => {
+    'worklet';
+    const config = isMine
+      ? hasGoods
+        ? heightConfigs.mineWithGoods
+        : heightConfigs.mineNoGoods
+      : hasGoods
+        ? heightConfigs.visitorWithGoods
+        : heightConfigs.visitorNoGoods;
+
+    $topHeight.value = withTiming(config.topHeight, { duration: 300 });
+    $maskTop.value = withTiming(config.maskTop, { duration: 300 });
+  }, [isMine, hasGoods]);
+
+  const onPressTab = useMemoizedFn((_, type) => {
+    if (type === UserPageTab.SECRET) {
+      reportClick('secret_button', { type: 'video' });
+    } else if (type === UserPageTab.MY_ROLE) {
+      reportClick('role_button');
+    } else {
+      reportClick('button', {
+        user_button: type === UserPageTab.LIKE ? 9 : 8,
+        identity_status: isMine ? '0' : '1'
+      });
+    }
+
+    const tabMap = isMine ? authorPageTabMap : visitorPageTabMap;
+    const tab = tabMap[type as UserPageTab];
+    pagerRef.current?.setPage(tab || 0);
+  });
+
+  const $buttonsStyle = useAnimatedStyle(() => {
+    return {
+      opacity: hasSlideUp?.value ? 0 : 1
+    };
+  }, [hasSlideUp]);
+
+  // 用于容器高度的动画样式
+  const $containerStyle = useAnimatedStyle(() => {
+    'worklet';
+    return {
+      height: $topHeight.value + safeTop
+    };
+  });
+
+  // 用于背景底部的动画样式
+  const $backgroundStyle = useAnimatedStyle(() => {
+    'worklet';
+    return {
+      top: $maskTop.value + backgroundTopOffset
+    };
+  });
+
+  // 用于遮罩的动画样式
+  const $maskStyle = useAnimatedStyle(() => {
+    'worklet';
+    return {
+      position: 'absolute',
+      left: 0,
+      right: 0,
+      top: $maskTop.value + safeTop,
+      zIndex: 0
+    };
+  });
+
+  // 背景点击区域的高度
+  const clickAreaHeight = useMemo(
+    () => (hasGoods ? dp2px(179) : dp2px(90)),
+    [hasGoods]
+  );
+
+  return (
+    <>
+      <Animated.View style={[styles.$backgroundBottom, $backgroundStyle]} />
+      <Animated.View style={[styles.$container, $containerStyle]}>
+        {/* 添加背景墙点击区域 - 改为普通Pressable */}
+        <Pressable
+          style={[
+            styles.$backgroundClickArea,
+            { top: safeTop, height: clickAreaHeight }
+          ]}
+          onPress={checkEnterGoods}
+        />
+
+        {/* 将 Image 放入 Animated.View 中 */}
+        <Animated.View style={$maskStyle}>
+          <Image
+            source={GOODS_MASK}
+            // 使用 native 会导致背景渐变效果失效
+            contentFit="fill"
+            contentPosition="top"
+            style={styles.$goodsMask}
+            onLoadEnd={() => {
+              CommonEventBus.emit('goodsMaskLoaded', { loaded: true });
+            }}
+          />
+        </Animated.View>
+
+        <UserInfo
+          currentUser={currentUser}
+          isMine={isMine}
+          showFeedBack={showFeedBack}
+        />
+        <View style={styles.$statsWrap}>
+          <UserStats
+            stat={stat}
+            isMine={isMine}
+            currentUserId={currentUser?.uid}
+            currentUserName={currentUser?.name}
+          />
+          {isMine && <MineButtons />}
+          <Animated.View style={$buttonsStyle}>
+            {!isMine && stat && (
+              <UserHeaderRight
+                profile={currentUser}
+                stat={stat}
+                isMine={isMine}
+              />
+            )}
+          </Animated.View>
+        </View>
+
+        {/* MineCards - 仅在个人主页显示 */}
+        {isMine && <MineCards />}
+
+        {/* Tabs与图集按钮 */}
+        <View style={styles.$tabContainer}>
+          <DynamicWidthTabs
+            current={currentTab}
+            items={tabConfig || (isMine ? authorTabs : visitorTabs)}
+            animatedTabIndex={$animatedIndictor}
+            tabBarStyle={styles.$tabBar}
+            tabGap={0}
+            itemStyle={styles.$tabItemStyle}
+            itemTextStyle={styles.$tabItemTextStyle}
+            indicatorBottomOffset={-6}
+            onPressTab={onPressTab}
+          />
+          {isMine && (
+            <Pressable style={styles.$albumButton} onPress={onAlbumPress}>
+              <Text style={styles.$albumButtonText}>图集</Text>
+              <Icon
+                icon="right_outline2"
+                size={16}
+                color={darkTheme.text.disabled}
+              />
+            </Pressable>
+          )}
+        </View>
+      </Animated.View>
+    </>
+  );
+}
+
+const styles = StyleSheet.create({
+  $container: {
+    width: '100%',
+    position: 'relative',
+    justifyContent: 'flex-end',
+    paddingLeft: 16,
+    paddingRight: 16
+  },
+  $backgroundBottom: {
+    position: 'absolute',
+    left: 0,
+    right: 0,
+    bottom: 0,
+    backgroundColor: darkTheme.background.page,
+    zIndex: -1
+  },
+  $goodsMask: {
+    left: 0,
+    right: 0,
+    width: '100%',
+    height: fixedPx(MASK_HEIGHT),
+    position: 'absolute',
+    zIndex: 0
+  },
+  $statsWrap: {
+    flexDirection: 'row',
+    alignItems: 'center',
+    justifyContent: 'space-between',
+    zIndex: 1
+  },
+  $tabContainer: {
+    position: 'relative',
+    flexDirection: 'row',
+    alignItems: 'center',
+    width: '100%',
+    zIndex: 1,
+    marginTop: 8
+  },
+  $tabBar: {
+    marginLeft: fixedPx(-16),
+    height: 44
+  },
+  $tabItemStyle: {
+    justifyContent: 'center',
+    alignItems: 'center',
+    height: 18
+  },
+  $tabItemTextStyle: {
+    fontSize: 14,
+    textAlign: 'center',
+    fontWeight: '500'
+  },
+  $albumButton: {
+    position: 'absolute',
+    right: 0,
+    height: 44,
+    justifyContent: 'center',
+    alignItems: 'center',
+    flexDirection: 'row'
+  },
+  $albumButtonText: {
+    marginRight: 1,
+    fontSize: 14,
+    color: darkTheme.text.disabled
+  },
+  $backgroundClickArea: {
+    position: 'absolute',
+    left: 0,
+    right: 0,
+    zIndex: 200
+  }
+});



## File: 'src/bizComponents/userScreen/components/UserStats.tsx'

@@ -0,0 +1,150 @@
+import { useMemoizedFn } from 'ahooks';
+import { router } from 'expo-router';
+import { Pressable, TextStyle, View, ViewStyle } from 'react-native';
+import { Text } from '@/src/components';
+import { typography } from '@/src/theme';
+import { UserSocialStat } from '@/src/types';
+import { formatUserNumber } from '@/src/utils/opt/transNum';
+import { reportClick } from '@/src/utils/report';
+import { showImageConfirm } from '@Components/popup/confirmModalGlobal/Confirm';
+import { StyleSheet } from '@Utils/StyleSheet';
+import { Theme } from '../../../theme/colors/type';
+import dp2px from '../../../utils/dp2px';
+
+const LIKE_COVER = require('@Assets/user/userLike.png');
+
+const HIT_SLOP = { top: 10, bottom: 10, left: 15, right: 15 };
+
+export function UserStats({
+  stat,
+  isMine,
+  currentUserId,
+  currentUserName
+}: {
+  stat?: UserSocialStat;
+  isMine?: boolean;
+  currentUserId?: string;
+  currentUserName?: string;
+}) {
+  const onPressFollowAndFans = useMemoizedFn((defaultTab: string) => {
+    reportClick('button', {
+      user_button: defaultTab === 'follow' ? 4 : 5,
+      identity_status: isMine ? '0' : '1'
+    });
+    router.push({
+      pathname: '/follow-fan/',
+      params: {
+        defaultTab,
+        uid: currentUserId || ''
+      }
+    });
+  });
+
+  const showLikeModal = useMemoizedFn(() => {
+    reportClick('button', {
+      user_button: 6,
+      identity_status: isMine ? '0' : '1'
+    });
+    showImageConfirm({
+      image: LIKE_COVER,
+      title: currentUserName,
+      content: `共获得${stat?.beingLikeds}个赞`,
+      confirmText: '知道了',
+      cancelText: '#hiddenCancel#',
+      onConfirm: ({ close }) => {
+        close();
+      },
+      theme: Theme.DARK
+    });
+  });
+
+  const showSameModal = useMemoizedFn(() => {
+    reportClick('button', {
+      user_button: 7,
+      identity_status: isMine ? '0' : '1'
+    });
+
+    router.push({
+      pathname: `/fame/${currentUserId}`,
+      params: {
+        identity_status: isMine ? 0 : 1
+      }
+    });
+  });
+
+  return (
+    <View style={styles.$numsWrap}>
+      <Pressable
+        onPress={() => onPressFollowAndFans('follow')}
+        hitSlop={HIT_SLOP}
+        style={styles.$numItem}
+      >
+        <Text style={styles.$num}>
+          {formatUserNumber(stat?.followings?.toLocaleString() || 0)}
+        </Text>
+        <Text style={styles.$label}>关注</Text>
+      </Pressable>
+      <Pressable
+        onPress={() => onPressFollowAndFans('fans')}
+        hitSlop={HIT_SLOP}
+        style={styles.$numItem}
+      >
+        <Text style={styles.$num}>
+          {formatUserNumber(stat?.fans?.toLocaleString() || 0)}
+        </Text>
+        <Text style={styles.$label}>粉丝</Text>
+      </Pressable>
+      <Pressable
+        onPress={showLikeModal}
+        hitSlop={HIT_SLOP}
+        style={styles.$numItem}
+      >
+        <Text style={styles.$num}>
+          {formatUserNumber(stat?.beingLikeds?.toLocaleString() || 0)}
+        </Text>
+        <Text style={styles.$label}>获赞</Text>
+      </Pressable>
+      <Pressable
+        onPress={showSameModal}
+        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
+        style={styles.$numItem}
+      >
+        <Text style={styles.$num}>
+          {formatUserNumber(stat?.beingCopieds?.toLocaleString() || 0)}
+        </Text>
+        <Text style={styles.$label}>声望</Text>
+      </Pressable>
+    </View>
+  );
+}
+
+const styles = StyleSheet.create({
+  $numsWrap: {
+    maxWidth: dp2px(196),
+    flex: 1,
+    flexDirection: 'row',
+    justifyContent: 'space-between'
+  },
+  $numItem: {
+    alignItems: 'center',
+    flexDirection: 'column',
+    paddingBottom: 2
+  },
+  $num: {
+    fontSize: dp2px(18),
+    height: 20,
+    textAlign: 'center',
+    fontFamily: typography.fonts.Barlow.SemiBold,
+    color: StyleSheet.darkTheme.text.primary,
+    letterSpacing: -0.02
+  },
+  $label: {
+    color: StyleSheet.darkTheme.text.disabled,
+    marginTop: 4,
+    fontSize: dp2px(12),
+    lineHeight: dp2px(17),
+    height: 17,
+    fontWeight: '500',
+    textAlign: 'center'
+  }
+});



## File: 'src/bizComponents/userScreen/hooks/useHandleSwipeBack.ts'

@@ -0,0 +1,32 @@
+import { useMemoizedFn } from 'ahooks';
+import { useEffect } from 'react';
+import { CommonEventBus } from '@/src/utils/event';
+import { useRetentionPopup } from './useRetentionPopup';
+
+export const useHandleSwipeBack = (
+  checkRetentionAndReport: ReturnType<
+    typeof useRetentionPopup
+  >['checkRetentionAndReport']
+) => {
+  // 使用 useMemoizedFn 包装事件处理函数，确保函数引用稳定
+  const handleUserScreenBack = useMemoizedFn(
+    (data: { resolve: () => void; ownerUid: string }) => {
+      const { resolve } = data;
+      const shouldShowPopup = checkRetentionAndReport(resolve);
+      if (!shouldShowPopup) {
+        // 如果不需要显示弹窗，正常返回
+        resolve();
+      }
+      // 否则阻止返回，不调用 resolve
+    }
+  );
+
+  // 注册事件处理函数
+  useEffect(() => {
+    CommonEventBus.on('handleUserScreenBack', handleUserScreenBack);
+
+    return () => {
+      CommonEventBus.off('handleUserScreenBack', handleUserScreenBack);
+    };
+  }, [handleUserScreenBack]);
+};



## File: 'src/bizComponents/userScreen/infoProfile/index.tsx'

@@ -1,499 +1,0 @@ 
-import { useMemoizedFn } from 'ahooks';
-import dayjs from 'dayjs';
-import { router } from 'expo-router';
-import AnimatedLottieView, {
-  AnimatedLottieViewProps
-} from 'lottie-react-native';
-import {
-  Component,
-  LegacyRef,
-  memo,
-  useEffect,
-  useMemo,
-  useRef,
-  useState
-} from 'react';
-import {
-  Text,
-  TextStyle,
-  TouchableOpacity,
-  View,
-  ViewStyle
-} from 'react-native';
-import Animated, {
-  AnimateProps,
-  useAnimatedStyle,
-  useSharedValue
-} from 'react-native-reanimated';
-import { Icon } from '@/src/components';
-import { Avatar } from '@/src/components/avatar';
-import { CREDIT_LIMIT } from '@/src/components/credit-cas';
-import { Follow } from '@/src/components/follow';
-import { getSkinConfig } from '@/src/components/skin/getSkinConfig';
-import { SwitchName, useControlStore } from '@/src/store/control';
-import { useCreditStore } from '@/src/store/credit';
-import { useResourceStore } from '@/src/store/resource';
-import { useStorageStore } from '@/src/store/storage';
-import { useUserInfoStore } from '@/src/store/userInfo';
-import { darkTheme, typography } from '@/src/theme';
-import { $USE_FONT, $Z_INDEXES, $flexCenter } from '@/src/theme/variable';
-import { dp2px, isIos } from '@/src/utils';
-import { stirngRemoveEnter } from '@/src/utils/opt/replace';
-import { reportClick, reportDiy, reportExpo } from '@/src/utils/report';
-import { Image, ImageStyle } from '@Components/image';
-import { StyleSheet } from '@Utils/StyleSheet';
-import { UserProfile } from '@/proto-registry/src/web/raccoon/uinfo/uinfo_pb';
-import { useIsFocused } from '@react-navigation/native';
-import TransparentVideo from '@step.ai/react-native-transparent-video';
-import { useShallow } from 'zustand/react/shallow';
-
-const RED_LIGHT = require('@Assets/lottie/redlight.json');
-const GREEN_LIGHT = require('@Assets/lottie/greenlight.json');
-
-interface TInfoProfileProps {
-  isRootPage: boolean;
-  isMine: boolean;
-  creditOpacity: number;
-  id: string;
-  showAvatar?: boolean;
-  currentUser?: UserProfile;
-  showFeedBack?: () => void;
-  onBack?: () => void;
-  showBackButton?: boolean;
-}
-
-const InfoProfile = memo(
-  ({
-    isRootPage,
-    isMine,
-    creditOpacity,
-    id,
-    showAvatar,
-    currentUser,
-    showFeedBack,
-    onBack,
-    showBackButton = true
-  }: TInfoProfileProps) => {
-    const skinConfig = getSkinConfig();
-
-    const creditShareOpacity = useSharedValue(0);
-    const videoRef = useRef<TransparentVideo | null>(null);
-
-    const creditStyle = useAnimatedStyle(() => ({
-      opacity: creditShareOpacity.value
-    }));
-
-    const { totalCredits } = useCreditStore(
-      useShallow(state => ({
-        totalCredits: state.totalCredits
-      }))
-    );
-
-    useEffect(() => {
-      const getCredits = async () => {
-        await useCreditStore.getState().syncCredits();
-        creditShareOpacity.value = 1;
-      };
-      isMine && getCredits();
-    }, [isMine]);
-
-    useEffect(() => {
-      if (creditOpacity) {
-        creditShareOpacity.value = 1;
-      } else {
-        creditShareOpacity.value = 0;
-      }
-    }, [creditOpacity]);
-    const lightRef = useRef<AnimatedLottieView>(null);
-
-    const isMinus = totalCredits < CREDIT_LIMIT ? true : false;
-    const lottieSource = isMinus ? RED_LIGHT : GREEN_LIGHT;
-
-    useEffect(() => {
-      lightRef.current?.play();
-      const timer = setInterval(() => {
-        lightRef.current?.play();
-      }, 10 * 1000);
-      return () => {
-        clearInterval(timer);
-      };
-    }, []);
-
-    const { profile, stat, updateStat } = useUserInfoStore(
-      useShallow(state => {
-        const userInfo = id ? state.getUserInfo(id) : undefined;
-        return {
-          profile: userInfo?.profile,
-          stat: userInfo?.stat,
-          updateStat: state.updateStat
-        };
-      })
-    );
-
-    const onUpdatefollow = (followed: boolean) => {
-      reportClick('follow_button', { userId: id, followed });
-      updateStat(id, {
-        followed
-      });
-    };
-
-    const handleEditAvatar = useMemoizedFn(() => {
-      reportClick('button', {
-        user_button: 2,
-        identity_status: isMine ? '0' : '1'
-      });
-      if (isMine) {
-        router.push('/avatar-edit/');
-      } else {
-        if (
-          useControlStore.getState().checkIsOpen(SwitchName.ENABLE_USER_REPORT)
-        ) {
-          showFeedBack?.();
-        }
-      }
-    });
-
-    const currentUserName = stirngRemoveEnter(currentUser?.name);
-    const lotteryVideo = useMemo(
-      () => ({
-        uri: useResourceStore
-          .getState()
-          .getResource(
-            'https://mediafile.lipuhome.[BASE64_DATA:56chars].mp4'
-          )
-      }),
-      []
-    );
-
-    const isFocus = useIsFocused();
-
-    const [showDailyTip, setShowDailyTip] = useState(false);
-
-    useEffect(() => {
-      const isDailyFissionGuide =
-        useStorageStore.getState().isDailyFissionGuide;
-      if (
-        !isDailyFissionGuide ||
-        (isDailyFissionGuide &&
-          isDailyFissionGuide - dayjs().startOf('day').valueOf() < 0)
-      ) {
-        useStorageStore.getState().__setStorage({
-          isDailyFissionGuide: Date.now()
-        });
-        setShowDailyTip(true);
-      } else {
-        setShowDailyTip(false);
-      }
-    }, [isFocus]);
-
-    return (
-      <View
-        style={{
-          flexDirection: 'row',
-          width: '100%',
-          justifyContent: 'space-between',
-          alignItems: 'center',
-          minHeight: 58,
-          position: 'relative',
-          paddingHorizontal: 16
-        }}
-      >
-        {!isRootPage ? (
-          <View style={{ width: 24, height: 24 }}>
-            {showBackButton && (
-              <TouchableOpacity
-                onPressIn={() => {
-                  reportClick('button', {
-                    user_button: 1,
-                    identity_status: isMine ? '0' : '1'
-                  });
-                  onBack ? onBack() : router.back();
-                }}
-              >
-                <Icon icon={skinConfig ? 'back_pw' : 'back'} size={24} />
-              </TouchableOpacity>
-            )}
-          </View>
-        ) : (
-          <View />
-        )}
-        <View
-          style={{
-            flexDirection: 'row',
-            position: 'relative',
-            justifyContent: 'space-between',
-            flex: 1,
-            marginTop: 4
-          }}
-        >
-          {showAvatar ? (
-            <Animated.View
-              style={{
-                flexDirection: 'row',
-                alignItems: 'center',
-                flex: 1
-              }}
-            >
-              <View style={{ width: 32, height: 'auto', marginRight: 10 }}>
-                <Avatar
-                  profile={currentUser}
-                  size={32}
-                  showTag={true}
-                  showPendant={creditOpacity < 1}
-                  onPress={handleEditAvatar}
-                  borderWidth={2}
-                  borderColor={'#262629'}
-                />
-              </View>
-              <Text
-                ellipsizeMode="tail"
-                numberOfLines={1}
-                allowFontScaling={false}
-                style={{
-                  width: 180,
-                  ...$USE_FONT(
-                    isMine && currentUser?.isNameUpdated === false
-                      ? darkTheme.text.disabled
-                      : darkTheme.text.primary,
-                    typography.fonts.baba.medium,
-                    dp2px(14),
-                    'normal',
-                    isIos ? '500' : 'bold',
-                    dp2px(19.6)
-                  )
-                }}
-              >
-                {currentUserName}
-              </Text>
-            </Animated.View>
-          ) : isMine ? (
-            <>
-              <Animated.View style={[creditStyle]}>
-                <TouchableOpacity
-                  onPressIn={() => {
-                    reportDiy('credit', 'entrance_button-click');
-                    router.push('/credit/');
-                  }}
-                  style={{
-                    marginEnd: 16,
-                    width: 32,
-                    height: 32,
-                    transform: [
-                      {
-                        translateY: 0
-                      }
-                    ]
-                  }}
-                >
-                  <Icon
-                    icon={!isMinus ? 'credit_minus' : 'credit_plus'}
-                    size={24}
-                    style={{ opacity: 0 }}
-                  />
-                  <View style={$creditLottie}>
-                    <AnimatedLottieView
-                      source={lottieSource}
-                      ref={
-                        lightRef as LegacyRef<
-                          Component<AnimateProps<AnimatedLottieViewProps>>
-                        >
-                      }
-                      loop={false}
-                    />
-                  </View>
-                </TouchableOpacity>
-              </Animated.View>
-              <TouchableOpacity
-                style={st.lotteryEntry}
-                onPress={() => {
-                  reportClick('invite_icon');
-                  router.push('/lottery-fission/');
-                }}
-              >
-                {/* 目前没有 unload api */}
-                {isFocus ? (
-                  <TransparentVideo
-                    ref={videoRef}
-                    isAutoPlay
-                    loop
-                    style={st.lotteryEntryVideo}
-                    source={lotteryVideo}
-                  />
-                ) : null}
-                {showDailyTip ? (
-                  <View style={[$dailyTip]}>
-                    <Image
-                      style={{
-                        width: 120,
-                        height: 36
-                      }}
-                      source={
-                        'https://resource.lipuhome.com/resource/img/prod/20250307/82994ac4159c85c133be6c132b71d8d0.png'
-                      }
-                    />
-                    <Text
-                      numberOfLines={1}
-                      style={{
-                        ...$USE_FONT(
-                          'StyleSheet.currentColors.titleGray',
-                          typography.fonts.pingfangSC.normal,
-                          dp2px(12),
-                          'normal',
-                          isIos ? '500' : 'bold',
-                          dp2px(18)
-                        ),
-                        position: 'absolute',
-                        top: 12
-                      }}
-                    >
-                      邀好友得狸电池
-                    </Text>
-                  </View>
-                ) : null}
-              </TouchableOpacity>
-            </>
-          ) : null}
-          {isMine ? (
-            <View
-              style={{
-                flexDirection: 'row',
-                alignItems: 'center',
-                flex: 1,
-                justifyContent: 'flex-end'
-              }}
-            >
-              <TouchableOpacity
-                activeOpacity={0.3}
-                style={[
-                  $creditBtn,
-                  {
-                    backgroundColor: '#00000099', // 暗色模式无法覆盖
-                    marginRight: isIos ? 16 : 4
-                  }
-                ]}
-                onPress={() => {
-                  router.push('/profile/edit');
-                  setTimeout(() => {
-                    reportClick('edit_profile');
-                  });
-                }}
-              >
-                <Text
-                  style={[
-                    $creditBtnText,
-                    {
-                      color: darkTheme.text.primary
-                    }
-                  ]}
-                >
-                  编辑资料
-                </Text>
-              </TouchableOpacity>
-
-              <TouchableOpacity
-                style={{
-                  width: 24,
-                  height: 24,
-                  alignItems: 'center',
-                  justifyContent: 'center'
-                }}
-                onPress={() => {
-                  reportClick('button', {
-                    user_button: 0,
-                    identity_status: isMine ? '0' : '1'
-                  });
-                  router.push('/setting/');
-                }}
-              >
-                <Icon
-                  color={darkTheme.text.solid}
-                  icon={skinConfig ? 'setting_pw' : 'setting'}
-                  size={24}
-                />
-              </TouchableOpacity>
-            </View>
-          ) : null}
-          {profile ? (
-            <>
-              <View />
-              <Follow
-                followed={!!stat?.followed}
-                beingFollowed={!!stat?.beingFollowed}
-                uid={profile?.uid}
-                onUnfollow={() => onUpdatefollow(false)}
-                onFollow={() => onUpdatefollow(true)}
-              />
-            </>
-          ) : null}
-        </View>
-      </View>
-    );
-  }
-);
-export default InfoProfile;
-
-const st = StyleSheet.create({
-  lotteryEntryVideo: {
-    width: 34,
-    height: 34
-  },
-  lotteryEntry: {
-    width: 34,
-    height: 34,
-    marginTop: -9,
-    marginLeft: -12
-  }
-});
-
-const $creditLottie: ViewStyle = {
-  width: 70,
-  height: 70,
-  position: 'absolute',
-  pointerEvents: 'none',
-  right: 0,
-  bottom: 8,
-  zIndex: $Z_INDEXES.zm1,
-  transform: [
-    {
-      translateX: dp2px(18)
-    },
-    {
-      translateY: dp2px(23)
-    }
-  ]
-};
-
-const $creditBtn: ViewStyle = {
-  width: 72,
-  height: 26,
-  borderRadius: 100,
-  borderColor: 'rgba(0, 0, 0, 0.12)',
-  borderWidth: 0.5,
-  // paddingHorizontal: 12,
-  // paddingVertical: 4,
-  marginRight: 16,
-  alignItems: 'center',
-  justifyContent: 'center',
-  top: -1
-};
-
-const $creditBtnText: TextStyle = {
-  ...$USE_FONT(
-    StyleSheet.currentColors.titleGray,
-    typography.fonts.pingfangSC.normal,
-    dp2px(12),
-    'normal',
-    '600',
-    dp2px(18)
-  )
-};
-
-const $dailyTip: ImageStyle = {
-  position: 'absolute',
-  width: 120,
-  height: 36,
-  bottom: -48,
-  left: -41,
-  borderRadius: 8,
-  ...$flexCenter
-};



## File: 'src/bizComponents/userScreen/infoSection/index.tsx'

@@ -1,552 +1,0 @@ 
-import { useMemoizedFn } from 'ahooks';
-import { router } from 'expo-router';
-import AnimatedLottieView from 'lottie-react-native';
-import { useEffect, useMemo, useRef, useState } from 'react';
-import {
-  ImageStyle,
-  Pressable,
-  Text,
-  TextStyle,
-  TouchableOpacity,
-  View,
-  ViewStyle
-} from 'react-native';
-import { ShadowedView } from 'react-native-fast-shadow';
-import Animated, {
-  AnimatedStyle,
-  AnimatedStyleProp,
-  SharedValue,
-  useAnimatedStyle,
-  useSharedValue,
-  withSpring
-} from 'react-native-reanimated';
-import { useSafeAreaInsets } from 'react-native-safe-area-context';
-import { Icon } from '@/src/components';
-import { Avatar } from '@/src/components/avatar';
-import { SkinnedImage } from '@/src/components/skin/SkinnedImage';
-import { CONFIG_KYES } from '@/src/components/skin/getSkinConfig';
-import { useScreenSize } from '@/src/hooks';
-import { useBehaviorStore } from '@/src/store/behavior';
-import { SwitchName, useControlStore } from '@/src/store/control';
-import { useEditPendantStore } from '@/src/store/edit-pendant';
-import { centerStyle, darkTheme, rowStyle, typography } from '@/src/theme';
-import { Theme } from '@/src/theme/colors/type';
-import { $USE_FONT, $Z_INDEXES, $flexHCenter } from '@/src/theme/variable';
-import { UserSocialStat } from '@/src/types';
-import { dp2px, isIos } from '@/src/utils';
-import { formatTosUrl } from '@/src/utils/getTosUrl';
-import { stirngRemoveEnter } from '@/src/utils/opt/replace';
-import { formatUserNumber } from '@/src/utils/opt/transNum';
-import { reportClick } from '@/src/utils/report';
-import { Image } from '@Components/image';
-import { showImageConfirm } from '@Components/popup/confirmModalGlobal/Confirm';
-import { StyleSheet } from '@Utils/StyleSheet';
-import INFO_MASK from '@Assets/image/goods-shef/info-mask.png';
-import INTRO_CUSTOM_PULL from '@Assets/lottie/goods/intro_custom_pull.json';
-import INTRO_PULL from '@Assets/lottie/goods/intro_pull.json';
-import { userPerformanceCollector } from '../../../utils/report/userPageCollector';
-import { UserProfile } from '@/proto-registry/src/web/raccoon/uinfo/uinfo_pb';
-import { useIsFocused } from '@react-navigation/native';
-import { useShallow } from 'zustand/react/shallow';
-
-const LIKE_COVER = require('@Assets/user/userLike.png');
-const PENDANT_TIP_BG = require('@Assets/user/pendant-tip-bg.png');
-
-export function InfoSection({
-  currentUser,
-  stat,
-  isMine,
-  showFeedBack,
-  isRootPage,
-  animateStyle,
-  isAllowed = false,
-  isSlideToTop,
-  goodsLikes,
-  hasGoods,
-  checkEnterGoods
-}: {
-  currentUser?: UserProfile;
-  stat?: UserSocialStat;
-  isMine?: boolean;
-  showFeedBack?: () => void;
-  isRootPage?: boolean;
-  animateStyle: ViewStyle;
-  isAllowed: boolean;
-  isSlideToTop: boolean;
-  goodsLikes?: number;
-  hasGoods?: boolean;
-  checkEnterGoods: () => void;
-}) {
-  const { newPendant, fetchNewPendant } = useEditPendantStore(
-    useShallow(state => ({
-      newPendant: state.newPendant,
-      fetchNewPendant: state.fetchNewPendant
-    }))
-  );
-  const isFocus = useIsFocused();
-
-  useEffect(() => {
-    if (isFocus && isMine) {
-      fetchNewPendant();
-    }
-  }, [isFocus, isMine]);
-
-  const currentUserName = stirngRemoveEnter(currentUser?.name);
-
-  const handleEditAvatar = useMemoizedFn(() => {
-    reportClick('button', {
-      user_button: 2,
-      identity_status: isMine ? '0' : '1'
-    });
-    if (isMine) {
-      router.push('/avatar-edit/');
-    } else {
-      if (
-        useControlStore.getState().checkIsOpen(SwitchName.ENABLE_USER_REPORT)
-      ) {
-        showFeedBack?.();
-      }
-    }
-  });
-
-  const onPressFollowAndFans = useMemoizedFn((defaultTab: string) => {
-    reportClick('button', {
-      user_button: defaultTab === 'follow' ? 4 : 5,
-      identity_status: isMine ? '0' : '1'
-    });
-    router.push({
-      pathname: '/follow-fan/',
-      params: {
-        defaultTab,
-        uid: currentUser?.uid || ''
-      }
-    });
-  });
-
-  const showLikeModal = useMemoizedFn(() => {
-    reportClick('button', {
-      user_button: 6,
-      identity_status: isMine ? '0' : '1'
-    });
-    showImageConfirm({
-      image: LIKE_COVER,
-      title: currentUser?.name || '',
-      content: `共获得${stat?.beingLikeds}个赞`,
-      confirmText: '知道了',
-      cancelText: '#hiddenCancel#', // 暗色不影响其他 confirm
-      onConfirm: ({ close }) => {
-        close();
-      },
-      theme: Theme.DARK
-    });
-  });
-
-  const showSameModal = useMemoizedFn(() => {
-    reportClick('button', {
-      user_button: 7,
-      identity_status: isMine ? '0' : '1'
-    });
-
-    router.push({
-      pathname: `/fame/${currentUser?.uid}`,
-      params: {
-        identity_status: isMine ? 0 : 1
-      }
-    });
-  });
-
-  console.log('[infoSection] currentUser', currentUser);
-
-  const HasBeLiked = useMemo(() => goodsLikes, [goodsLikes]);
-
-  const safeTop = useSafeAreaInsets().top;
-
-  return (
-    <View style={{ alignItems: 'center' }}>
-      <View
-        style={{
-          position: 'absolute',
-          width: '100%',
-          height: '100%',
-          pointerEvents: 'none',
-          zIndex: -1,
-          transform: [
-            {
-              translateY: 58
-            }
-          ]
-        }}
-      >
-        <Image
-          source={INFO_MASK}
-          contentFit="cover"
-          style={{ flex: 1, zIndex: -1 }}
-        />
-      </View>
-
-      {HasBeLiked && !isAllowed ? (
-        <Animated.View
-          style={[
-            animateStyle,
-            {
-              position: 'absolute',
-              height: 26,
-              top: 0,
-              transform: [
-                {
-                  translateY: safeTop < 44 ? 0 : -12
-                },
-                {
-                  translateX: -6
-                }
-              ],
-              zIndex: 100
-            }
-          ]}
-        >
-          <ShadowedView>
-            <View style={$flexHCenter}>
-              <Icon
-                icon="self_goods_like"
-                size={14}
-                style={{
-                  marginRight: 4
-                }}
-              />
-              <Text
-                style={{
-                  ...$USE_FONT(
-                    darkTheme.text.primary,
-                    typography.fonts.pingfangSC.normal,
-                    12,
-                    'normal',
-                    isIos ? '600' : 'bold',
-                    undefined
-                  ),
-                  marginRight: 4
-                }}
-              >
-                {goodsLikes}人
-              </Text>
-              <Text
-                style={{
-                  ...$USE_FONT(
-                    darkTheme.text.primary,
-                    typography.fonts.pingfangSC.normal,
-                    12,
-                    'normal',
-                    isIos ? '600' : 'bold',
-                    undefined
-                  )
-                }}
-              >
-                {isMine ? '赞了你的痛墙' : '赞了TA的痛墙'}
-              </Text>
-            </View>
-          </ShadowedView>
-        </Animated.View>
-      ) : null}
-
-      <Animated.View style={[$userInfo]}>
-        {isAllowed && (isMine || (!isMine && hasGoods)) ? (
-          <Animated.View
-            style={[
-              animateStyle,
-              {
-                position: 'absolute',
-                height: 26,
-                transform: [
-                  {
-                    translateY: -126
-                  },
-                  {
-                    translateX: -6
-                  }
-                ],
-                zIndex: 200
-              }
-            ]}
-          >
-            <Pressable
-              onPress={() => {
-                checkEnterGoods();
-              }}
-            >
-              <ShadowedView>
-                <View style={$flexHCenter}>
-                  {isMine ? (
-                    <AnimatedLottieView
-                      source={INTRO_PULL}
-                      loop
-                      autoPlay
-                      style={{
-                        width: 111,
-                        height: 21,
-                        transform: [
-                          {
-                            translateX: 4
-                          },
-                          {
-                            translateY: -1
-                          },
-                          {
-                            scale: 1
-                          }
-                        ]
-                      }}
-                    />
-                  ) : (
-                    <AnimatedLottieView
-                      source={INTRO_CUSTOM_PULL}
-                      loop
-                      autoPlay
-                      style={{
-                        width: 139,
-                        height: 21,
-                        transform: [
-                          {
-                            translateX: 6
-                          },
-                          {
-                            translateY: -1
-                          },
-                          {
-                            scale: 1
-                          }
-                        ]
-                      }}
-                    />
-                  )}
-                </View>
-              </ShadowedView>
-            </Pressable>
-          </Animated.View>
-        ) : null}
-
-        <Animated.View style={[$avatar]}>
-          <Avatar
-            profile={currentUser}
-            size={100}
-            showTag={true}
-            showPendant={true}
-            onPress={handleEditAvatar}
-            borderWidth={2}
-            borderColor={'#262629'}
-            onLoad={() => {
-              if (currentUser?.uid) {
-                userPerformanceCollector.markPerformanceTimestamp(
-                  'user_info_available_timestamp',
-                  currentUser?.uid
-                );
-              }
-            }}
-          />
-        </Animated.View>
-        {newPendant ? (
-          <Animated.View style={[$pendantTipContainer]}>
-            <Image style={[$pendantTip]} source={PENDANT_TIP_BG} />
-            <View
-              style={[
-                $pendantTip,
-                rowStyle,
-                { justifyContent: 'center', gap: 2, top: -52, paddingLeft: 6 }
-              ]}
-            >
-              <Image
-                source={newPendant?.pendantUrl || ''}
-                style={$pendantImage}
-              />
-              <Text style={[$newPendantStyle]}>{'新挂件'}</Text>
-            </View>
-            <View style={[$dotTip]} />
-          </Animated.View>
-        ) : null}
-        <Animated.View style={[$nicknameConainer]}>
-          <Animated.Text
-            style={[
-              $nickname as AnimatedStyleProp<TextStyle>,
-              {
-                width: 300,
-                color:
-                  isMine && currentUser?.isNameUpdated === false
-                    ? darkTheme.text.disabled
-                    : darkTheme.text.primary,
-                textAlign: 'center'
-              }
-            ]}
-            ellipsizeMode="tail"
-            numberOfLines={1}
-            allowFontScaling={false}
-          >
-            {currentUserName}
-          </Animated.Text>
-        </Animated.View>
-      </Animated.View>
-
-      <Animated.View style={[$numsWrap]} pointerEvents="box-none">
-        <Pressable onPress={() => onPressFollowAndFans('follow')}>
-          <View style={$numItem}>
-            <Text style={$num}>
-              {formatUserNumber(stat?.followings?.toLocaleString() || 0)}
-            </Text>
-            <Text style={$label}>关注</Text>
-          </View>
-        </Pressable>
-        <Pressable onPress={() => onPressFollowAndFans('fans')}>
-          <View style={$numItem}>
-            <Text style={$num}>
-              {formatUserNumber(stat?.fans?.toLocaleString() || 0)}
-            </Text>
-            <Text style={$label}>粉丝</Text>
-          </View>
-        </Pressable>
-        <Pressable onPress={showLikeModal} style={$numItem}>
-          <Text style={$num}>
-            {formatUserNumber(stat?.beingLikeds?.toLocaleString() || 0)}
-          </Text>
-          <Text style={$label}>获赞</Text>
-        </Pressable>
-        <Pressable onPress={showSameModal} style={$numItem}>
-          <Text style={$num}>
-            {formatUserNumber(stat?.beingCopieds?.toLocaleString() || 0)}
-          </Text>
-          <Text style={$label}>声望</Text>
-        </Pressable>
-      </Animated.View>
-    </View>
-  );
-}
-
-const $userInfo: ViewStyle = {
-  display: 'flex',
-  flexDirection: 'column',
-  justifyContent: 'center',
-  alignItems: 'center',
-  marginTop: 58,
-  zIndex: $Z_INDEXES.z200
-};
-
-const $avatar: ViewStyle = {
-  width: 100,
-  height: 100,
-  position: 'relative'
-};
-
-const $pendantTipContainer: ViewStyle = {
-  position: 'absolute',
-  width: 0,
-  height: 0
-};
-
-const $pendantTip: ImageStyle = {
-  width: 91,
-  height: 45,
-  left: 22,
-  top: -48,
-  position: 'absolute'
-};
-
-const $dotTip: ViewStyle = {
-  width: 8,
-  height: 8,
-  backgroundColor: '#fff',
-  borderRadius: 4,
-  left: 26,
-  top: -18,
-  position: 'absolute'
-};
-
-const $pendantImage: ImageStyle = {
-  width: 20,
-  height: 20
-};
-
-const $newPendantStyle: TextStyle = {
-  fontSize: 11,
-  fontWeight: '600'
-};
-
-const $avatarTag: ImageStyle = {
-  width: 24,
-  height: 24,
-  right: 6,
-  position: 'absolute',
-  bottom: 0
-};
-
-const $avatarInner: ViewStyle = {
-  width: 104,
-  height: 104,
-  top: -2,
-  left: -2,
-  position: 'absolute',
-  borderColor: 'rgba(255, 255, 255, 0.6)',
-  borderWidth: 2,
-  borderRadius: 52,
-  zIndex: $Z_INDEXES.z0
-};
-
-const $nicknameConainer: ViewStyle = {
-  marginTop: 12,
-  flexDirection: 'row',
-  alignItems: 'center',
-  justifyContent: 'center',
-  maxWidth: '60%',
-  transformOrigin: 'top'
-  // backgroundColor: 'pink'
-};
-
-const $nickname: TextStyle = {
-  // overflow: 'hidden',
-  // flexShrink: 0,
-  color: '#000',
-  position: 'relative',
-  fontFamily: typography.fonts.baba.heavy,
-  fontSize: 20,
-  lineHeight: 28
-};
-
-const $nicknameEdit: ViewStyle = {
-  flex: 0,
-  marginLeft: 4
-};
-
-const $numsWrap: ViewStyle = {
-  ...StyleSheet.rowStyle,
-  width: '100%',
-  paddingHorizontal: 16,
-  marginTop: 16,
-  marginBottom: 17,
-  justifyContent: 'space-around'
-};
-
-const $numItem: ViewStyle = {
-  display: 'flex',
-  alignItems: 'center',
-  flexDirection: 'column',
-  minWidth: 32,
-  minHeight: 44
-  //   flexBasis: 100
-};
-
-const $num: TextStyle = {
-  fontSize: 17,
-  lineHeight: 24,
-  textAlign: 'center',
-  fontFamily: typography.fonts.baba.heavy,
-  color: darkTheme.text.primary
-};
-
-const $label: TextStyle = {
-  color: darkTheme.text.tertiary,
-  fontSize: 12,
-  lineHeight: 17,
-  fontWeight: '500'
-};
-
-const $editIconPress: ViewStyle = {
-  width: 20,
-  height: 20,
-  position: 'absolute',
-  ...centerStyle
-};



## File: 'src/bizComponents/userScreen/retentionPopup/index.tsx'

@@ -1,7 +1,7 @@ 
 import React, { useCallback } from 'react';
 import { useEffect } from 'react';
-import { Pressable, Text, View } from 'react-native';
+import { Pressable, View } from 'react-native';
 import { queryClient } from '@/src/api/query';
 import { Follow } from '@/src/components/follow';
 import { Image } from '@/src/components/image';
 import { showModal } from '@/src/components/popup/ModalGlobal';


@@ -10,8 +10,9 @@ 
 import { darkTheme, typography } from '@/src/theme';
 import { StyleSheet, dp2px } from '@/src/utils';
 import { followGuide } from '@/src/utils/FollowGuideManager';
 import { reportClick } from '@/src/utils/report';
+import { Text } from '@Components/text';
 import { FirstTriggerType } from '@/proto-registry/src/web/raccoon/common/types_pb';
 import { useShallow } from 'zustand/react/shallow';
 import { AvatarBackgroundHalo, AvatarForegroundHalo } from './AvatarHalo';



## File: 'src/bizComponents/userScreen/constants.ts'

@@ -0,0 +1,47 @@
+import { TabItem } from './components/UserPanel';
+
+export enum UserPageTab {
+  WORKS = 'works',
+  SECRET = 'live_photo',
+  LIKE = 'like',
+  MY_ROLE = 'my_role'
+}
+
+export const authorTabs: TabItem[] = [
+  {
+    key: UserPageTab.WORKS,
+    title: '作品',
+    width: 48
+  },
+  {
+    key: UserPageTab.SECRET,
+    title: '待发布',
+    width: 63
+  },
+  {
+    key: UserPageTab.LIKE,
+    title: '赞过',
+    width: 48
+  },
+  {
+    key: UserPageTab.MY_ROLE,
+    title: '我的角色',
+    width: 78
+  }
+];
+
+export const visitorTabs = authorTabs.filter(
+  tab => tab.key !== UserPageTab.SECRET && tab.key !== UserPageTab.MY_ROLE
+);
+
+export const authorPageTabMap = {
+  [UserPageTab.WORKS]: 0,
+  [UserPageTab.SECRET]: 1,
+  [UserPageTab.LIKE]: 2,
+  [UserPageTab.MY_ROLE]: 3
+};
+
+export const visitorPageTabMap = {
+  [UserPageTab.WORKS]: 0,
+  [UserPageTab.LIKE]: 1
+};



## File: 'src/bizComponents/userScreen/index.tsx'

--- 
+++ 


@@ -1,41 +1,36 @@ 
 import { useDebounceFn, useMemoizedFn } from 'ahooks';
 import { router, useFocusEffect, useLocalSearchParams } from 'expo-router';
 import React, {
   memo,
   useCallback,
   useEffect,
   useLayoutEffect,
   useMemo,
   useRef,
   useState
 } from 'react';
-import { InteractionManager, Pressable, View } from 'react-native';
-import { GestureDetector } from 'react-native-gesture-handler';
+import { InteractionManager, StyleProp, TextStyle, View } from 'react-native';
+import { Gesture, GestureDetector } from 'react-native-gesture-handler';
 import PagerView from 'react-native-pager-view';
 import Animated, {
-  cancelAnimation,
   runOnJS,
   useAnimatedStyle,
   useSharedValue,
   withDelay,
   withRepeat,
-  withSpring,
-  withTiming
+  withSpring
 } from 'react-native-reanimated';
 import { useSafeAreaInsets } from 'react-native-safe-area-context';
 import { getGoodsList, visitHomePage } from '@/src/api/goods';
-import ProfileGuideModal from '@/src/bizComponents/profile/GuideModal';
-import { FullScreen, Icon, Text } from '@/src/components';
-import { EmptyPlaceHolder } from '@/src/components/Empty';
+import { usePullDownGestureHandlersFactory } from '@[BASE64_DATA:68chars]';
+import { GoodsWallBg } from '@/src/bizComponents/userScreen/components/GoodsWallBg';
+import { Screen } from '@/src/components';
 import { AlbumSheet } from '@/src/components/album';
 import { AlbumFromType } from '@/src/components/album/const';
 import { PagePerformance } from '@/src/components/common/pagePerformance';
-import { showLogin } from '@/src/components/login';
-import { Tabs } from '@/src/components/tabs';
 import { LOGIN_SCENE } from '@/src/constants';
 import { useAuthState, usePersistFn, useScreenSize } from '@/src/hooks';
-import { useRetentionPopup } from '@/src/hooks/useRetentionPopup';
 import { useSafeBottomArea } from '@/src/hooks/useSafeAreaInsetsStyle';
 import { useAuthStore } from '@/src/store/authInfo';
 import { useGoodsShefStore } from '@/src/store/goods_shef';
 import { usePerformanceStore } from '@/src/store/performance';


@@ -38,19 +33,19 @@ 
 import { useSafeBottomArea } from '@/src/hooks/useSafeAreaInsetsStyle';
 import { useAuthStore } from '@/src/store/authInfo';
 import { useGoodsShefStore } from '@/src/store/goods_shef';
 import { usePerformanceStore } from '@/src/store/performance';
 import { usePublishStore } from '@/src/store/publish';
 import { useStorageStore } from '@/src/store/storage';
 import { useUserInfoStore } from '@/src/store/userInfo';
-import { darkTheme } from '@/src/theme';
+import { darkTheme, typography } from '@/src/theme';
 import {
   FirstTriggerType,
   Pagination,
   TabItemType,
   UserSocialStat
 } from '@/src/types';
-import { dp2px } from '@/src/utils';
+import { dp2px, isIos } from '@/src/utils';
 import { CommonEventBus } from '@/src/utils/event';
 import { safeParseJson } from '@/src/utils/opt/safeParseJson';
 import { reportClick, reportExpo } from '@/src/utils/report';
 import { userPerformanceCollector } from '@/src/utils/report/userPageCollector';


@@ -53,75 +48,38 @@ 
 import { CommonEventBus } from '@/src/utils/event';
 import { safeParseJson } from '@/src/utils/opt/safeParseJson';
 import { reportClick, reportExpo } from '@/src/utils/report';
 import { userPerformanceCollector } from '@/src/utils/report/userPageCollector';
 import { Image } from '@Components/image';
 import { MaskArea } from '@Components/maskArea';
 import { StyleSheet } from '@Utils/StyleSheet';
-import CUSTOMER_EMPTY from '@Assets/image/goods-shef/customer-empty.png';
-import GOODS_MASK from '@Assets/image/goods-shef/goods-mask.png';
-import DEFAULT_MASK_BG from '@Assets/image/goods-shef/goods_bg.png';
-import MAIN_EMPTY from '@Assets/image/goods-shef/main-bg.png';
 import { GoodsHomeState } from '../goodsHome/types';
+import { NestedScrollView } from '../nestedScrollView';
+import ProfileGuideModal from '../profile/GuideModal';
+import { GoodsButton } from './components/GoodsButton';
+import { ANIMATION_PARAMS, GoodsTopTip } from './components/GoodsTopTip';
+import { UserHeader } from './components/UserHeader';
+import { UserPanel } from './components/UserPanel';
 import { LikesFlowList } from './components/likeFlowList';
+import { MyRoleFlowList } from './components/myRoleList';
 import { SecretsFlowList } from './components/secretFlowList';
 import { WorksFlowList } from './components/workFlowList';
+import { useHandleSwipeBack } from './hooks/useHandleSwipeBack';
+import { useRetentionPopup } from './hooks/useRetentionPopup';
 import { GetPlaceRsp } from '@/proto-registry/src/web/raccoon/goods/goods_pb';
 import { UserProfile } from '@/proto-registry/src/web/raccoon/uinfo/uinfo_pb';
 import { PartialMessage } from '@bufbuild/protobuf';
 import { useIsFocused } from '@react-navigation/native';
 import { useShallow } from 'zustand/react/shallow';
+import {
+  UserPageTab,
+  authorPageTabMap,
+  authorTabs,
+  visitorPageTabMap,
+  visitorTabs
+} from './constants';
 import { FeedbackSheet } from './feedback';
-import InfoProfile from './infoProfile';
-import { InfoSection } from './infoSection';
-import { PageTab } from './types';
-import { useAnimation } from './useAnimation';
-
-export { PageTab } from './types';
-
-export enum PageParticleTab {
-  LIVE_PHOTO = 'live_photo',
-  MAGIC_VIDEO = 'magic_video'
-}
-
-const authorTabs = [
-  {
-    key: PageTab.WORKS,
-    title: '作品',
-    label: '作品'
-  },
-  {
-    key: PageTab.SECRET,
-    title: '待发布',
-    label: '待发布'
-  },
-  {
-    key: PageTab.LIKE,
-    title: '赞过',
-    label: '赞过'
-  },
-  {
-    key: PageTab.ALBUM,
-    label: '图集',
-    title: '图集'
-  }
-];
-
-const visitorTabs = authorTabs.filter(
-  tab => tab.key !== PageTab.SECRET && tab.key !== PageTab.ALBUM
-);
-
-const authorPageTabMap = {
-  [PageTab.WORKS]: 0,
-  [PageTab.SECRET]: 1,
-  [PageTab.LIKE]: 2,
-  [PageTab.ALBUM]: 3
-};
-
-const visitorPageTabMap = {
-  [PageTab.WORKS]: 0,
-  [PageTab.LIKE]: 1
-};
+import { fetchSecretCount } from './utils';
 
 function User({
   isRootPage = false,
   pageTab: queryPageTab,


@@ -129,18 +87,18 @@ 
   timestamp: queryTimestamp,
   id: propId,
   fromSwipePreview,
   preloadProfile
 }: {
   isRootPage?: boolean;
   pageTab?: string;
   refresh?: string;
   timestamp?: string;
   id?: string;
   fromSwipePreview?: boolean;
   preloadProfile?: PartialMessage<UserProfile>;
 }) {
-  const [currentTab, setCurrentTab] = useState(PageTab.WORKS);
+  const [currentTab, setCurrentTab] = useState<UserPageTab>(UserPageTab.WORKS);
   const pagerRef = useRef<PagerView>(null);
   const [pagerViewReady, setPagerViewReady] = useState<boolean>(false);
   const { width: screenWidth, height: screenHeight } = useScreenSize('window');
   const [tabEntry, setTabEntry] = useState(false);


@@ -202,32 +160,88 @@ 
     }
   }, [id, preloadProfile]);
 
   const isMine =
     (isRootPage === true && !params.id) || profile?.uid === user?.uid;
 
   const currentUser = useMemo(() => {
     if (isMine) {
       return user;
     }
     return profile;
   }, [isMine, user, profile]);
 
-  const shouldShowProfileGuide =
-    userId &&
-    isMine &&
-    (!queryPageTab || tabEntry) &&
-    !user?.gender &&
-    !user?.birthday;
-
   const pageTabMap = useMemo(
     () => (isMine ? authorPageTabMap : visitorPageTabMap),
     [isMine]
   );
-  const pageTabConfig = useMemo(
-    () => (isMine ? authorTabs : visitorTabs),
-    [isMine]
-  );
+  const [secretsCount, setSecretsCount] = useState<number | null>(null);
+
+  const countStr = useMemo(() => {
+    if (secretsCount) {
+      return secretsCount > 99 ? '99+' : secretsCount.toString();
+    }
+    return '';
+  }, [secretsCount]);
+
+  const countTextStyle: TextStyle = useMemo(() => {
+    return {
+      width: countStr.length * 8.5,
+      lineHeight: 15,
+      fontFamily: typography.fonts.Barlow.SemiBold,
+      textOverflow: 'clip',
+      fontWeight: '600'
+    };
+  }, [countStr.length]);
+
+  // 为待发布标签创建一个可以立即反映数量变化的渲染函数
+  const renderSecretTabContent = useMemoizedFn(
+    ({
+      textStyle
+    }: {
+      isActive: boolean;
+      textStyle: StyleProp<TextStyle>[];
+    }) => {
+      return (
+        <>
+          <Animated.Text
+            style={[...textStyle, { width: 46 }]}
+            numberOfLines={1}
+          >
+            待发布
+          </Animated.Text>
+          <Animated.Text
+            style={[...textStyle, countTextStyle]}
+            numberOfLines={1}
+          >
+            {countStr}
+          </Animated.Text>
+        </>
+      );
+    }
+  );
+  const pageTabConfig = useMemo(() => {
+    // 修改作者标签配置，为待发布标签添加数量
+    if (isMine) {
+      const updatedConfig = [...authorTabs];
+      // 找到待发布标签并添加数量
+      const secretsTabIndex = updatedConfig.findIndex(
+        tab => tab.key === UserPageTab.SECRET
+      );
+      if (!secretsCount || secretsTabIndex === -1) {
+        return updatedConfig;
+      }
+
+      updatedConfig[secretsTabIndex] = {
+        ...updatedConfig[secretsTabIndex],
+        renderItem: renderSecretTabContent,
+        width: 63 + 8.5 * countStr.length
+      };
+
+      return updatedConfig;
+    }
+    return visitorTabs;
+  }, [isMine, secretsCount, renderSecretTabContent, countStr.length]);
 
   const [profileGuideModalVisible, setProfileGuideModalVisible] = useState<
     boolean | null
   >(null);


@@ -238,17 +252,24 @@ 
       setTabEntry(info.tab === TabItemType.PROFILE);
       if (info.tab === TabItemType.PROFILE) {
         onRefreshData();
       }
     };
 
     CommonEventBus.on('tabBarPressed', handler);
     CommonEventBus.on('tabBarPressedWhenFocus', handler);
     return () => {
       CommonEventBus.off('tabBarPressed', handler);
       CommonEventBus.off('tabBarPressedWhenFocus', handler);
     };
   }, []);
+
+  const shouldShowProfileGuide =
+    userId &&
+    isMine &&
+    (!queryPageTab || tabEntry) &&
+    !user?.gender &&
+    !user?.birthday;
 
   /** 资料引导 */
   useEffect(() => {
     InteractionManager.runAfterInteractions(() => {


@@ -260,70 +281,51 @@ 
       const hasShown = profileGuideRecord[userId];
       if (!hasShown) {
         setProfileGuideModalVisible(true);
         useStorageStore
           .getState()
           .updateJsonRecord('profileGuideRecord', record => ({
             ...record,
             [userId]: true
           }));
       }
     });
   }, [shouldShowProfileGuide, userId, isFocused]);
 
-  const topHeight = useMemo(() => {
-    return dp2px(274) + safeTop * 2;
-  }, [safeTop]);
-  const translateYMax = topHeight - safeTop - (safeTop < 44 ? 0 : 44);
-  const [creditOpacity, setCreditOpacity] = useState(1);
-
-  /** 当用户上滑到边界区域，并立刻下拉时 */
-  const [returnTop, setReturnTop] = useState(true);
-  const updateUnlockTop = (status: boolean) => {
-    setReturnTop(status);
-    if (!status) {
-      $tabStickyOpacity.value = withTiming(1);
-      $goodsShareY.value = withTiming(-translateYMax);
-    }
-  };
-
-  /** 统一设置回到头部 重置 */
-  const resetTopBehavior = () => {
-    $goodsShareY.value = withTiming(0);
-    $tabStickyOpacity.value = withTiming(0);
-  };
-
-  useEffect(() => {
-    if (returnTop) {
-      resetTopBehavior();
-    }
-  }, [returnTop]);
-
   /** profile 的一些引导 */
   const [isFeedbackShow, setIsFeedbackShow] = useState(false);
   const [isSeqToastShow, setIsSeqToastShow] = useState(false);
 
   /** 谷子部分 */
-  const [goodsLikes, setGoodsLikes] = useState(0);
-  const DEFAULT_EMPTY = isMine ? MAIN_EMPTY : CUSTOMER_EMPTY;
-  const [goodsBgWall, setGoodsBgWall] = useState('');
   const [hasGoods, setHasGoods] = useState(false);
-
-  useEffect(() => {
-    setGoodsBgWall(goodsWallRes?.backgroundImage?.url || DEFAULT_EMPTY);
-    setGoodsLikes(goodsWallRes?.likes || 0);
-  }, [goodsWallRes]);
-
-  const checkEnterGoods = useCallback(() => {
+  const [isMaskLoaded, setIsMaskLoaded] = useState(false);
+
+  // 监听GOODS_MASK加载完成事件
+  useEffect(() => {
+    const handleMaskLoaded = (data: { loaded: boolean }) => {
+      // 使用InteractionManager确保其他UI渲染完成后再显示背景
+      if (data.loaded) {
+        InteractionManager.runAfterInteractions(() => {
+          setIsMaskLoaded(true);
+        });
+      }
+    };
+
+    CommonEventBus.on('goodsMaskLoaded', handleMaskLoaded);
+
+    return () => {
+      CommonEventBus.off('goodsMaskLoaded', handleMaskLoaded);
+    };
+  }, []);
+
+  const checkEnterGoods = useMemoizedFn(() => {
     loginIntercept(
       () => {
         enterGoods();
       },
       { scene: LOGIN_SCENE.GOODS }
     );
-  }, []);
-
-  const maxScrollLimit = 215 - safeTop;
+  });
 
   // tab 触发刷新
   const [isRefreshData, setIsRefreshData] = useState<[] | undefined>(undefined);
   const { run: onRefreshData } = useDebounceFn(


@@ -330,34 +332,12 @@ 
     () => {
       setIsRefreshData([]);
     },
     {
       wait: 500,
       leading: true
     }
   );
-
-  const {
-    holdGesture,
-    $goodsShareY,
-    nativeGesture,
-    refreshStyle,
-    enterGoodsStyle,
-    refreshWrapStyle,
-    worksScrollY,
-    livePhotoSY,
-    likeScrollY
-  } = useAnimation({
-    currentTab,
-    maxScrollLimit,
-    checkEnterGoods,
-    onRefreshData,
-    setReturnTop
-  });
-
-  const $goodsTransBgStyle = useAnimatedStyle(() => ({
-    transform: [{ translateY: $goodsShareY.value as number }]
-  }));
 
   const GOODS_SHEF_TRIGGER = isMine
     ? FirstTriggerType.ENTER_OWN_FANDOM_WALL
     : FirstTriggerType.ENTER_OTHER_FANDOM_WALL;


@@ -372,18 +352,18 @@ 
   const enterGoods = usePersistFn(() => {
     if (isMine) {
       if (hasGoods) {
         router.push({
           pathname:
             `/goods/home?state=${GoodsHomeState.Me}` as RelativePathString
         });
       } else {
         router.push({
           pathname: '/goods/'
         });
       }
     } else {
-      if (hasGoods && profile?.uid) {
+      if (profile?.uid) {
         router.push({
           pathname:
             `/goods/home?state=${GoodsHomeState.Guest}&uid=${profile?.uid}` as RelativePathString
         });


@@ -387,17 +367,18 @@ 
           pathname:
             `/goods/home?state=${GoodsHomeState.Guest}&uid=${profile?.uid}` as RelativePathString
         });
       }
     }
   });
 
   const syncGoodsShefToast = useCallback(async () => {
     const res = await checkIsAllowed(GOODS_SHEF_TRIGGER);
     console.log(res, 'isallowed ====', GOODS_SHEF_TRIGGER);
     setIsSeqToastShow(res);
   }, []);
 
+  // FIXME(fuxiao): 需要再次降低优先级
   const syncGoodsList = useCallback(async (id: string) => {
     const res = await getGoodsList({
       pagination: {
         cursor: '',


@@ -415,18 +396,20 @@ 
       syncUserInfo(id);
 
       // 其他请求延迟执行（次高优先级和低优先级）
       InteractionManager.runAfterInteractions(() => {
         // 获取谷子列表（次高优先级）
         syncGoodsList(id);
 
         // 访问量
         if (!isMine && isFocused && profile?.uid) {
           visitHomePage({ targetUid: profile?.uid });
         }
 
         // 引导提示（低优先级）
-        ((hasGoods && !isMine) || isMine) && syncGoodsShefToast();
+        if ((hasGoods && !isMine) || isMine) {
+          syncGoodsShefToast();
+        }
       });
     }
   }, [
     id,


@@ -429,19 +412,34 @@ 
       });
     }
   }, [
     id,
     isFocused,
     isMine,
     profile?.uid,
     syncGoodsList,
     syncGoodsShefToast,
     syncUserInfo,
     hasGoods
   ]);
 
-  useEffect(() => {
-    isFocused && reportExpo('invite_icon', {}, true);
+  // 添加获取待发布数量的逻辑，确保在其他接口完成后再执行
+  useEffect(() => {
+    if (id && isMine && isFocused && goodsWallRes && secretsCount === null) {
+      // 先执行高优先级和次高优先级的请求
+      InteractionManager.runAfterInteractions(async () => {
+        // 所有优先级请求完成后，再获取待发布数量
+        const count = await fetchSecretCount();
+        setSecretsCount(count);
+        console.log('[LogPrefix][User][secretsCount] 待发布数量:', count);
+      });
+    }
+  }, [id, isMine, isFocused, goodsWallRes, secretsCount]);
+
+  useEffect(() => {
+    if (isFocused) {
+      reportExpo('invite_icon', {}, true);
+    }
   }, [isFocused]);
 
   /** 新手引导的 痛墙 bounce */
   const { lockBounce } = useGoodsShefStore(


@@ -446,75 +444,28 @@ 
   /** 新手引导的 痛墙 bounce */
   const { lockBounce } = useGoodsShefStore(
     useShallow(state => ({
       lockBounce: state.lockBounce
     }))
   );
 
   const isLoadBouncing = useRef(false);
 
   const resetLoadBouncing = () => {
     isLoadBouncing.current = false;
   };
 
-  useEffect(() => {
-    if (
-      isFocused &&
-      useStorageStore.getState().userGoodsHasbeBounced < 5 &&
-      (isMine || (!isMine && hasGoods)) &&
-      !lockBounce &&
-      profileGuideModalVisible !== true &&
-      isStickyAbs
-    ) {
-      const nextCount = useStorageStore.getState().userGoodsHasbeBounced + 1;
-      isLoadBouncing.current = true;
-      $goodsShareY.value = withDelay(
-        300,
-        withRepeat(
-          withSpring((maxScrollLimit / 6) * 5 - 10, {
-            damping: 80
-          }),
-          4,
-          true,
-          () => {
-            runOnJS(__setStorage)({
-              userGoodsHasbeBounced: nextCount
-            });
-            runOnJS(resetLoadBouncing)();
-            $goodsShareY.value = 0;
-          }
-        )
-      );
-    }
-  }, [isFocused, isMine, hasGoods, lockBounce, profileGuideModalVisible]);
-
   /**是否可回退 */
-  const [touchOffsetX, setTouchOffsetX] = useState(screenWidth);
-  const [isBack, setIsBack] = useState(false);
-  useEffect(() => {
-    if (isBack) {
-      router.back();
-    }
-  }, [isBack]);
 
   const pageScroll = useCallback(
     (e: { nativeEvent: { position: number; offset: number } }) => {
       const pos = e.nativeEvent.position + e.nativeEvent.offset;
 
-      // 只在非 Preview 模式下启用返回手势
-      if (
-        !fromSwipePreview &&
-        e.nativeEvent.position < 0 &&
-        touchOffsetX < 100
-      ) {
-        setIsBack(true);
-      }
-
-      if (pos >= 0 && pos < 2) {
-        $animatedIndictor.value = pos;
+      if (pos >= 0 && pos < (isMine ? 4 : 2)) {
+        $animatedTabIndictor.value = pos;
       }
     },
-    [touchOffsetX, fromSwipePreview]
+    [isMine]
   );
 
   useEffect(() => {
     setTimeout(() => {


@@ -518,42 +469,25 @@ 
 
   useEffect(() => {
     setTimeout(() => {
       reportExpo('all', {
         identity_status: isMine ? '0' : '1'
       });
     });
   }, []);
 
   /** tab更新 */
   useEffect(() => {
     if (pagerViewReady && queryPageTab && queryPageTab !== currentTab) {
       if (pagerRef.current) {
-        const tab = pageTabMap[queryPageTab as PageTab];
+        const tab = pageTabMap[queryPageTab as UserPageTab];
         pagerRef.current?.setPage(tab || 0);
       }
     }
   }, [queryPageTab, queryTimestamp, pagerViewReady]);
 
-  /** 部分场景重置所有动画 */
-  const cancelAllAnimate = () => {
-    cancelAnimation($goodsShareY);
-  };
-
-  const $animatedIndictor = useSharedValue(0);
-  const [isStickyAbs, setIsStickyAbs] = useState(true);
-
-  const [tabPosY, setTabPosY] = useState(0);
-  const $goodsMaskShareOpacity = useSharedValue(1);
-  const $goodsMaskStyle = useAnimatedStyle(() => ({
-    opacity: $goodsMaskShareOpacity.value
-  }));
-
-  const $tabStickyOpacity = useSharedValue(0);
-  const $tabStickyAnimateStyle = useAnimatedStyle(() => ({
-    opacity: $tabStickyOpacity.value,
-    display: $tabStickyOpacity.value === 0 ? 'none' : 'flex'
-  }));
+  const $animatedTabIndictor = useSharedValue(0);
+  const $animatedScrollPosition = useSharedValue(0);
 
   const { checkRetentionAndReport } = useRetentionPopup({
     isMine,
     stat,


@@ -563,47 +497,25 @@ 
   });
 
   useFocusEffect(
     useCallback(() => {
       reportExpo(
         'user_expo',
         {
           userId: id
         },
         true
       );
       return () => {
         if (isLoadBouncing.current) {
-          cancelAllAnimate();
+          // cancelAllAnimate();
         }
       };
     }, [id])
   );
 
   // 处理返回事件，用于 SwipeScreen 的 beforeBackPressed
-  useEffect(() => {
-    const handleUserScreenBack = (data: {
-      resolve: () => void;
-      ownerUid: string;
-    }) => {
-      const { resolve } = data;
-      const shouldShowPopup = checkRetentionAndReport(resolve);
-      if (!shouldShowPopup) {
-        // 如果不需要显示弹窗，正常返回
-        resolve();
-      }
-      // 否则阻止返回，不调用 resolve
-    };
-
-    // 注册事件处理函数
-    const handler = (event: { resolve: () => void; ownerUid: string }) =>
-      handleUserScreenBack(event);
-    CommonEventBus.on('handleUserScreenBack', handler);
-
-    return () => {
-      CommonEventBus.off('handleUserScreenBack', handler);
-    };
-  }, [checkRetentionAndReport]);
+  useHandleSwipeBack(checkRetentionAndReport);
 
   // 处理返回按钮点击
   const handleBack = useCallback(() => {
     const shouldShowPopup = checkRetentionAndReport();


@@ -608,22 +520,34 @@ 
   const handleBack = useCallback(() => {
     const shouldShowPopup = checkRetentionAndReport();
     if (!shouldShowPopup) {
       router.back();
     }
     // 否则阻止返回，让弹窗处理
   }, [checkRetentionAndReport]);
 
+  // 处理图集按钮点击
+  const handleAlbumPress = useCallback(() => {
+    reportClick('album_button', {
+      identity_status: isMine ? '0' : '1'
+    });
+
+    usePublishStore.getState().getAlbumPhotos(true);
+    usePublishStore.getState().getHistoryPhotos(true);
+    setShowAlbum(true);
+    usePerformanceStore.getState().recordStart('make_photo_photo_set_render', {
+      performance_type: 'render',
+      performance_key: AlbumFromType.USER
+    });
+  }, [isMine]);
+
+  const PagerViewGesture = Gesture.Native().shouldCancelWhenOutside(false);
+
   const renderPageView = useMemoizedFn(() => {
-    if (!currentUser?.uid) {
-      return <View />;
-    }
-
+    // NOTE(fuxiao): 如果这里判断 id 返回 View 或者 null，会导致 Android 无法滑动
     const commonParams = {
       id: id || '',
-      updateUnlockTop: updateUnlockTop,
       $safePaddingBottom: $safePaddingBottom,
-      nativeGesture: nativeGesture,
       queryRefresh: queryRefresh,
       queryPageTab: queryPageTab,
       queryTimestamp: queryTimestamp,
       isRefreshData,


@@ -629,20 +553,20 @@ 
       isRefreshData,
       isRootPage,
       currentTab
     };
 
     // 这里有点坑，在pageview 里面直接控制会报错，所以在这里处理
     const children = isMine
       ? [
-          <WorksFlowList key={0} scrollY={worksScrollY} {...commonParams} />,
-          <SecretsFlowList key={1} scrollY={livePhotoSY} {...commonParams} />,
-          <LikesFlowList key={2} scrollY={likeScrollY} {...commonParams} />,
-          <View key={3} />
+          <WorksFlowList key={0} {...commonParams} />,
+          <SecretsFlowList key={1} {...commonParams} />,
+          <LikesFlowList key={2} {...commonParams} />,
+          <MyRoleFlowList key={3} {...commonParams} />
         ]
       : [
-          <WorksFlowList key={0} scrollY={worksScrollY} {...commonParams} />,
-          <LikesFlowList key={2} scrollY={likeScrollY} {...commonParams} />
+          <WorksFlowList key={0} {...commonParams} />,
+          <LikesFlowList key={2} {...commonParams} />
         ];
 
     return (
       <PagerView


@@ -647,32 +571,25 @@ 
     return (
       <PagerView
         ref={pagerRef}
         style={{
           flex: 1,
           backgroundColor: darkTheme.background.page
         }}
         onPageSelected={e => {
+          const position = e.nativeEvent.position;
+          // 修复查找对应标签的逻辑
           const tab = Object.entries(pageTabMap).find(
-            (key, value) => value === e.nativeEvent.position
+            ([_, value]) => value === position
           );
-          if (tab?.[0] === PageTab.ALBUM) {
-            setShowAlbum(true);
-            usePerformanceStore
-              .getState()
-              .recordStart('make_photo_photo_set_render', {
-                performance_type: 'render',
-                performance_key: AlbumFromType.USER
-              });
-          }
           if (pagerViewReady && tab?.[0]) {
-            setCurrentTab((tab?.[0] as PageTab) || PageTab.WORKS);
+            setCurrentTab((tab?.[0] as UserPageTab) || UserPageTab.WORKS);
             setIsRefreshData(undefined);
           }
         }}
         // overdrag
         onTouchStart={e => {
-          setTouchOffsetX(e.nativeEvent.pageX);
+          // setTouchOffsetX(e.nativeEvent.pageX);
         }}
         onPageScroll={pageScroll}
         onLayout={() => {
           setTimeout(() => {


@@ -701,18 +618,18 @@ 
   }, [id, isRootPage, fromSwipePreview]);
 
   useEffect(() => {
     if (id) {
       userPerformanceCollector.markPerformanceTimestamp(
         'user_render_done_timestamp',
         id
       );
     }
   }, [id]);
 
   // 在作品列表开始加载时标记
   useEffect(() => {
-    if (currentTab === PageTab.WORKS && id) {
+    if (currentTab === UserPageTab.WORKS && id) {
       userPerformanceCollector.markPerformanceTimestamp(
         'user_works_init_timestamp',
         id
       );


@@ -715,405 +632,220 @@ 
       userPerformanceCollector.markPerformanceTimestamp(
         'user_works_init_timestamp',
         id
       );
     }
   }, [currentTab, id]);
 
-  if (!id) {
-    return (
-      <EmptyPlaceHolder
-        buttonText="立即登录"
-        button={true}
-        type="needlogin"
-        onButtonPress={showLogin}
-        style={{
-          height: '100%',
-          paddingBottom: $safePaddingBottom
-        }}
-      >
-        登录账号，查看你关注的精彩内容
-      </EmptyPlaceHolder>
-    );
-  }
+  const hasSlideUp = useSharedValue(false);
+
+  const { getGestureHandlers, pullDownProgress } =
+    usePullDownGestureHandlersFactory({
+      onPullDownEnd: useMemoizedFn((progress: number) => {
+        console.log('[LogPrefix][GoodsTopTip] 下拉结束，进度:', progress);
+        if (progress >= ANIMATION_PARAMS.PULL_GOODS_THRESHOLD) {
+          // 打开痛墙
+          checkEnterGoods();
+        } else if (progress >= ANIMATION_PARAMS.PULL_REFRESH_THRESHOLD) {
+          // 触发刷新
+          onRefreshData();
+        }
+      }),
+      scrollPosition: $animatedScrollPosition
+    });
+
+  // 创建橡皮筋效果的动画样式
+  const pullDownAnimatedStyle = useAnimatedStyle(() => {
+    // 检查是否有下拉值时应用变换
+    if (pullDownProgress.value <= 0 && !isLoadBouncing.current) {
+      return { transform: [{ translateY: 0 }] };
+    }
+
+    return {
+      transform: [{ translateY: pullDownProgress.value }]
+    };
+  });
+
+  // 添加引导动画效果
+  useEffect(() => {
+    if (
+      isMaskLoaded &&
+      isFocused &&
+      useStorageStore.getState().userGoodsHasbeBounced < 5 &&
+      (isMine || (!isMine && hasGoods)) &&
+      !isLoadBouncing.current &&
+      lockBounce &&
+      profileGuideModalVisible !== true
+    ) {
+      const nextCount = useStorageStore.getState().userGoodsHasbeBounced + 1;
+      isLoadBouncing.current = true;
+
+      // 使用 pullDownProgress 进行动画
+      pullDownProgress.value = withDelay(
+        1000,
+        withRepeat(
+          withSpring(ANIMATION_PARAMS.PULL_GOODS_THRESHOLD + 20, {
+            damping: 80
+          }),
+          4,
+          true,
+          () => {
+            runOnJS(__setStorage)({
+              userGoodsHasbeBounced: nextCount
+            });
+            runOnJS(resetLoadBouncing)();
+            pullDownProgress.value = 0;
+          }
+        )
+      );
+    }
+  }, [
+    isFocused,
+    isMine,
+    hasGoods,
+    profileGuideModalVisible,
+    lockBounce,
+    isMaskLoaded,
+    pullDownProgress,
+    __setStorage
+  ]);
 
   return (
     <PagePerformance pathname="user">
-      <FullScreen
+      <Screen
+        theme="dark"
         key={id}
-        style={{
-          backgroundColor: isStickyAbs
-            ? darkTheme.background.popup
-            : darkTheme.background.page,
-          position: 'relative'
-        }}
+        safeAreaEdges={[]}
+        headerShown={false}
+        backgroundView={
+          <GoodsWallBg
+            visible={isMaskLoaded}
+            goodsWallRes={goodsWallRes}
+            safeTop={safeTop}
+          />
+        }
       >
-        <Animated.View
-          style={[
-            {
-              position: 'absolute',
-              zIndex: -50,
-              width: '100%',
-              height: 481 + safeTop * 2,
-              opacity: 0.75,
-              transform: [
-                {
-                  translateY: -safeTop
-                }
-              ]
-            },
-            $goodsMaskStyle
-          ]}
-        >
-          <Image
-            source={GOODS_MASK}
-            style={{
-              flex: 1
+        <UserHeader
+          profile={profile}
+          stat={stat}
+          hasSlideUp={hasSlideUp}
+          isMine={isMine}
+          isRootPage={isRootPage}
+          onBack={handleBack}
+          safeTop={safeTop}
+        />
+        <GoodsTopTip
+          isMine={isMine}
+          hasGoods={hasGoods}
+          goodsLikes={goodsWallRes?.likes}
+          isGoodsTipAllowed={isSeqToastShow}
+          checkEnterGoods={checkEnterGoods}
+          safeTop={safeTop}
+          scrollPosition={$animatedScrollPosition}
+          pullDownProgress={pullDownProgress}
+        />
+        <Animated.View style={[{ flex: 1 }, pullDownAnimatedStyle]}>
+          <NestedScrollView
+            animatedPosition={$animatedScrollPosition}
+            hasSlideUp={hasSlideUp}
+            topPreserveInset={dp2px(88) + safeTop}
+            topInset={0}
+            simultaneousWithExternalGesture={
+              isIos ? undefined : PagerViewGesture
+            }
+            gestureEventsHandlersHook={getGestureHandlers}
+            failOffsetX={[-20, 20]}
+            activeOffsetY={[-20, 20]}
+            headerComponent={
+              <>
+                <UserPanel
+                  currentUser={currentUser as UserProfile}
+                  stat={stat}
+                  isMine={isMine}
+                  showFeedBack={() => setIsFeedbackShow(true)}
+                  hasGoods={hasGoods}
+                  currentTab={currentTab}
+                  $animatedIndictor={$animatedTabIndictor}
+                  pagerRef={pagerRef}
+                  onAlbumPress={handleAlbumPress}
+                  safeTop={safeTop}
+                  hasSlideUp={hasSlideUp}
+                  checkEnterGoods={checkEnterGoods}
+                  tabConfig={pageTabConfig}
+                />
+                <GoodsButton
+                  isMine={isMine}
+                  checkEnterGoods={checkEnterGoods}
+                  hasGoods={hasGoods}
+                  safeTop={safeTop}
+                />
+              </>
+            }
+          >
+            {isIos ? (
+              renderPageView()
+            ) : (
+              <GestureDetector gesture={PagerViewGesture}>
+                {renderPageView()}
+              </GestureDetector>
+            )}
+          </NestedScrollView>
+          <View
+            style={[
+              {
+                width: '100%',
+                height: screenHeight,
+                position: 'relative',
+                pointerEvents: 'box-none'
+              }
+            ]}
+          >
+            {currentUser?.uid && (
+              <View
+                style={{
+                  position: 'absolute',
+                  top: 0,
+                  left: 0,
+                  flex: 1
+                }}
+              >
+                <FeedbackSheet
+                  isVisible={isFeedbackShow}
+                  userId={currentUser?.uid}
+                  onClose={() => {
+                    setIsFeedbackShow(false);
+                  }}
+                />
+              </View>
+            )}
+
+            {profileGuideModalVisible && (
+              <ProfileGuideModal
+                visible={profileGuideModalVisible}
+                onClose={() => setProfileGuideModalVisible(false)}
+              />
+            )}
+          </View>
+
+          <MaskArea />
+
+          {isIos && <MaskArea width={10} />}
+          <AlbumSheet
+            // FIXME(fuxiao): 图集 Modal 上方按钮需要增加一个间距
+            style={
+              {
+                // paddingLeft: 20
+                // paddingRight: 20
+              }
+            }
+            callWhere={AlbumFromType.USER}
+            isVisible={showAlbum}
+            onClose={() => {
+              setShowAlbum(false);
             }}
-            contentFit="cover"
           />
         </Animated.View>
-
-        <Image
-          source={DEFAULT_MASK_BG}
-          contentFit="contain"
-          style={{
-            position: 'absolute',
-            zIndex: -1000,
-            width: '100%',
-            height: 481 + safeTop,
-            transform: [
-              {
-                translateY: -safeTop
-              }
-            ]
-          }}
-        />
-
-        <Image
-          source={goodsBgWall}
-          contentFit="contain"
-          tosSize="size1"
-          style={{
-            position: 'absolute',
-            zIndex: -100,
-            width: '100%',
-            height: 481 + safeTop * 2,
-            opacity: 1,
-            transform: [
-              {
-                translateY: -safeTop
-              }
-            ]
-          }}
-        />
-
-        <Animated.View
-          style={[
-            {
-              position: 'absolute',
-              top: safeTop + 10,
-              left: 0,
-              right: 0,
-              justifyContent: 'center',
-              flexDirection: 'row',
-              width: '100%'
-            },
-            refreshWrapStyle
-          ]}
-        >
-          <Animated.View style={refreshStyle}>
-            <Icon icon="loading" style={{ width: 24, height: 24 }} />
-          </Animated.View>
-          <Animated.View
-            style={[enterGoodsStyle, { borderRadius: 15, overflow: 'hidden' }]}
-          >
-            <Text
-              style={{
-                height: 30,
-                paddingHorizontal: 8,
-                backgroundColor: 'rgba(0, 0,0, 0.5)',
-                color: '#ffffff',
-                fontSize: 14,
-                textAlign: 'center',
-                lineHeight: 30
-              }}
-            >
-              打开狸小窝
-            </Text>
-          </Animated.View>
-        </Animated.View>
-
-        <Animated.View
-          style={{
-            position: 'absolute',
-            paddingTop: safeTop - 8,
-            top: 0,
-            width: '100%',
-            zIndex: 1000
-          }}
-        >
-          <InfoProfile
-            id={id}
-            isRootPage={isRootPage}
-            isMine={isMine}
-            creditOpacity={creditOpacity}
-            showAvatar={false}
-            showFeedBack={() => setIsFeedbackShow(true)}
-            onBack={handleBack}
-            showBackButton={!fromSwipePreview}
-          />
-        </Animated.View>
-
-        <Animated.View
-          style={[
-            $tabStickyAnimateStyle,
-            {
-              position: 'absolute',
-              paddingTop: safeTop - 8,
-              top: 0,
-              width: '100%',
-              zIndex: 1000,
-              backgroundColor: darkTheme.background.page
-            }
-          ]}
-        >
-          <InfoProfile
-            id={id}
-            isRootPage={isRootPage}
-            isMine={isMine}
-            creditOpacity={creditOpacity}
-            showAvatar={true}
-            currentUser={currentUser as UserProfile}
-            showBackButton={!fromSwipePreview}
-          />
-
-          <Tabs
-            current={currentTab}
-            items={pageTabConfig}
-            tabBarStyle={{
-              ...tabStyles.$tabStyle,
-              backgroundColor: darkTheme.background.input
-            }}
-            animatedTabIndex={$animatedIndictor}
-            tabWidth={60}
-            itemStyle={tabStyles.$tabItemStyle}
-            itemTextStyle={{
-              ...tabStyles.$tabItemTextStyle,
-              color: darkTheme.text.disabled
-            }}
-            onPressTab={(_, type) => {
-              if (type === PageTab.ALBUM) {
-                console.log('=======type=====', type);
-                usePublishStore.getState().getAlbumPhotos(true);
-                usePublishStore.getState().getHistoryPhotos(true);
-                setShowAlbum(true);
-                usePerformanceStore
-                  .getState()
-                  .recordStart('make_photo_photo_set_render', {
-                    performance_type: 'render',
-                    performance_key: AlbumFromType.USER
-                  });
-                return;
-              }
-              if (type === PageTab.SECRET) {
-                reportClick('secret_button', { type: 'video' });
-              } else {
-                reportClick('button', {
-                  user_button: type === PageTab.LIKE ? 9 : 8,
-                  identity_status: isMine ? '0' : '1'
-                });
-              }
-
-              const tab = pageTabMap[type as PageTab];
-              pagerRef.current?.setPage(tab || 0);
-            }}
-            renderIndicator={({ tabWidth, animatedStyle }) => (
-              <Animated.View
-                style={[
-                  {
-                    width: tabWidth,
-                    position: 'absolute',
-                    bottom: -2,
-                    alignItems: 'center'
-                  },
-                  animatedStyle
-                ]}
-              >
-                <View
-                  style={{
-                    width: 24,
-                    height: 2,
-                    borderRadius: 5,
-                    backgroundColor: StyleSheet.currentColors.brand1
-                  }}
-                />
-              </Animated.View>
-            )}
-          />
-        </Animated.View>
-
-        <GestureDetector gesture={holdGesture}>
-          <Animated.View style={[{ flex: 1 }, $goodsTransBgStyle]}>
-            <View
-              style={[
-                {
-                  width: '100%',
-                  height: topHeight + (safeTop < 44 ? 44 : 0)
-                }
-              ]}
-            >
-              <Pressable
-                style={{ position: 'relative', flex: 1 }}
-                onPress={checkEnterGoods}
-              >
-                <View
-                  style={[
-                    {
-                      flex: 1,
-                      justifyContent: 'flex-end'
-                    }
-                  ]}
-                >
-                  <InfoSection
-                    isRootPage={isRootPage}
-                    currentUser={currentUser as UserProfile}
-                    stat={stat}
-                    isMine={isMine}
-                    showFeedBack={() => setIsFeedbackShow(true)}
-                    animateStyle={$goodsMaskStyle}
-                    isAllowed={isSeqToastShow}
-                    isSlideToTop={isStickyAbs}
-                    goodsLikes={goodsLikes}
-                    hasGoods={hasGoods}
-                    checkEnterGoods={checkEnterGoods}
-                  />
-                </View>
-              </Pressable>
-
-              <Animated.View
-                onLayout={e => {
-                  if (!tabPosY) {
-                    setTabPosY(e.nativeEvent.layout.y);
-                  }
-                }}
-              >
-                <Tabs
-                  current={currentTab}
-                  items={pageTabConfig}
-                  animatedTabIndex={$animatedIndictor}
-                  tabBarStyle={{
-                    ...tabStyles.$tabStyle,
-                    backgroundColor: darkTheme.background.input
-                  }}
-                  tabWidth={60}
-                  itemStyle={tabStyles.$tabItemStyle}
-                  itemTextStyle={{
-                    ...tabStyles.$tabItemTextStyle,
-                    color: darkTheme.text.disabled
-                  }}
-                  onPressTab={(_, type) => {
-                    if (type === PageTab.ALBUM) {
-                      console.log('=======type=====', type);
-                      usePublishStore.getState().getAlbumPhotos(true);
-                      usePublishStore.getState().getHistoryPhotos(true);
-                      setShowAlbum(true);
-                      usePerformanceStore
-                        .getState()
-                        .recordStart('make_photo_photo_set_render', {
-                          performance_type: 'render',
-                          performance_key: AlbumFromType.USER
-                        });
-                      return;
-                    }
-                    if (type === PageTab.SECRET) {
-                      reportClick('secret_button', { type: 'video' });
-                    } else {
-                      reportClick('button', {
-                        user_button: type === PageTab.LIKE ? 9 : 8,
-                        identity_status: isMine ? '0' : '1'
-                      });
-                    }
-
-                    const tab = pageTabMap[type as PageTab];
-                    pagerRef.current?.setPage(tab || 0);
-                  }}
-                  renderIndicator={({ tabWidth, animatedStyle }) => (
-                    <Animated.View
-                      style={[
-                        {
-                          width: tabWidth,
-                          position: 'absolute',
-                          bottom: -2,
-                          alignItems: 'center'
-                        },
-                        animatedStyle
-                      ]}
-                    >
-                      <View
-                        style={{
-                          width: 24,
-                          height: 2,
-                          borderRadius: 5,
-                          backgroundColor: StyleSheet.currentColors.brand1
-                        }}
-                      />
-                    </Animated.View>
-                  )}
-                />
-              </Animated.View>
-            </View>
-
-            <View
-              style={[
-                {
-                  width: '100%',
-                  height: screenHeight - (returnTop ? 200 : 0),
-                  position: 'relative'
-                }
-              ]}
-            >
-              {renderPageView()}
-              {currentUser?.uid && (
-                <View
-                  style={{
-                    position: 'absolute',
-                    top: 0,
-                    left: 0,
-                    flex: 1
-                  }}
-                >
-                  <FeedbackSheet
-                    isVisible={isFeedbackShow}
-                    userId={currentUser?.uid}
-                    onClose={() => {
-                      setIsFeedbackShow(false);
-                    }}
-                  />
-                </View>
-              )}
-
-              {profileGuideModalVisible && (
-                <ProfileGuideModal
-                  visible={profileGuideModalVisible}
-                  onClose={() => setProfileGuideModalVisible(false)}
-                />
-              )}
-            </View>
-          </Animated.View>
-        </GestureDetector>
-
-        <MaskArea />
-        <AlbumSheet
-          callWhere={AlbumFromType.USER}
-          isVisible={showAlbum}
-          onClose={() => {
-            const tab = pageTabMap[PageTab.WORKS];
-            pagerRef.current?.setPage(tab || 0);
-            setShowAlbum(false);
-          }}
-        />
-      </FullScreen>
+      </Screen>
     </PagePerformance>
   );
 }
 


@@ -1116,38 +848,7 @@ 
     </PagePerformance>
   );
 }
 
 export const UserScreen = memo(User, (prev, now) => {
   return prev.timestamp === now.timestamp;
 });
-
-const tabStyles = StyleSheet.create({
-  $tabStyle: {
-    ...StyleSheet.rowStyle,
-    borderColor: StyleSheet.hex(StyleSheet.currentColors.black, 0.08),
-    height: 44,
-    backgroundColor: 'white',
-    borderTopLeftRadius: 16,
-    borderTopRightRadius: 16,
-    paddingHorizontal: 70
-  },
-  $tabItemStyle: {
-    // flex: 1
-  },
-  $tabItemTextStyle: {
-    fontSize: 14,
-    lineHeight: 20
-  },
-
-  $tabActiveStyle: {
-    color: '#222222'
-  },
-  $tabActiveBorder: {
-    ...StyleSheet.rowStyle,
-    position: 'absolute',
-    width: '100%',
-    height: '100%',
-    bottom: -13,
-    justifyContent: 'center'
-  }
-});



## File: 'src/bizComponents/userScreen/types.ts'

@@ -1,23 +1,12 @@ 
-import { GestureType } from 'react-native-gesture-handler';
-import { SharedValue } from 'react-native-reanimated';
-
-export enum PageTab {
-  WORKS = 'works',
-  SECRET = 'live_photo',
-  LIKE = 'like',
-  ALBUM = 'album'
-}
+import { UserPageTab } from './constants';
 
 export interface FlowCommonProps {
   id: string;
-  updateUnlockTop: (status: boolean) => void;
   $safePaddingBottom: number;
-  scrollY: SharedValue<number>;
-  nativeGesture: GestureType;
   queryRefresh?: string;
   queryPageTab?: string;
   queryTimestamp?: string;
   isRefreshData?: [];
   isRootPage?: boolean;
-  currentTab: PageTab;
+  currentTab: UserPageTab;
 }



## File: 'src/bizComponents/userScreen/useAnimation.ts'

@@ -1,132 +1,0 @@ 
-import { Platform } from 'react-native';
-import { Gesture } from 'react-native-gesture-handler';
-import {
-  runOnJS,
-  useAnimatedStyle,
-  useSharedValue,
-  withSpring,
-  withTiming
-} from 'react-native-reanimated';
-import { useIsFocused } from '@react-navigation/native';
-import { PageTab } from './types';
-
-export const useAnimation = ({
-  currentTab,
-  checkEnterGoods,
-  maxScrollLimit,
-  onRefreshData,
-  setReturnTop
-}) => {
-  const $goodsShareY = useSharedValue(0);
-  const refreshTypeS = useSharedValue(-1);
-  const worksScrollY = useSharedValue(0);
-  const livePhotoSY = useSharedValue(0);
-  const likeScrollY = useSharedValue(0);
-
-  const scrollY =
-    currentTab === PageTab.WORKS
-      ? worksScrollY
-      : currentTab === PageTab.SECRET
-        ? livePhotoSY
-        : likeScrollY;
-
-  const nativeGesture = Gesture.Native();
-
-  const refreshStyle = useAnimatedStyle(() => ({
-    opacity: refreshTypeS.value === 0 ? 1 : 0,
-    display: refreshTypeS.value === 0 ? 'flex' : 'none'
-  }));
-
-  const enterGoodsStyle = useAnimatedStyle(() => ({
-    opacity: refreshTypeS.value === 1 ? 1 : 0,
-    display: refreshTypeS.value === 1 ? 'flex' : 'none'
-  }));
-
-  const refreshWrapStyle = useAnimatedStyle(() => ({
-    display: refreshTypeS.value === -1 ? 'none' : 'flex'
-  }));
-
-  const iosMoveSv = useSharedValue(0);
-  const isFocused = useIsFocused();
-  const holdGesture = Gesture.Pan()
-    .enabled(isFocused)
-    .activeOffsetY(30)
-    .onBegin(e => {
-      iosMoveSv.value = e.absoluteY;
-    })
-    .maxPointers(2)
-    .onTouchesMove(e => {
-      //   ios 无法响应 onChange
-      if (Platform.OS === 'ios') {
-        if (scrollY.value > 0) {
-          return;
-        }
-        const abY = e.allTouches[0].absoluteY;
-        if (abY < iosMoveSv.value) {
-          return;
-        }
-
-        const curV = (abY - iosMoveSv.value) * 3;
-
-        $goodsShareY.value = withSpring(curV, {
-          damping: 100,
-          stiffness: 100 + Math.floor(Math.abs(curV) / maxScrollLimit)
-        });
-        let v = -1;
-
-        if (curV >= maxScrollLimit / 5) {
-          v = 1;
-        }
-        refreshTypeS.value = withTiming(v, { duration: 100 });
-      }
-    })
-    .onChange(e => {
-      if (scrollY.value > 0) {
-        runOnJS(setReturnTop)(false);
-        return;
-      }
-
-      $goodsShareY.value = withSpring($goodsShareY.value + e.changeY, {
-        damping: 100,
-        stiffness: 100 + Math.floor(Math.abs(e.translationY) / maxScrollLimit)
-      });
-      let v = -1;
-
-      if (e.translationY >= maxScrollLimit / 5) {
-        v = 1;
-      }
-      refreshTypeS.value = v;
-    })
-    .onFinalize(() => {
-      if (scrollY.value > 0) {
-        refreshTypeS.value = -1;
-        return;
-      }
-
-      runOnJS(setReturnTop)(true);
-      $goodsShareY.value = withSpring(0, {
-        damping: 100,
-        stiffness: 100
-      });
-      if (refreshTypeS.value === 0) {
-        runOnJS(onRefreshData)();
-      } else if (refreshTypeS.value === 1) {
-        // 超过阈值进入谷子
-        runOnJS(checkEnterGoods)();
-      }
-      refreshTypeS.value = -1;
-    })
-    .simultaneousWithExternalGesture(nativeGesture);
-
-  return {
-    holdGesture,
-    nativeGesture,
-    $goodsShareY,
-    refreshStyle,
-    enterGoodsStyle,
-    refreshWrapStyle,
-    worksScrollY,
-    livePhotoSY,
-    likeScrollY
-  };
-};



## File: 'src/bizComponents/userScreen/utils.ts'

@@ -1,6 +1,13 @@ 
+import { router } from 'expo-router';
+import { feedClient } from '@/src/api';
+import { getUserShowAsyncCardCnt } from '@/src/api/asynccard/index';
 import { showToast } from '@/src/components';
 import { RequestScene } from '@/src/components/infiniteList/typing';
+import { dp2px } from '@/src/utils';
+import { reportClick } from '@/src/utils/report';
+import { catchErrorLog, errorReport } from '../../utils/error-log';
+import { AsyncCardStatus } from '@/proto-registry/src/web/raccoon/common/asynccard_pb';
 
 /** feed流部分 */
 export const onRefreshError = (scene: RequestScene) => {
   return scene === RequestScene.REFRESHING


@@ -3,7 +10,73 @@ export const onRefreshError = (scene: RequestScene) => {
 
 /** feed流部分 */
 export const onRefreshError = (scene: RequestScene) => {
   return scene === RequestScene.REFRESHING
     ? showToast('刷新失败啦，请重试')
     : undefined;
 };
+
+/**
+ * 统一处理编辑资料的点击事件
+ * @param customHandler 自定义处理函数，如果提供则优先使用
+ */
+export const handleEditProfile = (customHandler?: () => void) => {
+  if (customHandler) {
+    customHandler();
+  } else {
+    router.push('/profile/edit');
+    setTimeout(() => {
+      reportClick('edit_profile');
+    });
+  }
+};
+
+/**
+ * 计算用户面板的高度相关参数
+ * @param isMine 是否是当前用户自己的面板
+ * @param hasGoods 是否有商品
+ * @returns 包含 topHeight 和 maskTop 的对象
+ */
+export const calculatePanelHeight = () => {
+  // 所有可能的高度配置
+  const heights = {
+    visitorNoGoods: {
+      topHeight: dp2px(267),
+      maskTop: dp2px(13)
+    },
+    visitorWithGoods: {
+      topHeight: dp2px(386),
+      maskTop: dp2px(89)
+    },
+    mineNoGoods: {
+      topHeight: dp2px(355),
+      maskTop: dp2px(12)
+    },
+    mineWithGoods: {
+      topHeight: dp2px(461),
+      maskTop: dp2px(76)
+    }
+  };
+
+  return heights;
+};
+
+/**
+ * 获取用户待发布内容的数量
+ * @returns 待发布内容的数量
+ */
+export const fetchSecretCount = async (): Promise<number> => {
+  try {
+    const res = await getUserShowAsyncCardCnt({});
+    // 过滤出未发布的内容
+    const secretsCount = Number(res.cnt);
+
+    return secretsCount;
+  } catch (error) {
+    console.log(
+      '[LogPrefix][fetchSecretCount][utils] 获取待发布数量失败',
+      error
+    );
+    catchErrorLog('fetchSecretCount_error', error);
+    return 0;
+  }
+};



## File: 'src/bizComponents/videoMagic/button/generateButton.tsx'

@@ -23,9 +23,9 @@ 
 import { reportClick } from '@/src/utils/report';
 import ToastInner from '../../credit/toast';
 import { TStoryBoardItem } from '../../magic-video/draggieStoryboard';
 import { WINDOW_WIDTH } from '../../nestedScrollView';
-import { PageTab } from '../../userScreen/types';
+import { UserPageTab } from '../../userScreen/constants';
 import { PlotType } from '@/proto-registry/src/web/raccoon/common/reimagine_pb';
 import { InvokeType } from '@/proto-registry/src/web/raccoon/common/types_pb';
 import { GameType } from '@/proto-registry/src/web/raccoon/common/types_pb';
 import {


@@ -259,13 +259,13 @@ export default function GenerateButton() {
               storyboardtotal: mediaSource.length,
               dialoguetoatal: mediaSource.filter(m => m.ttsUrl)?.length
             });
             console.log(res, '生成视频成功===');
             syncVideoGenerating(false);
 
             afterPublish({
               tab: TabItemType.PROFILE,
-              pageTab: PageTab.SECRET,
+              pageTab: UserPageTab.SECRET,
               refresh: true,
               appendId: '200',
               scene: Go2HomeScene.VIDEO_GENERATING
             });



## File: 'src/components/avatar/index.tsx'

@@ -60,12 +60,13 @@ export type AvatarProps = {
         height?: number;
         borderWidth?: number;
       };
   roleSource?: RoleSource;
   onPress?: (e: GestureResponderEvent) => void;
   theme?: Theme;
   borderWidth?: number;
   borderColor?: ColorValue;
+  outerBorder?: boolean; // 添加外边框选项
   onLoad?: () => void;
 };
 
 export function Avatar(props: AvatarProps) {


@@ -71,13 +72,14 @@ 
 export function Avatar(props: AvatarProps) {
   const {
     showTag = true,
     roleBadge = null,
     cover = '',
     theme = Theme.DARK,
     showPendant = false,
     borderWidth = 0,
-    borderColor = 'rgba(255,255,255,0.06)'
+    borderColor = 'rgba(255,255,255,0.06)',
+    outerBorder = false
   } = props;
 
   const { loginIntercept } = useAuthState();
   const { getRoleDetail } = useRoleDetail();


@@ -87,14 +89,17 @@ export function Avatar(props: AvatarProps) {
     }))
   );
 
   const isMine = uid === props?.profile?.uid;
 
   const { teenModeGuard } = useTeenModeGuard();
 
   const innerSize = useMemo(
-    () => Math.max((props.size || 50) - borderWidth * 2, 0),
-    [borderWidth, props.size]
+    () =>
+      outerBorder
+        ? props.size || 50
+        : Math.max((props.size || 50) - borderWidth * 2, 0),
+    [borderWidth, props.size, outerBorder]
   );
 
   return (
     <Pressable


@@ -97,22 +102,33 @@ export function Avatar(props: AvatarProps) {
   );
 
   return (
     <Pressable
       onStartShouldSetResponderCapture={() => false}
       onPress={handlePress}
       style={{
-        width: props.size || 50,
-        height: props.size || 50,
+        width: outerBorder
+          ? (props.size || 50) + borderWidth * 2
+          : props.size || 50,
+        height: outerBorder
+          ? (props.size || 50) + borderWidth * 2
+          : props.size || 50,
         // overflow: 'hidden',
         ...centerStyle,
-        ...StyleSheet.circleStyle
+        ...StyleSheet.circleStyle,
+        ...(outerBorder && borderWidth > 0
+          ? {
+              borderWidth,
+              borderColor,
+              padding: borderWidth
+            }
+          : {})
       }}
     >
       <Image
         source={
           formatTosUrl(props.profile?.avatar || props.source || '', {
-            size: innerSize ? innerSize * 3 : 'size10'
+            size: innerSize ? Math.ceil(innerSize * 3) : 'size10'
           }) || (isMine ? DEFAULT_AVATAR_MINE : DEFAULT_AVATAR)
         }
         style={{
           width: innerSize,


@@ -115,13 +131,12 @@ export function Avatar(props: AvatarProps) {
           }) || (isMine ? DEFAULT_AVATAR_MINE : DEFAULT_AVATAR)
         }
         style={{
           width: innerSize,
           height: innerSize,
           borderRadius: 500,
           zIndex: 0,
-          borderWidth,
-          borderColor
+          ...(outerBorder ? {} : { borderWidth, borderColor })
         }}
         onLoad={props.onLoad}
       />
       {!roleBadge &&


@@ -177,15 +192,15 @@ export function Avatar(props: AvatarProps) {
           borderWidth={roleBadge.borderWidth}
         />
       )}
     </Pressable>
   );
 
   function handlePress(e: GestureResponderEvent) {
     // clickEffect();
-
     if (!teenModeGuard()) return;
     if (props.onPress) {
+      console.log('### handlePress', props.onPress?.toString());
       props.onPress(e);
       return;
     }
 


@@ -258,14 +273,16 @@ 
 function AvatarTag({
   profile,
   avatarSize
 }: {
   profile?: UserProfile | null;
   avatarSize: number;
 }) {
   const size = useMemo(() => {
     if (avatarSize >= 100) {
       return 24;
+    } else if (avatarSize >= 90) {
+      return 20;
     } else if (avatarSize >= 50) {
       return 16;
     } else {
       return 14;


@@ -270,12 +287,14 @@ function AvatarTag({
     } else {
       return 14;
     }
   }, [avatarSize]);
   const position = useMemo(() => {
     switch (size) {
       case 24:
         return 6;
+      case 20:
+        return 4;
       case 16:
         return 0;
       case 14:
         return -2;



## File: 'src/components/follow/index.tsx'

@@ -230,12 +230,13 @@ export const Follow = (props: IFollowProps) => {
 
   return (
     <View style={externalStyle}>
       <Pressable
         disabled={disabled}
         onPressIn={onPressIn}
         onPressOut={onPressOut}
         onPress={onPress}
+        hitSlop={10}
       >
         {customRender ? (
           <View>{customRender(internalFollowed)}</View>
         ) : (



## File: 'src/components/icons/icon.tsx'

@@ -564,13 +564,13 @@ export const iconRegistry = {
   // avatar-pendant
   locked: require('@Assets/icon/locked.png'),
   up_new: require('@Assets/icon/up_new.png'),
 
   // 图集
   duntu_icon: require('@Assets/icon/gallery/duntu.png'),
   warning: require('@Assets/icon/icon-warning.png'),
   message: require('@Assets/icon/icon-message.png'),
-  message_comment: require('@Assets/icon/message-comment.png'),
+  message_comment: require('@Assets/icon/message-comment.png')
 };
 
 const $imageStyle: ImageStyle = {
   resizeMode: 'contain'



## File: 'src/components/infiniteList/CustomNestedInnerScrollView.tsx'

@@ -87,13 +87,13 @@ export const CustomNestedInnerScrollView = forwardRef(
       };
     }, [manuallyRefreshing]);
 
     return (
       <BottomSheetScrollView
         isActive={isActive}
         // @ts-ignore
         ref={ref}
-        // bounces={false}
+        bounces={bounces}
         {...props}
         onScroll={onScroll1}
         onRefresh={onRefresh}
         refreshing={refreshing}



## File: 'src/components/infiniteList/typing.ts'

@@ -15,11 +15,18 @@ 
 export enum RequestScene {
   INIT = 'init',
   REFRESHING = 'refresh',
   LOAD_MORE = 'append',
   AFTER_PUBLISH = 'after_publish'
 }
 
+export interface InfiniteScrollViewProps extends ScrollViewProps {
+  contentStyle?: StyleProp<ViewStyle>;
+  ref?: React.MutableRefObject<ScrollView | null>;
+  hideRefresh?: boolean;
+  lockScroll?: boolean;
+}
+
 export interface IInfiniteListProps<T> {
   // loading 状态
   loading?: boolean;
   // 列表数据


@@ -33,18 +40,13 @@ export interface IInfiniteListProps<T> {
   // 触发请求回调
   onRequest?: (scene: RequestScene) => Promise<void> | void;
   // 滚动事件回调
   onScroll?: (offset: { offsetX: number; offsetY: number }) => void;
 
   // 自定义
   customListProps?: Partial<RecyclerListViewProps>;
   // scrollView props 透传
-  scrollViewProps?: ScrollViewProps & {
-    contentStyle?: StyleProp<ViewStyle>;
-    ref?: React.MutableRefObject<ScrollView | null>;
-    hideRefresh?: boolean;
-    lockScroll?: boolean;
-  };
+  scrollViewProps?: InfiniteScrollViewProps;
   // 是否开启下拉刷新
   enablePullRefresh?: boolean;
   // 自定义 LayoutProvider
   getLayoutProvider: (



## File: 'src/components/skeletion/index.tsx'

@@ -46,9 +46,8 @@ 
 export function SkeletonSpan({
   width,
   height,
   radius,
-  // FIXME(fuxiao): 需要根据主题来设置
   theme = Theme.DARK,
   style: $customStyle,
   children
 }: SkeletonSpanProps) {


@@ -74,9 +73,8 @@ 
 export function SkeletonRow({
   children,
   gap = 10,
   repeat,
-  // FIXME(fuxiao): 需要根据主题来设置
   theme = Theme.DARK,
   style: $customStyle
 }: {
   children?: React.ReactNode;


@@ -110,10 +108,9 @@ 
 export function SkeletonColumn({
   children,
   gap = 10,
   repeat,
   style: $customStyle,
-  // FIXME(fuxiao): 需要根据主题来设置
   theme = Theme.DARK
 }: {
   children?: React.ReactNode;
   gap?: number;



## File: 'src/components/tabs/dynamic-tabs.tsx'

@@ -0,0 +1,355 @@
+import { useMemoizedFn } from 'ahooks';
+import React, { ReactElement, useEffect } from 'react';
+import { Pressable, TextStyle, View, ViewStyle } from 'react-native';
+import Animated, {
+  AnimatedStyleProp,
+  SharedValue,
+  useAnimatedStyle,
+  useDerivedValue,
+  useSharedValue,
+  withTiming
+} from 'react-native-reanimated';
+import { Text } from '@/src/components';
+import { $Z_INDEXES, $flexCenter, $flexRow } from '@/src/theme/variable';
+import { StyleSheet } from '@/src/utils';
+
+export type Tab = {
+  title: string;
+  key?: string | number;
+  width: number; // 必须提供宽度
+  renderItem?: (props: {
+    isActive: boolean;
+    textStyle: TextStyle[];
+  }) => ReactElement;
+};
+
+export interface DynamicWidthTabsProps<T extends Tab> {
+  items: T[]; //数据
+  current: number | string; // 当前的激活态的key或index
+  animatedTabIndex?: SharedValue<number>; // 与PagerView配合使用，获取滑动手势触发的TabIndicator变化
+  onPressTab: (tabIndex: number, tabKey?: T['key']) => void; // 点击事件
+  // 手动渲染Tab节点
+  renderItem?: (props: {
+    item: T;
+    isActive: boolean;
+    textStyle: TextStyle[];
+  }) => ReactElement; // 全局renderItem
+  animatedActive?: boolean;
+  tabBarStyle?: ViewStyle; // 最外层的bar的样式
+  itemStyle?: ViewStyle; //item的样式
+  itemTextStyle?: TextStyle; // 文字样式
+  tabGap?: number; // tab间的间隙
+  indicatorBottomOffset?: number; // 指示器与文字的间距
+  // 手动渲染indicator(必需把animatedStyle赋值给最外层的元素)
+  renderIndicator?: (props: {
+    animatedStyle: AnimatedStyleProp<ViewStyle>;
+  }) => React.ReactElement | undefined;
+  renderOthers?: () => ReactElement;
+}
+
+// UI线程工作函数 - 计算位置和宽度
+const calculatePositionsAndWidths = (
+  widths: readonly number[],
+  tabGap: number
+) => {
+  'worklet';
+  const positions: number[] = [0];
+
+  for (let i = 1; i < widths.length; i++) {
+    positions[i] = positions[i - 1] + widths[i - 1] + tabGap;
+  }
+
+  return { positions, widths };
+};
+
+// 插值计算 - 在UI线程运行
+const interpolate = (current: number, start: number, end: number) => {
+  'worklet';
+  return start + (end - start) * current;
+};
+
+export function DynamicWidthTabs<T extends Tab>({
+  items,
+  onPressTab,
+  current = 0,
+  animatedTabIndex,
+  tabBarStyle = {},
+  itemTextStyle = {},
+  itemStyle,
+  tabGap = 24,
+  animatedActive,
+  renderIndicator,
+  renderOthers,
+  indicatorBottomOffset = -4,
+  renderItem
+}: DynamicWidthTabsProps<T>) {
+  const validIndex =
+    typeof current === 'number'
+      ? current
+      : items.findIndex(item => item.key === current);
+
+  // 保存items到共享值，以便在worklet中访问
+  const $itemWidths = useSharedValue([...items.map(item => item.width)]);
+  const $tabGap = useSharedValue(tabGap);
+  const $validIndex = useSharedValue(validIndex);
+
+  // 当props变化时更新共享值
+  useEffect(() => {
+    $tabGap.value = tabGap;
+    $validIndex.value = validIndex;
+    $itemWidths.value = [...items.map(item => item.width)];
+  }, [items, tabGap, validIndex]);
+
+  // 在UI线程计算位置和宽度 - 避免JS到UI线程的数据传输
+  const $cachedData = useDerivedValue(() => {
+    return calculatePositionsAndWidths($itemWidths.value, $tabGap.value);
+  }, [$itemWidths, $tabGap]);
+
+  // 目标位置和宽度 - 在UI线程计算
+  const $targetPosition = useDerivedValue(() => {
+    const { positions } = $cachedData.value;
+    return positions[$validIndex.value] || 0;
+  }, [$cachedData, $validIndex]);
+
+  const $targetWidth = useDerivedValue(() => {
+    const { widths } = $cachedData.value;
+    return widths[$validIndex.value];
+  }, [$cachedData, $validIndex]);
+
+  // 动画到目标位置和宽度
+  const $animatedPosition = useDerivedValue(() => {
+    if (!animatedTabIndex) {
+      return withTiming($targetPosition.value, { duration: 300 });
+    }
+
+    const { positions } = $cachedData.value;
+    const idx = Math.floor(animatedTabIndex.value);
+    const fraction = animatedTabIndex.value % 1;
+
+    // 处理边界情况
+    if (fraction === 0 || idx < 0 || idx >= positions.length - 1) {
+      return idx >= 0 && idx < positions.length
+        ? positions[idx]
+        : $targetPosition.value;
+    }
+
+    // UI线程插值计算，更高效
+    const startPos = positions[idx];
+    const endPos = positions[idx + 1];
+    return interpolate(fraction, startPos, endPos);
+  });
+
+  const $animatedWidth = useDerivedValue(() => {
+    if (!animatedTabIndex) {
+      return withTiming($targetWidth.value, { duration: 300 });
+    }
+
+    const { widths } = $cachedData.value;
+    const idx = Math.floor(animatedTabIndex.value);
+    const fraction = animatedTabIndex.value % 1;
+
+    // 处理边界情况
+    if (fraction === 0 || idx < 0 || idx >= widths.length - 1) {
+      return idx >= 0 && idx < widths.length ? widths[idx] : $targetWidth.value;
+    }
+
+    // UI线程插值计算，更高效
+    const startWidth = widths[idx];
+    const endWidth = widths[idx + 1];
+    return interpolate(fraction, startWidth, endWidth);
+  });
+
+  // 动画样式 - 只需要位置和宽度变化，不需要scale效果
+  const $indicatorAnimateStyle = useAnimatedStyle(() => {
+    return {
+      transform: [{ translateX: $animatedPosition.value }],
+      width: $animatedWidth.value
+    };
+  });
+
+  const $activeIndex = useSharedValue(-1);
+
+  useEffect(() => {
+    if ($activeIndex.value !== validIndex) $activeIndex.value = validIndex;
+  }, [validIndex]);
+
+  // 渲染Tab内容
+  const renderTabContent = useMemoizedFn(
+    (tab: T, idx: number, $textStyle: TextStyle) => {
+      const inactiveStyle = animatedActive ? {} : $inactiveTabText;
+
+      const textStyle: TextStyle[] = [
+        $tabText,
+        { width: tab.width },
+        validIndex === idx ? $activeTabText : inactiveStyle,
+        itemTextStyle || {}
+      ];
+
+      const isActive = validIndex === idx;
+
+      // 从原始items中获取renderItem，而不是从共享值中获取
+      const originalTab = items[idx];
+
+      // 优先使用单个tab的renderItem，其次使用全局renderItem
+      if (originalTab.renderItem) {
+        return originalTab.renderItem({
+          isActive,
+          textStyle: [...textStyle, $textStyle]
+        });
+      } else if (renderItem) {
+        return renderItem({
+          item: tab,
+          isActive,
+          textStyle: [...textStyle, $textStyle]
+        });
+      }
+
+      return (
+        <Animated.Text numberOfLines={1} style={[...textStyle, $textStyle]}>
+          {tab.title}
+        </Animated.Text>
+      );
+    }
+  );
+
+  return (
+    <View style={[$tabBar, tabBarStyle]}>
+      <View style={{ position: 'relative' }}>
+        {renderIndicator ? (
+          renderIndicator({
+            animatedStyle: $indicatorAnimateStyle
+          })
+        ) : (
+          <Animated.View
+            style={[
+              $indicator,
+              $indicatorAnimateStyle,
+              { bottom: indicatorBottomOffset }
+            ]}
+          >
+            <View style={$indicatorPointer} />
+          </Animated.View>
+        )}
+        <View style={{ flexDirection: 'row', gap: tabGap }}>
+          {items.map((tab, idx) => (
+            <Pressable
+              key={tab.title}
+              onPress={() => {
+                $activeIndex.value = idx;
+                onPressTab(idx, tab?.key);
+              }}
+            >
+              <TabItem
+                itemStyle={itemStyle}
+                tab={tab}
+                idx={idx}
+                $activeIndex={$activeIndex}
+                animatedActive={animatedActive}
+                renderContent={renderTabContent}
+              />
+            </Pressable>
+          ))}
+        </View>
+        {renderOthers?.()}
+      </View>
+    </View>
+  );
+}
+
+interface TabsItemProps<T extends Tab> {
+  tab: T;
+  idx: number;
+  $activeIndex: SharedValue<number>;
+  itemStyle?: ViewStyle;
+  animatedActive?: boolean;
+  renderContent: (tab: T, idx: number, textStyle: TextStyle) => ReactElement;
+}
+
+function TabItem<T extends Tab>({
+  tab,
+  idx,
+  $activeIndex,
+  itemStyle: itemStyleOverride = {},
+  animatedActive = false,
+  renderContent
+}: TabsItemProps<T>) {
+  // 在UI线程计算透明度动画
+  const $animationStyle = useAnimatedStyle(() => {
+    'worklet';
+    const isActive = $activeIndex.value === idx;
+    return {
+      opacity: withTiming(isActive ? 1 : 0.4, { duration: 200 })
+    };
+  });
+
+  // 在UI线程计算字体样式，同时添加透明度过渡
+  const $animationTextStyle = useAnimatedStyle(() => {
+    'worklet';
+    const isActive = $activeIndex.value === idx;
+    return {
+      fontWeight: isActive ? '600' : '500',
+      color: withTiming(
+        isActive
+          ? StyleSheet.darkColors.white[1000]
+          : StyleSheet.darkColors.white[400],
+        { duration: 200 }
+      ),
+      opacity: withTiming(isActive ? 1 : 0.8, { duration: 200 })
+    };
+  });
+
+  return (
+    <Animated.View
+      style={[
+        $tab,
+        itemStyleOverride,
+        animatedActive ? $animationStyle : {},
+        { width: tab.width }
+      ]}
+    >
+      {renderContent(tab, idx, $animationTextStyle)}
+    </Animated.View>
+  );
+}
+
+const $tabBar: ViewStyle = {
+  ...$flexRow,
+  ...$flexCenter,
+  paddingHorizontal: 8,
+  flexGrow: 0,
+  height: 44
+};
+
+const $tab: ViewStyle = {
+  ...$flexRow,
+  ...$flexCenter
+};
+
+const $tabText: TextStyle = {
+  fontSize: 16,
+  color: StyleSheet.darkColors.white[1000],
+  textAlign: 'center'
+};
+
+const $activeTabText: TextStyle = {
+  color: StyleSheet.darkColors.white[1000],
+  fontWeight: '600'
+};
+
+const $inactiveTabText: TextStyle = {
+  color: StyleSheet.darkColors.white[400],
+  fontWeight: '500'
+};
+
+const $indicator: ViewStyle = {
+  position: 'absolute',
+  alignItems: 'center',
+  zIndex: $Z_INDEXES.zm1
+};
+
+const $indicatorPointer: ViewStyle = {
+  width: 12,
+  height: 5,
+  backgroundColor: StyleSheet.colors.themeGround,
+  borderRadius: 4
+};



## File: 'src/components/tabs/index.tsx'

--- 
+++ 


@@ -32,17 +32,18 @@ 
   // 手动渲染Tab节点
   renderItem?: (
     props: {
       item: T;
       isActive: boolean;
       textStyle: TextStyle[];
     } & Pick<TabsProps<T>, 'current' | 'animatedTabIndex'>
   ) => React.ReactElement | string;
   animatedActive?: boolean;
   tabBarStyle?: ViewStyle; // 最外层的bar的样式
   itemStyle?: ViewStyle; //item的样式
   itemTextStyle?: TextStyle; // 文字样式
   tabGap?: number; // tab间的间隙
+  indicatorBottomOffset?: number; // 指示器与文字的间距
   // 手动渲染indicator(必需把animatedStyle和tabWidth赋值给最外层的元素)
   renderIndicator?: (props: {
     animatedStyle: AnimatedStyleProp<ViewStyle>;
     tabWidth?: number;


@@ -53,18 +54,19 @@ 
 export function Tabs<T extends Tab>({
   items,
   onPressTab,
   renderItem,
   current = 0,
   animatedTabIndex,
   tabBarStyle: tabBarStyleOverride = {},
   itemTextStyle: itemTextStyleOverride = {},
   itemStyle,
   tabWidth = 40,
   tabGap = 24,
   animatedActive,
   renderIndicator,
-  renderOthers
+  renderOthers,
+  indicatorBottomOffset = -4
 }: TabsProps<T>) {
   const validIndex =
     typeof current === 'number'
       ? current


@@ -117,18 +119,22 @@ 
   );
 
   return (
     <View style={[$tabBar, tabBarStyleOverride]}>
       {/* 左侧 Tab 列表 */}
       <View style={{ position: 'relative' }}>
         {renderIndicator ? (
           renderIndicator({
             animatedStyle: $indicatorAnimateStyle,
             tabWidth
           })
         ) : (
           <Animated.View
-            style={[$indicator, $indicatorAnimateStyle, { width: tabWidth }]}
+            style={[
+              $indicator,
+              $indicatorAnimateStyle,
+              { width: tabWidth, bottom: indicatorBottomOffset }
+            ]}
           >
             <View style={$indicatorPointer} />
           </Animated.View>
         )}


@@ -268,18 +274,17 @@ 
 
 const $activeTabText: TextStyle = {
   color: StyleSheet.darkColors.white[1000],
   fontWeight: '600'
 };
 
 const $inactiveTabText: TextStyle = {
   color: StyleSheet.darkColors.white[400],
   fontWeight: '500'
 };
 
 const $indicator: ViewStyle = {
   position: 'absolute',
-  bottom: -4,
   alignItems: 'center',
   zIndex: $Z_INDEXES.zm1
 };



## File: 'src/components/tabs/simple-tabs.tsx'

--- 
+++ 


@@ -32,17 +32,18 @@ 
   // 手动渲染Tab节点
   renderItem?: (
     props: {
       item: T;
       isActive: boolean;
       textStyle: TextStyle[];
     } & Pick<TabsProps<T>, 'current'>
   ) => React.ReactElement | string;
   animatedActive?: boolean;
   tabBarStyle?: ViewStyle; // 最外层的bar的样式
   itemStyle?: ViewStyle; //item的样式
   itemTextStyle?: TextStyle; // 文字样式
   tabGap?: number; // tab间的间隙
+  indicatorBottomOffset?: number; // 指示器与文字的间距
   // 手动渲染indicator(必需把animatedStyle和tabWidth赋值给最外层的元素)
   renderIndicator?: (props: {
     tabWidth?: number;
     visibleStyle: ViewStyle;


@@ -53,18 +54,19 @@ 
 export function SimpleTabs<T extends Tab>({
   items,
   onPressTab,
   renderItem,
   current = 0,
   // animatedTabIndex,
   tabBarStyle: tabBarStyleOverride = {},
   itemTextStyle: itemTextStyleOverride = {},
   itemStyle,
   tabWidth = 40,
   tabGap = 24,
   animatedActive,
   renderIndicator,
-  renderOthers
+  renderOthers,
+  indicatorBottomOffset = -4
 }: TabsProps<T>) {
   const validIndex =
     typeof current === 'number'
       ? current


@@ -105,18 +107,24 @@ 
         <Animated.Text numberOfLines={1} style={[...textStyle, $textStyle]}>
           {content || tab.title}
         </Animated.Text>
       );
     }
   );
   const _renderIndicator = visibleStyle => {
     return renderIndicator ? (
       renderIndicator({
         tabWidth,
         visibleStyle
       })
     ) : (
-      <Animated.View style={[$indicator, { width: tabWidth }, visibleStyle]}>
+      <Animated.View
+        style={[
+          $indicator,
+          { width: tabWidth, bottom: indicatorBottomOffset },
+          visibleStyle
+        ]}
+      >
         <View style={$indicatorPointer} />
       </Animated.View>
     );
   };


@@ -133,17 +141,18 @@ 
                 $activeIndex.value = idx;
                 onPressTab(idx, tab?.key);
               }}
             >
               <TabItem
                 itemStyle={itemStyle}
                 tab={tab}
                 idx={idx}
                 $activeIndex={$activeIndex}
                 tabWidth={tabWidth}
                 animatedActive={animatedActive}
                 renderContent={renderContent}
                 renderIndicator={_renderIndicator}
+                indicatorBottomOffset={indicatorBottomOffset}
               />
             </Pressable>
           ))}
         </View>


@@ -152,17 +161,18 @@ 
     </View>
   );
 }
 
 export interface TabsItemProps<T extends Tab> {
   tab: T;
   idx: number;
   $activeIndex: SharedValue<number>;
   itemStyle?: ViewStyle; //item的样式
   tabWidth: number; // tab的宽度(必传，用于计算)
   animatedActive?: boolean;
   renderContent: (tab: T, idx: number, textStyle: TextStyle) => ReactElement;
   renderIndicator?: (visibleStyle: ViewStyle) => React.ReactElement | undefined;
+  indicatorBottomOffset?: number;
 }
 
 function TabItem<T extends Tab>({
   tab,



## File: 'src/components/waterfall/WaterFall2.tsx'

--- 
+++ 


@@ -155,17 +155,21 @@ 
               // for 自定义 report key
               reportKey: 'templatecard',
               card_order: indexNum,
               gameType,
               cardType,
               templateId: extInfo?.templateId,
               templateName: extInfo?.templateName,
               ...reportParams,
               ...extReportParams,
               ...dynamicReportParams
             };
           }
 
+          if (!data || data.length === 0) {
+            return {};
+          }
+
           if (!data?.[indexNum]?.card) {
             errorReport(
               `CardExpoReportMissingId_${pagePathRef.current}`,
               ReportError.NOT_FOUND,


@@ -479,18 +483,19 @@ 
       layoutInfo?: { x: number; y: number }
     ) {
       const isLeft = layoutInfo?.x === 0;
       // key会导致view重新创建，影响回收机制，但是去除key会导致卡片节点复用，状态还没来得及更新，出现重复卡片的问题。
       const key = data.card?.id || `${data.card?.type}_${index}`;
 
       return (
         <View
           // key={key}
           style={[
             {
               width: '100%',
               height: '100%',
-              paddingBottom: 5
+              paddingBottom: 5,
+              paddingTop: 5
             },
             isLeft
               ? {
                   paddingLeft: 5,



## File: 'src/components/waterfall/useWaterfallGesture.tsx'

--- 
+++ 


@@ -1,15 +1,18 @@ 
 import { useRef } from 'react';
 import {
   NativeScrollEvent,
   NativeSyntheticEvent,
   Platform,
   ScrollView
 } from 'react-native';
 import { SharedValue } from 'react-native-reanimated';
 import { usePersistFn } from '@/src/hooks';
 import { InfiniteListRef } from '../infiniteList/typing';
 
+/**
+ * @deprecated 已废弃
+ */
 export const useWaterfallGesture = (params: {
   threshold?: number;
   updateUnlockTop?: (status: boolean) => void;
   active?: boolean;



## File: 'src/store/asyncMessage.tsx'

--- 
+++ 


@@ -1,11 +1,11 @@ 
 /**
     处理长连接消息
  */
 import { router } from 'expo-router';
 import { create } from 'zustand';
 import { Socket } from '@/src/api/websocket';
-import { PageTab } from '@/src/bizComponents/userScreen/types';
+import { UserPageTab } from '@/src/bizComponents/userScreen/constants';
 import { showMessage } from '@/src/components/v2/systemMessage';
 import { TabItemType } from '@/src/types';
 import { reportClick } from '@/src/utils/report';
 import { Theme } from '../theme/colors/type';


@@ -87,18 +87,18 @@ 
                 padding: dp2px(5),
                 paddingRight: dp2px(16)
               },
               onClick: ({ onClose, onNavigate }) => {
                 reportClick('content_check', {
                   module: 'videofeed',
                   contentid: cardId,
                   contenttype: 2
                 });
                 onClose?.();
                 const { go2HomePage } = onNavigate;
                 go2HomePage({
                   tab: TabItemType.PROFILE,
-                  pageTab: PageTab.SECRET,
+                  pageTab: UserPageTab.SECRET,
                   refresh: true
                 });
               }
             });



## File: 'src/store/font.ts'

--- 
+++ 


@@ -13,17 +13,19 @@ 
   | 'YeZiGongChangAoYeHei'
   | 'HanziPenSC'
   | 'HanziPenSCBlod'
   | 'LipuIP'
   | 'LipuWishCard'
   | 'LipuFeed'
   | 'LipuWorld'
   | 'AlibabaPuHuiTiMedium'
   | 'AlibabaPuHuiTiBold'
   | 'AlibabaPuHuiTiHeavy'
   | 'HanRoundedCNHeavy'
   | 'BarlowBlack'
   | 'BarlowBold'
+  | 'BarlowSemiBold'
+  | 'RanyMedium'
   | 'DouyinSansBold'
   | 'MuyaoSoftbrush';
 
 const fonts: Record<FontName, FileSource> = {


@@ -75,17 +77,25 @@ 
   }, // 积分用，可异步
   HanRoundedCNHeavy: {
     url: 'https://resource.lipuhome.com/font/ResourceHanRoundedCN-Heavy.ttf',
     md5: '578d8c14adea0859ac730b8f13b86483'
   },
   BarlowBlack: {
     url: 'https://resource.lipuhome.[BASE64_DATA:64chars].ttf',
     md5: 'cf65db28f1236aab6108e92fe5d01e66'
   },
   BarlowBold: {
     url: 'https://resource.lipuhome.[BASE64_DATA:64chars].ttf',
     md5: '3e5e203ff456c318de37f41ff3317d83'
   },
+  BarlowSemiBold: {
+    url: 'https://resource.lipuhome.[BASE64_DATA:64chars].ttf',
+    md5: '29527ab52af2334e2bcb6290c8692f70'
+  },
+  RanyMedium: {
+    url: 'https://resource.lipuhome.[BASE64_DATA:64chars].otf',
+    md5: '8a96a720c5d6678c04b61c1e56c2054d'
+  },
   DouyinSansBold: {
     url: 'https://resource.lipuhome.[BASE64_DATA:64chars].ttf',
     md5: '6068c4698b30572ac0d208464cc8d03a'
   },



## File: 'src/store/personalCenter.ts'

--- 
+++ 


@@ -1,13 +1,0 @@ 
-import { create } from 'zustand';
-import { PageTab } from '../bizComponents/userScreen/types';
-import { immer } from 'zustand/middleware/immer';
 
-interface PersonalCenterStore {
-  // 个人中心
-}
-
-export const usePersonalCenterStore = create<PersonalCenterStore>()(
-  immer((set, get) => ({
-    //
-  }))
-);



## File: 'src/store/userInfo.ts'

--- 
+++ 


@@ -11,36 +11,36 @@ 
 import { showToast } from '../components';
 import { UserProfile, UserSocialStat } from '../types';
 import { ReportError, errorReport } from '../utils/error-log';
 import { userPerformanceCollector } from '../utils/report/userPageCollector';
 import { GetPlaceRsp } from '@/proto-registry/src/web/raccoon/goods/goods_pb';
 
 // 最多只允许缓存 50条UserInfo在内存中,用最简单的数据结构，但牺牲速度
 const MAX_SIZE = 50;
 // 控制同uid的获取结果限制
 const loadingStates: Record<string, NodeJS.Timeout> = {};
 const MIN_FETCH_DURATION = 2000;
 
 //
-export type UserInfo = {
+export interface UserInfo {
   uid: string;
   profile: UserProfile;
   stat: UserSocialStat;
   goodsWallRes: PartialMessage<GetPlaceRsp>;
-};
+}
 
-type States = {
+interface States {
   __users: UserInfo[]; // 不要直接依赖 __users!!，用getUserInfo获取
-};
+}
 
-type Actions = {
+interface Actions {
   // 同步UserInfo，支持传入预加载的profile
   syncUserInfo: (uid: string, preloadProfile?: UserProfile) => void;
   // 获取本地的
   getUserInfo: (uid: string) => UserInfo | undefined;
   // update stat
   updateStat: (uid: string, stat: Partial<UserSocialStat>) => void;
-};
+}
 
 export const useUserInfoStore = create<States & Actions>()((set, get) => {
   // 将uid 推到最前面
   function add(user: UserInfo) {



## File: 'src/theme/tokens/colors/base/dark.ts'

--- 
+++ 


@@ -1,16 +1,19 @@ 
 export const darkColors = {
   gray: {
     22: 'rgb(22, 22, 26)',
     29: 'rgb(29, 29, 33)',
     alpha29: '#1D1C21D5',
     35: 'rgb(35, 35, 41)',
     38: 'rgb(38, 38, 41)',
     42: 'rgb(42, 42, 42)',
     45: 'rgb(45, 45, 51)',
     54: 'rgba(54, 54, 61)',
     62: 'rgb(62, 62, 66)',
     244: 'rgba(244, 244, 244, 0.1)'
+  },
+  black: {
+    600: 'rgba(0, 0, 0, 0.6)'
   },
   white: {
     40: 'rgba(255, 255, 255, 0.04)',
     50: 'rgba(255, 255, 255, 0.05)',



## File: 'src/theme/tokens/colors/variants/dark.ts'

--- 
+++ 


@@ -10,18 +10,19 @@ 
     input: darkColors.gray[35],
     toast: darkColors.gray[62],
     card: darkColors.gray[38],
     popup: darkColors.gray[42],
     skeleton: darkColors.gray[45],
     skeleton40: darkColors.white[40],
     transparent: darkColors.white[50],
     list: darkColors.gray[38],
     share: darkColors.white[80],
     shareModal: darkColors.white[100],
     sheetModal: darkColors.gray[42],
     tag: darkColors.white[60],
     bottomBar: darkColors.gray[29],
-    bottomBarBlur: darkColors.gray.alpha29
+    bottomBarBlur: darkColors.gray.alpha29,
+    transTip: darkColors.black[600]
   },
   text: {
     solid: darkColors.white[1000],
     primary: darkColors.white[900],


@@ -27,18 +28,19 @@ 
     primary: darkColors.white[900],
     secondary: darkColors.white[600],
     tertiary: darkColors.white[500],
     disabled: darkColors.white[400],
     placeholder: darkColors.white[300],
     selection: {
       background: opacityColor(darkColors.orange.primary, 0.3)
     },
     tag: darkColors.orange.tag,
     shareButton: darkColors.white[870]
   },
   border: {
     alpha: darkColors.white[80],
-    button: darkColors.white[100]
+    button: darkColors.white[100],
+    avatar: darkColors.white[200]
   },
   tag: {
     bannerTopicBg: darkColors.white[60],
     bannerTopicText: darkColors.white[600],



## File: 'src/theme/typography.ts'

--- 
+++ 


@@ -41,18 +41,22 @@ 
   SourceHanSerif: {
     bold: 'SourceHanSerifBold'
   },
   YeZiGongChangAoYeHei: {
     normal: 'YeZiGongChangAoYeHei'
   },
   HanziPenSC: {
     normal: 'HanziPenSC',
     bold: 'HanziPenSCBlod'
   },
   HanRoundedCNHeavy: 'HanRoundedCNHeavy',
   Barlow: {
     BlackItalic: 'BarlowBlack',
-    BoldItalic: 'BarlowBold'
+    BoldItalic: 'BarlowBold',
+    SemiBold: 'BarlowSemiBold'
+  },
+  Rany: {
+    Medium: 'RanyMedium'
   },
   DouyinSans: {
     bold: 'DouyinSansBold'
   },



## File: 'src/utils/event.ts'

--- 
+++ 


@@ -43,18 +43,19 @@ 
   }
 }
 
 // 添加事件名时需要在这里添加类型，方便管理。
 export type TCOMMON_EVENT_KEYS =
   | 'navigationRequestEvent'
   | 'tabBarPressed'
   | 'tabBarPressedWhenFocus'
   | 'asyncCardPublished'
   | 'expandGameEntry'
   | 'forceRefreshRecommend'
   | 'handleUserScreenBack'
   | 'resetUserScreenTimer'
-  | 'videoTabSetPager';
+  | 'videoTabSetPager'
+  | 'goodsMaskLoaded';
 
 const CommonEventBus = new Event<TCOMMON_EVENT_KEYS>();
 
 export { CommonEventBus };



## File: 'assets/image/goods-shef/goods_bg.webp'

IMAGE file (binary) modified: assets/image/goods-shef/goods_bg.webp



## File: 'assets/lottie/greenlight.json'

--- 
+++ 


@@ -1,1 +1,1 @@ 
-    [BASE64_IMAGE_DATA_REMOVED]
+


// Lottie file summary: 3 lines, 1 base64 images, 0 assets, 0 animation layers
     [BASE64_IMAGE_DATA_REMOVED]



## File: 'assets/lottie/redlight.json'

@@ -1,1 +1,0 @@ 
-    [BASE64_IMAGE_DATA_REMOVED]
\ No newline at end of file


// Lottie file summary: 3 lines, 1 base64 images, 0 assets, 0 animation layers
     [BASE64_IMAGE_DATA_REMOVED]



## File: 'eslint.config.mjs'

--- 
+++ 


@@ -56,17 +56,23 @@ 
       'react/display-name': 'off',
 
       '@typescript-eslint/no-unused-vars': [
         'warn',
         {
           argsIgnorePattern: '^_',
           varsIgnorePattern: '^_',
           caughtErrorsIgnorePattern: '^_'
         }
       ],
 
       '@typescript-eslint/no-var-requires': 'off',
       '@typescript-eslint/require-await': 'warn',
+      '@typescript-eslint/no-require-imports': [
+        'warn',
+        {
+          allow: ['^@Assets/.*']
+        }
+      ],
 
       'no-restricted-imports': [
         'error',
         {


@@ -93,19 +99,19 @@ 
               message:
                 '请使用 react-native 替代 react-native-gesture-handler 的 TouchableOpacity，以避免 overflow 相关问题'
             }
           ]
         }
       ],
       'react/forbid-component-props': [
         'error',
         {
           forbid: ['exiting']
         }
       ],
       'eslint-comments/no-unused-disable': 'warn',
-      '@typescript-eslint/no-require-imports': 'warn',
-      'react/jsx-no-bind': 'warn',
+      'react/jsx-uses-react': 'error',
+      'react/jsx-no-bind': ['warn', { allowArrowFunctions: true }],
       'react/jsx-no-constructed-context-values': 'warn',
       'react/jsx-handler-names': 'warn',
       'react/self-closing-comp': [
         'warn',


@@ -108,12 +114,13 @@ 
       'react/jsx-no-constructed-context-values': 'warn',
       'react/jsx-handler-names': 'warn',
       'react/self-closing-comp': [
         'warn',
         {
           component: true,
           html: true
         }
-      ]
+      ],
+      '@typescript-eslint/consistent-type-definitions': 'off'
     }
   }
 );



## File: 'proto-gen.lock.json'

--- 
+++ 


@@ -1,14 +1,14 @@ 
 {
   "repos": {
     "passport": {
-      "commit": "42ac43100e6158410076d42cc35a261deffc3978"
+      "commit": "b97f943b0582b78d880db52358eda895776a233b"
     },
     "step-bridge": {
       "commit": "23d74262f05e9ecc75a94905bdae5381c998d9f3"
     },
     "raccoon": {
-      "commit": "42ac43100e6158410076d42cc35a261deffc3978"
+      "commit": "b97f943b0582b78d880db52358eda895776a233b"
     },
     "trpc-infra": {
       "commit": "77fc3539f5d5f285f5a89ba2f20bfebef60c2561"
     }
=====

Note that lines in the diff body are prefixed with a symbol that represents the type of change: '-' for deletions, '+' for additions, and ' ' (a space) for unchanged lines.


Response (should be a valid YAML, and nothing else):
```yaml
```

## Prompt Statistics
- System Prompt Length: 5101 characters
- User Prompt Length: 218529 characters
- Total Length: 223630 characters
- System Prompt Lines: 79
- User Prompt Lines: 8057
