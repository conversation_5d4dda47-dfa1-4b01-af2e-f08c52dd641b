### **PR Type**
Enhancement, Bug fix


___

### **Description**
- **个人中心页面重构**: 整个用户个人中心页面 (`UserScreen`) 进行了彻底的重构和视觉更新，采用了新的组件化架构，包括 `UserHeader`、`UserPanel` 和 `GoodsWallBg` 等。

- **全新的交互体验**: 引入了下拉手势交互。向下拉动可以触发内容刷新，继续下拉则会进入“痛墙”页面。iOS 设备上增加了触感反馈（震动）。

- **组件化和代码优化**: 移除了旧的 `infoProfile` 和 `infoSection` 组件，拆分为 `UserInfo`、`UserStats`、`MineButtons` 等多个更小、更易于维护的组件。

- **Tab 功能更新**:
  - 为“我的”页面新增了“我的角色”标签页。
  - “待发布”标签页现在会显示待发布内容的数量。
  - “图集”功能从标签页调整为一个独立的入口按钮。

- **API 和数据流变更**: 新增了 `getUserShowAsyncCardCnt` 接口用于获取待发布内容的数量，并优化了相关数据加载逻辑。

- **QA 测试重点**:
  - **UI 和布局**: 在不同尺寸的设备上验证新版个人中心页面的布局、元素对齐和响应式表现。
  - **下拉手势**: 重点测试下拉刷新和进入“痛墙”的阈值、动画效果、以及 iOS 上的震动反馈。测试边界情况，如快速下拉、慢速下拉。
  - **标签页功能**: 验证“作品”、“赞过”、“待发布”、“我的角色”等所有标签页的数据加载、滚动、空状态和刷新功能是否正常。
  - **交互元素**: 测试所有按钮的点击功能，包括返回、编辑资料、设置、关注/取关、进入“痛墙”、图集等。
  - **数据准确性**: 检查用户统计数据（关注、粉丝、获赞、声望）和“待发布”数量是否正确显示。


___

### **Changes diagram**

```mermaid
flowchart LR
    subgraph UserScreen [用户个人中心]
        direction TB
        A[UserHeader] -- "滚动时显示" --> B(NestedScrollView)
        B -- "包含" --> C{UserPanel}
        B -- "包含" --> D[PagerView]
        C -- "包含" --> E[UserInfo]
        C -- "包含" --> F[UserStats]
        C -- "包含" --> G[DynamicWidthTabs]
    end

    subgraph Interactions [交互]
        direction LR
        H(下拉手势) -- "达到刷新阈值" --> I(刷新列表)
        H -- "达到痛墙阈值" --> J(进入痛墙)
    end

    UserScreen -- "通过" --> H
```


___



### **Changes walkthrough** 📝
<table><thead><tr><th></th><th align="left">Relevant files</th></tr></thead><tbody><tr><td><strong>Enhancement</strong></td><td><details><summary>12 files</summary><table>
<tr>
  <td><strong>src/bizComponents/userScreen/index.tsx</strong><dd><code>重构个人中心主页面，采用新的滚动和组件架构，并集成下拉手势。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/index.tsx?ref_type=heads">+384/-683</a></td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/components/UserPanel.tsx</strong><dd><code>新增核心面板组件，渲染用户信息、统计数据和标签页，并支持动态高度。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/components/UserPanel.tsx?ref_type=heads">+334/-0</a>&nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/components/UserHeader.tsx</strong><dd><code>新增吸顶导航栏组件，在页面滚动时显示用户信息和快捷操作。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/components/UserHeader.tsx?ref_type=heads">+222/-0</a>&nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/components/GoodsTopTip.tsx</strong><dd><code>新增顶部提示组件，管理“下拉刷新”和“下拉进入痛墙”的动画提示。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/components/GoodsTopTip.tsx?ref_type=heads">+384/-0</a>&nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/components/GoodsWallBg.tsx</strong><dd><code>新增“痛墙”背景组件，用于展示用户个人中心的自定义背景。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/components/GoodsWallBg.tsx?ref_type=heads">+109/-0</a>&nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/components/myRoleList/index.tsx</strong><dd><code>新增“我的角色”列表组件，用于展示用户创建和收藏的角色。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/components/myRoleList/index.tsx?ref_type=heads">+289/-0</a>&nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/nestedScrollView/hooks/usePullDownGestureHandlers.tsx</strong><dd><code>新增下拉手势处理钩子，封装下拉进度跟踪和回调的核心逻辑。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/nestedScrollView/hooks/usePullDownGestureHandlers.tsx?ref_type=heads">+188/-0</a>&nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/constants.ts</strong><dd><code>新增常量文件，用于定义个人中心页面的标签页配置。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/constants.ts?ref_type=heads">+47/-0</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/utils.ts</strong><dd><code>更新工具函数，新增面板高度计算和待发布内容数量获取逻辑。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/utils.ts?ref_type=heads">+73/-0</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/api/asynccard/index.ts</strong><dd><code>新增 `getUserShowAsyncCardCnt` 接口调用，以获取用户待发布内容数量。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/api/asynccard/index.ts?ref_type=heads">+7/-0</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/tabs/dynamic-tabs.tsx</strong><dd><code>新增支持动态宽度的标签页组件，以适应不同长度的标签标题。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/components/tabs/dynamic-tabs.tsx?ref_type=heads">+355/-0</a>&nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/avatar/index.tsx</strong><dd><code>优化 Avatar 组件，增加新尺寸并添加外边框属性以支持新设计。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/components/avatar/index.tsx?ref_type=heads">+29/-10</a>&nbsp; </td>

</tr>
</table></details></td></tr><tr><td><strong>Refactoring</strong></td><td><details><summary>2 files</summary><table>
<tr>
  <td><strong>src/bizComponents/userScreen/infoProfile/index.tsx</strong><dd><code>删除旧的 InfoProfile 组件，其功能已被新的 `UserHeader` 和 `UserPanel` 替代。</code></dd></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/infoProfile/index.tsx?ref_type=heads">+0/-499</a>&nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/infoSection/index.tsx</strong><dd><code>删除旧的 InfoSection 组件，其功能已被新的 `UserPanel` 子组件取代。</code>&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; </dd></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/infoSection/index.tsx?ref_type=heads">+0/-552</a>&nbsp; </td>

</tr>
</table></details></td></tr><tr><td><strong>Additional files</strong></td><td><details><summary>50 files</summary><table>
<tr>
  <td><strong>app/bbs-feed/index.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/app/bbs-feed/index.tsx?ref_type=heads">+1/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>app/feed/index.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/app/feed/index.tsx?ref_type=heads">+2/-3</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>app/follow-fan/index.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/app/follow-fan/index.tsx?ref_type=heads">+10/-4</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>app/magic-video-history/history-item/index.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/app/magic-video-history/history-item/index.tsx?ref_type=heads">+3/-3</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>assets/image/goods-shef/goods_bg.webp</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/assets/image/goods-shef/goods_bg.webp?ref_type=heads">[link]</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>assets/lottie/greenlight.json</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/assets/lottie/greenlight.json?ref_type=heads">+2/-2</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>assets/lottie/redlight.json</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/assets/lottie/redlight.json?ref_type=heads">+0/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/api/goods/index.ts</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/api/goods/index.ts?ref_type=heads">+1/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/feedScreen/discussPanels/discussPanel.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/feedScreen/discussPanels/discussPanel.tsx?ref_type=heads">+2/-2</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/feedScreen/rolePanels/myRole.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/feedScreen/rolePanels/myRole.tsx?ref_type=heads">+19/-3</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/feedScreen/DiscussFeed.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/feedScreen/DiscussFeed.tsx?ref_type=heads">+2/-26</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/feedScreen/index.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/feedScreen/index.tsx?ref_type=heads">+1/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/feedScreen/type.ts</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/feedScreen/type.ts?ref_type=heads">+36/-0</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/topicScreen/components/topicRankBanner/components/TopicScrollingRow.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/topicScreen/components/topicRankBanner/components/TopicScrollingRow.tsx?ref_type=heads">+1/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/trendingScreen/ImmersivePost.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/trendingScreen/ImmersivePost.tsx?ref_type=heads">+1/-2</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/trendingScreen/index.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/trendingScreen/index.tsx?ref_type=heads">+2/-2</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/components/baseFlowList/index.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/components/baseFlowList/index.tsx?ref_type=heads">+46/-57</a>&nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/components/likeFlowList/index.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/components/likeFlowList/index.tsx?ref_type=heads">+14/-26</a>&nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/components/secretFlowList/index.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/components/secretFlowList/index.tsx?ref_type=heads">+65/-40</a>&nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/components/workFlowList/index.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/components/workFlowList/index.tsx?ref_type=heads">+14/-26</a>&nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/components/GoodsButton.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/components/GoodsButton.tsx?ref_type=heads">+47/-0</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/components/MineButtons.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/components/MineButtons.tsx?ref_type=heads">+99/-0</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/components/MineCards.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/components/MineCards.tsx?ref_type=heads">+168/-0</a>&nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/components/UserInfo.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/components/UserInfo.tsx?ref_type=heads">+200/-0</a>&nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/components/UserStats.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/components/UserStats.tsx?ref_type=heads">+150/-0</a>&nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/hooks/useHandleSwipeBack.ts</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/hooks/useHandleSwipeBack.ts?ref_type=heads">+32/-0</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/hooks/useRetentionPopup.ts</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/hooks/useRetentionPopup.ts?ref_type=heads">[link]</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/retentionPopup/index.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/retentionPopup/index.tsx?ref_type=heads">+2/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/types.ts</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/types.ts?ref_type=heads">+2/-13</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/userScreen/useAnimation.ts</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/userScreen/useAnimation.ts?ref_type=heads">+0/-132</a>&nbsp; </td>

</tr>

<tr>
  <td><strong>src/bizComponents/videoMagic/button/generateButton.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/bizComponents/videoMagic/button/generateButton.tsx?ref_type=heads">+2/-2</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/follow/index.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/components/follow/index.tsx?ref_type=heads">+1/-0</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/icons/icon.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/components/icons/icon.tsx?ref_type=heads">+1/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/infiniteList/CustomNestedInnerScrollView.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/components/infiniteList/CustomNestedInnerScrollView.tsx?ref_type=heads">+1/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/infiniteList/typing.ts</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/components/infiniteList/typing.ts?ref_type=heads">+8/-6</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/skeletion/index.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/components/skeletion/index.tsx?ref_type=heads">+0/-3</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/tabs/index.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/components/tabs/index.tsx?ref_type=heads">+9/-4</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/tabs/simple-tabs.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/components/tabs/simple-tabs.tsx?ref_type=heads">+13/-3</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/waterfall/WaterFall2.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/components/waterfall/WaterFall2.tsx?ref_type=heads">+7/-2</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/components/waterfall/useWaterfallGesture.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/components/waterfall/useWaterfallGesture.tsx?ref_type=heads">+4/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/store/asyncMessage.tsx</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/store/asyncMessage.tsx?ref_type=heads">+3/-3</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/store/font.ts</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/store/font.ts?ref_type=heads">+11/-1</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/store/personalCenter.ts</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/store/personalCenter.ts?ref_type=heads">+1/-13</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/store/userInfo.ts</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/store/userInfo.ts?ref_type=heads">+7/-7</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/theme/tokens/colors/base/dark.ts</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/theme/tokens/colors/base/dark.ts?ref_type=heads">+4/-1</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/theme/tokens/colors/variants/dark.ts</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/theme/tokens/colors/variants/dark.ts?ref_type=heads">+5/-3</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/theme/typography.ts</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/theme/typography.ts?ref_type=heads">+6/-2</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>src/utils/event.ts</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/src/utils/event.ts?ref_type=heads">+3/-2</a>&nbsp; &nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>eslint.config.mjs</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/eslint.config.mjs?ref_type=heads">+11/-4</a>&nbsp; &nbsp; </td>

</tr>

<tr>
  <td><strong>proto-gen.lock.json</strong></td>
  <td><a href="https://gitlab.basemind.com/raccoon/lipu-mobile/-/blob/fuxiao/feat/userscreen-redesign/proto-gen.lock.json?ref_type=heads">+3/-3</a>&nbsp; &nbsp; &nbsp; </td>

</tr>
</table></details></td></tr></tr></tbody></table>

___

<details><summary>Need help?</summary>- Type <code>/help how to ...</code> in the comments thread for any questions about PR-Agent usage.<br>- Check out the <a href='https://qodo-merge-docs.qodo.ai/usage-guide/'>documentation</a> for more information.</details>