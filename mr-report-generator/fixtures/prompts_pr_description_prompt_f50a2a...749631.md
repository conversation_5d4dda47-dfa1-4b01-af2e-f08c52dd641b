# Complete Prompts Debug Export

## Metadata
- PR ID: f50a2a...749631
- Prompt Type: pr_description_prompt
- Timestamp: 2025-07-09T21:09:24.973445
- Model: unknown

## System Prompt
```
You are PR-Reviewer, a language model designed to review a Git Pull Request (PR).
Your task is to provide a full description for the PR content: type, description, title, and files walkthrough.
- Focus on the new PR code (lines starting with '+' in the 'PR Git Diff' section).
- Keep in mind that the 'Previous title', 'Previous description' and 'Commit messages' sections may be partial, simplistic, non-informative or out of date. Hence, compare them to the PR diff code, and use them only as a reference.
- The generated title and description should prioritize the most significant changes.
- If needed, each YAML output should be in block scalar indicator ('|')
- When quoting variables, names or file paths from the code, use backticks (`) instead of single quote (').
- When needed, use '- ' as bullets

FILE PATH REQUIREMENTS:
- Use the EXACT file paths as they appear in the Git Diff section
- ONLY include files that actually exist in the provided diff - do not invent file paths
- Verify each file path against the diff before including it in your response

QA-FOCUSED DESCRIPTION REQUIREMENTS:
- Write descriptions for QA engineers who need to understand testing scope and impact
- Include specific functionality changes, not just technical implementation details
- Highlight user-facing changes, new features, removed features, and potential breaking changes
- Mention cross-component dependencies and integration points that require testing
- Specify areas that need focused testing attention (UI changes, API changes, data flow changes)
- Include potential edge cases or scenarios that QA should validate
- Use clear, non-technical language when describing user-visible impacts

Extra instructions from the user:
=====
Your response MUST be written in the language corresponding to locale code: 'zh-CN'. This is crucial.
=====



The output must be a YAML object equivalent to type $PRDescription, according to the following Pydantic definitions:
=====
class PRType(str, Enum):
    bug_fix = "Bug fix"
    tests = "Tests"
    enhancement = "Enhancement"
    documentation = "Documentation"
    other = "Other"

class FileDescription(BaseModel):
    filename: str = Field(description="MANDATORY: The EXACT full relative file path as it appears in the PR Git Diff section (e.g., 'src/components/UserPanel.tsx', 'app/feed/index.tsx'). You MUST use the complete path from the diff, never just a filename. ONLY include files that actually exist in the provided diff - do not generate or assume any file paths.")
    changes_title: str = Field(description="Clear, descriptive summary (8-15 words) explaining what changed in this file and its impact on functionality. Focus on user-visible changes and testing implications.")
    label: str = Field(description="a single semantic label that represents a type of code changes that occurred in the File. Possible values (partial list): 'bug fix', 'tests', 'enhancement', 'documentation', 'error handling', 'configuration changes', 'dependencies', 'formatting', 'miscellaneous', ...")

class PRDescription(BaseModel):
    type: List[PRType] = Field(description="one or more types that describe the PR content. Return the label member value (e.g. 'Bug fix', not 'bug_fix')")
    description: str = Field(description="Provide a comprehensive summary of the PR changes for QA testing purposes. Include 4-8 detailed bullet points (each 12-20 words) explaining: what functionality changed, potential user impact, areas requiring testing focus, and any breaking changes or new features. For large PRs, add sub-bullets with specific implementation details. Order by testing priority and include cross-component dependencies.")
    title: str = Field(description="a concise and descriptive title that captures the PR's main theme")
    changes_diagram: str = Field(description="a horizontal diagram that represents the main PR changes, in the format of a valid mermaid LR flowchart. The diagram should be concise and easy to read. Leave empty if no diagram is relevant. To create robust Mermaid diagrams, follow this two-step process: (1) Declare the nodes: nodeID["node description"]. (2) Then define the links: nodeID1 -- "link text" --> nodeID2. Node description must always be surrounded with quotation marks.")
    pr_files: List[FileDescription] = Field(max_items=100, description="a list of significant files that were changed in the PR, and summary of their changes. EXCLUDE: binary files, generated files (package-lock.json, *.lock, dist/*, build/*), and files with only trivial changes (1-3 lines of whitespace/formatting). PRIORITIZE: source code files with substantial logic changes, new features, and files affecting user-facing functionality.")
=====


Example output:

```yaml
type:
- ...
- ...
description: |
  ...
title: |
  ...
  changes_diagram: |
    ```mermaid
    flowchart LR
      ...
    ```
pr_files:
- filename: |
    ...
  changes_title: |
    ...
  label: |
    label_key_1
...
```

Answer should be a valid YAML, and nothing else. Each YAML output MUST be after a newline, with proper indent, and block scalar indicator ('|')
```

## User Prompt
```


PR Info:

Previous title: '代码变更汇总: 50 个提交 (f50a2a...749631)'

Previous description:
=====
**提交范围对比**: f50a2a...749631

**包含的提交 (50 个):**
- `2109f65d` Merge branch 'fix/camera-auth' into 'release/20250513' - youchuyu (2025-05-14)
- `4896d07b` feat: 首页卡片和沉浸流低清晰度视频 - xiongluyang (2025-05-15)
- `cda2e098` fix: 玩法卡片视频低分辨率视频 - xiongluyang (2025-05-15)
- `c41e86cb` feat: 切换低分辨率视频添加网络判断 - xiongluyang (2025-05-15)
- `ad50c723` feat: 低分辨率视频字段 - xiongluyang (2025-06-04)
- `25a170ec` fix: 低分辩率字段问题 - xiongluyang (2025-06-04)
- `b7774aaa` feat: 添加低分辨率埋点 - xiongluyang (2025-06-05)
- `b9cbf91d` fix: 预加载低分辨率视频增加弱网条件 - xiongluyang (2025-06-05)
- `feac7733` Merge branch 'feat/low-resolution-video' into 'release/20250520' - xiongluyang (2025-06-05)
- `8b9b9c63` ai生成水印 - wandanni (2025-06-06)
- `2df19163` Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-06)
- `79897ee6` fix: android水印重叠，把ai水印移到screen组件里 - wandanni (2025-06-09)
- `e6c35bae` Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-09)
- `51167b80` fix: android水印重叠，Android键盘升起问题 - wandanni (2025-06-09)
- `8e73b23b` fix:android角色列表样式 - wandanni (2025-06-09)
- `d9f069a5` Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-09)
- `e00c5a77` fix:表情包入口底部重叠 - wandanni (2025-06-09)
- `fe990cb7` Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-09)
- `4036221c` fix:炖图问题 - wandanni (2025-06-09)
- `d8ec2262` Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-09)
- `cd943419` fix:炖图问题 - wandanni (2025-06-09)
- `38df6f79` Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-09)
- `75634c84` 炖图回退 - wandanni (2025-06-10)
- `29fe21ba` Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-10)
- `c79ba436` 炖图loading问题fix - wandanni (2025-06-10)
- `10aa0a47` Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-10)
- `04a57e03` 发布字数限制改成2000字 - wandanni (2025-06-23)
- `d323d5c7` Merge branch 'feat/danny_20250609' into 'release/20250623' - wandanni (2025-06-23)
- `5d4a1fe6` Merge branch 'release/20250520' into 'main' - xiongluyang (2025-06-25)
- `4fa0945d` fix: 修复微动拍图片同款sref报错 - xiongluyang (2025-06-25)
- `5ff93953` Merge branch 'fix/live-takesame-sref' into 'release/20250627' - xiongluyang (2025-06-26)
- `b5f5e395` feat: 添加一键做实物功能 - wandanni (2025-06-26)
- `4829a5c8` feat: 添加一键做实物功能 - wandanni (2025-06-26)
- `c3a8fa6e` Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-26)
- `6097f087` feat: 玩法页一键实物 - wandanni (2025-06-26)
- `bd4797e1` Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-26)
- `22ef1985` feat: 炖图预览页一键实物 - wandanni (2025-06-26)
- `855e0d08` Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-26)
- `a939edf9` feat: 详情页一键实物 - wandanni (2025-06-26)
- `42b35975` Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-26)
- `5b685d0e` feat: 一键实物埋点 - wandanni (2025-06-26)
- `ef76e2d4` feat: 一键实物埋点 - wandanni (2025-06-26)
- `9271c372` Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-26)
- `9483620e` feat: 去掉高校榜入口 - wandanni (2025-06-27)
- `d00de116` Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-27)
- `5a9306e5` feat: 去掉高校榜入口 - wandanni (2025-06-27)
- `97e3b3df` Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-27)
- `b5c2af75` fix: lint - wandanni (2025-06-27)
- `b8015fcd` Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-27)
- `74963122` Merge branch 'release/20250627' into 'main' - linyueqiang (2025-07-03)
=====

Branch: '749631'

Commit messages:
=====
["2109f65d: Merge branch 'fix/camera-auth' into 'release/20250513' - youchuyu (2025-05-14)", '4896d07b: feat: 首页卡片和沉浸流低清晰度视频 - xiongluyang (2025-05-15)', 'cda2e098: fix: 玩法卡片视频低分辨率视频 - xiongluyang (2025-05-15)', 'c41e86cb: feat: 切换低分辨率视频添加网络判断 - xiongluyang (2025-05-15)', 'ad50c723: feat: 低分辨率视频字段 - xiongluyang (2025-06-04)', '25a170ec: fix: 低分辩率字段问题 - xiongluyang (2025-06-04)', 'b7774aaa: feat: 添加低分辨率埋点 - xiongluyang (2025-06-05)', 'b9cbf91d: fix: 预加载低分辨率视频增加弱网条件 - xiongluyang (2025-06-05)', "feac7733: Merge branch 'feat/low-resolution-video' into 'release/20250520' - xiongluyang (2025-06-05)", '8b9b9c63: ai生成水印 - wandanni (2025-06-06)', "2df19163: Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-06)", '79897ee6: fix: android水印重叠，把ai水印移到screen组件里 - wandanni (2025-06-09)', "e6c35bae: Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-09)", '51167b80: fix: android水印重叠，Android键盘升起问题 - wandanni (2025-06-09)', '8e73b23b: fix:android角色列表样式 - wandanni (2025-06-09)', "d9f069a5: Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-09)", 'e00c5a77: fix:表情包入口底部重叠 - wandanni (2025-06-09)', "fe990cb7: Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-09)", '4036221c: fix:炖图问题 - wandanni (2025-06-09)', "d8ec2262: Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-09)", 'cd943419: fix:炖图问题 - wandanni (2025-06-09)', "38df6f79: Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-09)", '75634c84: 炖图回退 - wandanni (2025-06-10)', "29fe21ba: Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-10)", 'c79ba436: 炖图loading问题fix - wandanni (2025-06-10)', "10aa0a47: Merge branch 'feat/danny_20250609' into 'release/20250520' - wandanni (2025-06-10)", '04a57e03: 发布字数限制改成2000字 - wandanni (2025-06-23)', "d323d5c7: Merge branch 'feat/danny_20250609' into 'release/20250623' - wandanni (2025-06-23)", "5d4a1fe6: Merge branch 'release/20250520' into 'main' - xiongluyang (2025-06-25)", '4fa0945d: fix: 修复微动拍图片同款sref报错 - xiongluyang (2025-06-25)', "5ff93953: Merge branch 'fix/live-takesame-sref' into 'release/20250627' - xiongluyang (2025-06-26)", 'b5f5e395: feat: 添加一键做实物功能 - wandanni (2025-06-26)', '4829a5c8: feat: 添加一键做实物功能 - wandanni (2025-06-26)', "c3a8fa6e: Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-26)", '6097f087: feat: 玩法页一键实物 - wandanni (2025-06-26)', "bd4797e1: Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-26)", '22ef1985: feat: 炖图预览页一键实物 - wandanni (2025-06-26)', "855e0d08: Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-26)", 'a939edf9: feat: 详情页一键实物 - wandanni (2025-06-26)', "42b35975: Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-26)", '5b685d0e: feat: 一键实物埋点 - wandanni (2025-06-26)', 'ef76e2d4: feat: 一键实物埋点 - wandanni (2025-06-26)', "9271c372: Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-26)", '9483620e: feat: 去掉高校榜入口 - wandanni (2025-06-27)', "d00de116: Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-27)", '5a9306e5: feat: 去掉高校榜入口 - wandanni (2025-06-27)', "97e3b3df: Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-27)", 'b5c2af75: fix: lint - wandanni (2025-06-27)', "b8015fcd: Merge branch 'feat/danny_20250609' into 'release/20250627' - wandanni (2025-06-27)", "74963122: Merge branch 'release/20250627' into 'main' - linyueqiang (2025-07-03)"]
=====


The PR Git Diff:
=====
## File: 'app/bot/index.tsx'

@@ -1,8 +1,15 @@ 
-import { useLatest, useMemoizedFn, useMount, useThrottleFn, useUnmount } from 'ahooks';
+import {
+  useLatest,
+  useMemoizedFn,
+  useMount,
+  useThrottleFn,
+  useUnmount
+} from 'ahooks';
 import { useEffect, useMemo, useRef, useState } from 'react';
 import {
   Dimensions,
+  InteractionManager,
   NativeScrollEvent,
   NativeSyntheticEvent,
   Platform,
   ScrollView,


@@ -5,13 +12,12 @@ import {
   NativeScrollEvent,
   NativeSyntheticEvent,
   Platform,
   ScrollView,
   TextInput,
   TextInputKeyPressEventData,
   TouchableOpacity,
-  View,
-  InteractionManager
+  View
 } from 'react-native';
 import { ShadowedView } from 'react-native-fast-shadow';
 import { IOScrollView } from 'react-native-intersection-observer';
 import LinearGradient from 'react-native-linear-gradient';


@@ -17,13 +23,21 @@ 
 import LinearGradient from 'react-native-linear-gradient';
 import { Icon, hideLoading, showLoading } from '@/src/components';
 import { hideToast, showToast } from '@/src/components';
 import { Image, Text } from '@/src/components';
+import { AiTag } from '@/src/components/aiTag';
 import AudioPlayer, { clearSounds } from '@/src/components/audioPlayer';
-import { InputStatus, RenderFlatListMessagesMemo, useBot } from '@/src/components/bot';
+import {
+  InputStatus,
+  RenderFlatListMessagesMemo,
+  useBot
+} from '@/src/components/bot';
 import { AnimatedXiaoli } from '@/src/components/bot/components/animatedXiaoli';
 import { DeepThinkButton } from '@/src/components/bot/components/deepThinkButton';
-import { BotEvent, BotEventBus } from '@/src/components/bot/components/utils/eventBus';
+import {
+  BotEvent,
+  BotEventBus
+} from '@/src/components/bot/components/utils/eventBus';
 import { BotStatus } from '@/src/components/bot/hooks';
 import { PagePerformance } from '@/src/components/common/pagePerformance';
 import { useSafeBottomArea } from '@/src/hooks';
 import { useKeyboard } from '@/src/hooks';


@@ -33,15 +47,17 @@ 
 import { $flexRow } from '@/src/theme/variable';
 import { StyleSheet, dp2px } from '@/src/utils';
 import { reportExpo } from '@/src/utils/report';
 import { RecommendList } from '@Components/bot/components/recommend';
-import { hideConfirm, showConfirm } from '@Components/popup/confirmModalGlobal/Confirm';
+import {
+  hideConfirm,
+  showConfirm
+} from '@Components/popup/confirmModalGlobal/Confirm';
 import { Screen } from '@Components/screen';
 import { useParams } from '../../src/hooks/useParams';
 import { ChatMessageContent } from '@/proto-registry/src/web/raccoon/chatbot/chatbot_pb';
 import { useShallow } from 'zustand/react/shallow';
 
-
 const bg = require('@Assets/image/bot/bg.png');
 const deepseek = require('@Assets/image/bot/deepseek.png');
 
 const MAX_INPUT_LENGTH = 200;


@@ -478,12 +494,13 @@ export default function Bot() {
       />
     ));
   }, [loading, messages]);
 
   return (
     <PagePerformance pathname="bot/index">
       <Screen
         theme="dark"
+        withWaterMark
         preset="fixed"
         maskAreaShown={false}
         safeAreaEdges={['bottom', 'top']}
         wholePageStyle={{


@@ -519,13 +536,13 @@ export default function Bot() {
         <IOScrollView
           bounces={false}
           ref={scrollViewRef}
           contentContainerStyle={[style.$pageContainer]}
           indicatorStyle="black"
           showsVerticalScrollIndicator={true}
           keyboardShouldPersistTaps="handled"
           scrollEventThrottle={200}
-          onTouchStart={() => { 
+          onTouchStart={() => {
             inputRef.current?.blur();
           }}
           onContentSizeChange={(width, height) => {
             if (


@@ -597,14 +614,13 @@ export default function Bot() {
             zIndex: 0,
             position: 'absolute',
             left: 0,
             right: 0,
             bottom: 0
           }}
         />
 
-        { 
-          animatedXiaoLiVisible &&
+        {animatedXiaoLiVisible && (
           <>
             <AnimatedXiaoli
               // 为什么要写两个组件呢？ 为了防止布局抖动
               style={[!useDs && { display: 'none' }]}


@@ -627,13 +643,13 @@ export default function Bot() {
                 bottom: $safePaddingBottom + inputHeight - 12,
                 zIndex: 1
               }}
               onPress={() => {
                 setUseDs(!useDs);
               }}
             />
           </>
-        }
+        )}
       </Screen>
     </PagePerformance>
   );
 }


@@ -726,9 +742,9 @@ const style = StyleSheet.create({
     color: StyleSheet.darkColors.white[900]
   },
   toastText: {
     fontSize: 14,
     lineHeight: 19.6,
     fontWeight: '600',
     color: StyleSheet.darkColors.white[900]
   }
-});
\ No newline at end of file
+});



## File: 'app/make-photo/index.tsx'

@@ -31,17 +31,18 @@ 
 import { usePublishStore } from '@/src/store/publish';
 import { useResourceStore } from '@/src/store/resource';
 import { useRoleStore } from '@/src/store/role';
 import { darkTheme, fullStyle, typography } from '@/src/theme';
 import { $USE_FONT, $Z_INDEXES } from '@/src/theme/variable';
 import { GameType } from '@/src/types';
 import {
   addCommonReportParams,
   getPageName,
   reportClick,
   reportExpo,
   reportMakePhotoTrack
 } from '@/src/utils/report';
+import { setTimeoutWorklet } from '@/src/utils/worklet';
 import { Image } from '@Components/image';
 import { ElementSuffix, MakePhotoEvents } from '@Components/makePhoto/constant';
 import { LoadingView, LoadingViewRef } from '@Components/makePhoto/loadingView';
 import { Pannel } from '@Components/makePhoto/pannel';


@@ -225,12 +226,13 @@ function MakePhoto() {
   }, [pageState, currentSlot]);
   useEffect(() => {
     if (inputVisible) {
       setOpenKeyboard(true);
     }
   }, [inputVisible]);
 
   const onBack = usePersistFn(() => {
+    console.log('onBack----');
     const { pageState, changePageState } = useMakePhotoStoreV2.getState();
     const { editPageState } = useMakePhotoEdit.getState();
     if (
       pageState === PageState.preview ||


@@ -245,13 +247,15 @@ function MakePhoto() {
           backFromText: true
         });
         useMakePhotoEdit.getState().resetGroupTexts();
         return;
       }
       if (palyType === PlayType.drawing) {
         loadingViewRef.current?.reset();
         useMakePhotoEdit.getState().reset();
-        changePageState(PageState.diy);
+        setTimeout(() => {
+          changePageState(PageState.diy);
+        }, 200); // 这里是为了等photoLoading状态更新
       }
     } else {
       showConfirm({
         title: '确定退出炖图吗？',



## File: 'app/parallel-world/[id].tsx'

@@ -27,9 +27,10 @@ 
 import ParallelWorldConsumer from '@/src/bizComponents/parallelWorld/screens/consumer';
 import ParallelWorldFeed from '@/src/bizComponents/parallelWorld/screens/feed';
 import ParallelWorldMain from '@/src/bizComponents/parallelWorld/screens/main';
 import ParallelWorldPublish from '@/src/bizComponents/parallelWorld/screens/publish';
-import { Image } from '@/src/components';
+import { Image, Screen } from '@/src/components';
+import { AiTag } from '@/src/components/aiTag';
 import AudioPlayer from '@/src/components/audioPlayer';
 import { audioControl } from '@/src/components/audioPlayer/control';
 import { PagePerformance } from '@/src/components/common/pagePerformance';
 import { useGameFeatureGate } from '@/src/hooks/useGameFeatureGate';


@@ -283,13 +284,18 @@ export default function World() {
   // useEffect(() => {
   //   return () => {
   //     // resetWorld();
   //   };
   // }, []);
 
   return (
     <PagePerformance pathname="parallel-world/[id]">
-      <View style={{ flex: 1, justifyContent: 'center' }}>
+      <Screen
+        headerShown={false}
+        safeAreaEdges={[]}
+        StatusBarProps={{ backgroundColor: 'transparent' }}
+        withWaterMark
+      >
         {curRoute?.route && !isParallelWorldLoading && (
           <Animated.View style={[{ flex: 1 }, $screenFoldStyle_A]}>
             {renderParallelPage(curRoute?.route, curRoute)}
           </Animated.View>


@@ -303,13 +309,13 @@ export default function World() {
           <Video ref={videoRef} url={PARALLEL_WORLD_BG_VIDEO} />
         </View>
         <Image
           cache
           source={PARALLEL_WORLD_BG}
           contentFit="cover"
           style={[styles.$absoluteFull, styles.$bgImg]}
         />
-      </View>
+      </Screen>
     </PagePerformance>
   );
 
   function playBgm(url?: string, force?: boolean) {



## File: 'app/publish/index.tsx'

@@ -98,8 +98,9 @@ 
 import { PartialMessage } from '@bufbuild/protobuf';
 import { ImageAspectRatio } from '@step.ai/proto-gen/raccoon/common/imagen_pb';
 import { useShallow } from 'zustand/react/shallow';
 
+const STORY_LIMIT = 2000;
 const ADD_KEY = 9999;
 const $line: ViewStyle = {
   borderTopWidth: 0.5,
   borderColor: StyleSheet.hex(StyleSheet.currentColors.white, 0.08)


@@ -274,13 +275,13 @@ function Publish() {
   const validTitle = useMemo(() => {
     if (!title.length) return false;
     if (title.length > 30) return false;
     return true;
   }, [title]);
 
   const validStory = useMemo(() => {
     // if (!story.length) return false;
-    if (story.length > 500) return false;
+    if (story.length > STORY_LIMIT) return false;
     return true;
   }, [story]);
 
   const blurAll = () => {


@@ -1091,13 +1092,13 @@ function Publish() {
                 )}
                 <View style={st.contentClearWrap}>
                   <Text
                     style={[
                       st.$text,
                       !validStory && story.length ? st.$error : null
                     ]}
                   >
-                    字数({story.length}/500)
+                    字数({story.length}/{STORY_LIMIT})
                   </Text>
                   {renderClose()}
                 </View>
               </Pressable>



## File: 'app/webview.tsx'

@@ -1,17 +1,18 @@ 
 import { useMemoizedFn } from 'ahooks';
 import { router } from 'expo-router';
 import { useEffect, useMemo, useRef, useState } from 'react';
 import { Pressable, Text, TouchableOpacity } from 'react-native';
 import { WebView, WebViewProps } from 'react-native-webview';
 import { useWebviewMagicVideo } from '@[BASE64_DATA:56chars].hook';
 import { useWebviewPublish } from '@/src/bizComponents/webviewScreen/hooks/webviewPublish.hook';
 import { useWebviewRefreshToken } from '@/src/bizComponents/webviewScreen/hooks/webviewRefreshToken.hook';
 import {
   WebviewReportState,
   useWebviewReport
 } from '@/src/bizComponents/webviewScreen/hooks/webviewReport.hook';
 import { Icon, hideLoading, showToast } from '@/src/components';
+import { AiTag } from '@/src/components/aiTag';
 import { PagePerformance } from '@/src/components/common/pagePerformance';
 import { hideShare, showCommonShare, showShare } from '@/src/components/share';
 import { shareCompPresets } from '@/src/components/share/channelConfig';
 import { ChannelConfig, ShareCompPreset } from '@/src/components/share/typings';


@@ -47,12 +48,13 @@ 
 export type WebviewScreenParams = {
   url: string;
   title: string;
   shareParams?: string;
   searchParams?: string;
   screenParams?: string;
   webviewParams?: string;
   preloadScene?: string;
+  withAiTag?: string;
   darkThemeStatus?: string;
   theme?: Theme;
 };
 


@@ -68,13 +70,14 @@ 
 export interface WebviewShareParams {
   gameType?: GameType;
   scene?: WebviewShareScene;
   shareInfo?: Partial<ShareInfo>;
 }
 
 export default function WebviewScreen() {
   const {
     url = '',
+    withAiTag = '0',
     title = '',
     shareParams,
     searchParams,
     screenParams = '',


@@ -396,12 +399,13 @@ export default function WebviewScreen() {
   const themeColor = useMemo(() => {
     return theme === 'light' ? StyleSheet.lightTheme : StyleSheet.darkTheme;
   }, [theme]);
 
   return (
     <PagePerformance pathname="webview">
       <Screen
         theme={theme}
+        withWaterMark={withAiTag === '1'}
         title={showTitle}
         screenStyle={{
           backgroundColor: backgroundColor || themeColor.background.page
         }}



## File: 'src/bizComponents/detailScreen/imageContent/index.tsx'

@@ -52,17 +52,18 @@ 
 import { usePKTagData } from '../pkTag/pkTag.hook';
 import { ToFirstDetailTag } from '../toFristDetailTag';
 import { useDetailShareCompConfig } from '../useDetailShareCompConfig';
 import { PhotoExtype } from '@/proto-registry/src/web/raccoon/common/assets_pb';
 import { CommonActions, useIsFocused } from '@react-navigation/native';
 import { useShallow } from 'zustand/react/shallow';
 import { ImageItem } from './ImageItem';
 import { SlidePagination } from './SlidePagination';
 import {
   DEFAULT_RATIO,
   MID_RATIO_SECTION,
   getImageContentRatios
 } from './utils';
+import { MaterlizeMask } from '@/src/components/materialize/MaterlizeMask';
 
 export type LikeCallbackConfig = {
   doubleLike?: {
     offsetX: number;


@@ -704,14 +705,14 @@ export function ImageContent(props: ImageContentProps) {
               detailId={cardId}
               protoId={
                 props.data?.photos?.[index - 1]?.subPhotos?.[0]?.protoId ||
                 props.data?.photos?.[index - 1]?.protoId
               }
               protos={props.data?.protos}
               style={{
                 position: 'absolute',
-                right: 12,
-                bottom: 16
+                right: 12, 
+                bottom: 54
               }}
               fromId={fromId as string}
             />
 


@@ -722,12 +723,13 @@ export function ImageContent(props: ImageContentProps) {
                   total={total}
                   onPress={onPressSlidePagination}
                 />
               </View>
             )}
             {validImages.type === ImageType.photo && (
               <PKTag pks={pkTagData} photo={curImgInfo.curImg as PhotoInfo} />
             )}
+        <MaterlizeMask logParams={{game_type:`${detail?.gameType ?? ''}`,card_id:cardId??''}}/>
           </View>
         </GestureDetector>
       </View>
     </>


@@ -758,13 +760,13 @@ const st = StyleSheet.create({
     zIndex: 1
   },
   $slide: {
     width: '100%',
     height: '100%'
   },
   $pagin: {
     position: 'absolute',
-    bottom: 6,
+    bottom: 44,
     width: '100%',
     zIndex: 2
   }
 });



## File: 'src/bizComponents/detailScreen/toFristDetailTag/index.tsx'

@@ -1,10 +1,11 @@ 
 import { useMemoizedFn } from 'ahooks';
 import { useNavigation } from 'expo-router';
 import { useEffect, useMemo } from 'react';
-import { StyleProp, Text, View, ViewStyle } from 'react-native';
+import { StyleProp, View, ViewStyle } from 'react-native';
 import { Gesture, GestureDetector } from 'react-native-gesture-handler';
 import Animated, { FadeIn, FadeOut, runOnJS } from 'react-native-reanimated';
+import { Text } from '@/src/components';
 import { CommonColor } from '@/src/theme/colors/common';
 import { createStyle } from '@/src/utils';
 import { reportClick, reportExpo } from '@/src/utils/report';
 import { ArrowIcon } from '@/assets/image/svg';


@@ -96,13 +97,12 @@ 
 const $styles = createStyle({
   wrapper: {
     borderTopRightRadius: 14,
     borderBottomRightRadius: 14,
     overflow: 'hidden',
     position: 'absolute',
     backgroundColor: 'rgba(0, 0, 0, 0.3)',
     left: 0,
-    bottom: 16
   },
 
   innerContainer: {
     height: 28,



## File: 'src/bizComponents/feedcard/entryCard/GameTemplateCard.tsx'

@@ -13,8 +13,9 @@ 
 import LinearGradient from 'react-native-linear-gradient';
 import { Image, showToast } from '@/src/components';
 import { EntryMediaList } from '@/src/components/publishEntry/constant';
 import { useChangeRoute } from '@/src/hooks/useChangeRoute';
+import { useDevicePerformance } from '@/src/hooks/useDevicePerformance';
 import { typography } from '@/src/theme';
 import { CardType, GameTemplateExtInfo, GameType } from '@/src/types';
 import { StyleSheet, dp2px } from '@/src/utils';
 import { formatNumber } from '@/src/utils/opt/transNum';


@@ -42,12 +43,13 @@ export const GameTemplateCard = (
     index,
     onLeave,
     reportParams,
     eventBus,
     containerStyle: $containerStyle
   } = props;
 
   const { gameType, displayImageUrl, displayVideoUrl } = data.card || {};
+  const { isLowEndDevice } = useDevicePerformance();
   const templateInfo = data.card?.imitationCardInfo
     ?.value as GameTemplateExtInfo;
   const {
     templateId,


@@ -171,13 +173,13 @@ export const GameTemplateCard = (
       >
         {/* content */}
         <View
           style={{
             flex: 1,
             alignItems: 'center'
           }}
         >
-          {displayVideoUrl ? (
+          {displayVideoUrl && !isLowEndDevice ? (
             <View
               style={{
                 height: posterHeight,
                 width: posterWidth,



## File: 'src/bizComponents/feedcard/ugcCard/BBSCard.tsx'

@@ -243,13 +243,12 @@ export function BBSCard(
   const onPress = useMemoizedFn((e: GestureResponderEvent) => {
     if (!teenModeGuard()) return;
 
     globalCache.set('lastTouchPoint', {
       x: e.nativeEvent.pageX - e.nativeEvent.locationX,
       y: e.nativeEvent.pageY - e.nativeEvent.locationY
     });
 
-    // @ts-expect-error 上报参数
     reportClick('content_button', {
       ...reportParams
     });



## File: 'src/bizComponents/feedcard/ugcCard/VideoRender.tsx'

@@ -1,23 +1,28 @@ 
 import { useMemoizedFn } from 'ahooks';
 import {
   forwardRef,
   memo,
   useEffect,
   useImperativeHandle,
   useMemo,
   useRef,
   useState
 } from 'react';
 import { View } from 'react-native';
 import { Image, ImageProps } from '@/src/components';
 import { TWaterFallInternalEvent } from '@/src/components/waterfall/type';
+import { useDevicePerformance } from '@/src/hooks/useDevicePerformance';
 import { PlainType } from '@/src/types';
 import { isIos } from '@/src/utils';
+import { getVideoUrlFromCard } from '@/src/utils/cardUtils';
+import { checkWeakNetwork } from '@/src/utils/device/network';
 import { ReportError, errorReport } from '@/src/utils/error-log';
 import { Event } from '@/src/utils/event';
 import { formatTosUrl } from '@/src/utils/getTosUrl';
 import { FeedRichCardInfo } from '../../feedScreen/type';
+import { useVideoStatusUpdate } from '../../livePhotoScreen/hooks/useVideoStatusUpdate';
+import { bindwidthRecorder } from '../../livePhotoScreen/timeRecord';
 import { LivePhotoExtInfo } from '@/proto-registry/src/web/raccoon/common/livephoto_pb.js';
 import { ReimagineExtInfo } from '@/proto-registry/src/web/raccoon/common/reimagine_pb.js';
 import { useIsFocused } from '@react-navigation/native';
 import { Video, VideoProps } from '@step.ai/expo-av';


@@ -56,14 +61,21 @@ 
 const VideoRender = forwardRef<{ stop: () => Promise<void> }, VideoRenderProps>(
   ({ data, index, eventBus, onReady }, ref) => {
     const [showVideo, setShowVideo] = useState(false);
     const [isError, setIsError] = useState(false);
     const videoRef = useRef<Video>(null);
     const isFocused = useIsFocused();
+    const { isLowEndDevice } = useDevicePerformance();
+    const isWeakNewwork = useMemo(
+      () => checkWeakNetwork(bindwidthRecorder.getEWMA()),
+      []
+    );
 
     const [lastLoadPoster, setLastLoadPoster] = useState<string | undefined>();
     const [lastLoadVideo, setLastLoadVideo] = useState<string | undefined>();
 
+    const { onPlaybackStatusUpdate } = useVideoStatusUpdate();
+
     useImperativeHandle(ref, () => {
       return {
         async stop() {
           await videoRef.current?.stopAsync();


@@ -87,20 +99,29 @@ const VideoRender = forwardRef<{ stop: () => Promise<void> }, VideoRenderProps>(
       };
       eventBus?.on('firstVisiableVideoCard', listener);
       return () => {
         eventBus?.off('firstVisiableVideoCard', listener);
       };
     }, [index, eventBus]);
 
     const videoSource = useMemo(() => {
-      const cardExtInfo = data?.card?.cardExtInfo?.value?.value;
-
-      const uri =
-        data.card?.displayVideoUrl ||
-        (cardExtInfo as LivePhotoExtInfo | ReimagineExtInfo)?.videoUrl ||
-        (cardExtInfo as OtakudanceExtInfo)?.video?.url;
+      const uri = getVideoUrlFromCard(
+        data?.card,
+        isLowEndDevice || isWeakNewwork
+      );
       return uri ? { uri } : void 0;
-    }, [data.card]);
+    }, [data.card?.id]); // isLowEndDevice不加入deps，防止清晰度切换。
+
+    // useEffect(() => {
+    //   console.log('### isLowEndDevice', isLowEndDevice);
+    //   if (showVideo && isLowEndDevice) {
+    //     console.log(
+    //       '### 使用低分辨率视频',
+    //       data?.card?.displayVideoUrl,
+    //       videoSource
+    //     );
+    //   }
+    // }, [data?.card?.id, showVideo]);
 
     const posterSource = useMemo(() => {
       const cardExtInfo = data?.card?.cardExtInfo?.value?.value;
 


@@ -152,12 +173,13 @@ const VideoRender = forwardRef<{ stop: () => Promise<void> }, VideoRenderProps>(
           onReady={onPosterReady}
         />
         {Boolean(videoSource) && !isError ? (
           <Video
             ref={videoRef}
             source={realSource}
             shouldPlay={true}
             isMuted={true}
+            onPlaybackStatusUpdate={onPlaybackStatusUpdate}
             usePoster
             // posterSource={posterSource}
             // PosterComponent={PosterComponent}
             // status={{



## File: 'src/bizComponents/goods/edit/components/material/index.tsx'

@@ -8,8 +8,9 @@ 
 import Animated, { FadeIn } from 'react-native-reanimated';
 import { uploadMakePhotoImg } from '@/src/api';
 import { goodsClient } from '@/src/api/goods';
 import { Text, hideLoading, showLoading, showToast } from '@/src/components';
+import { AiTag } from '@/src/components/aiTag';
 import { PrimaryButton } from '@/src/components/primaryButton';
 import CreditWrapper from '@/src/components/v2/credit-wrapper';
 import { usePersistFn, useScreenSize } from '@/src/hooks';
 import { GOODS_NAME, useGoodsStore } from '@/src/store/goods';



## File: 'src/bizComponents/goods/edit/index.tsx'

@@ -155,12 +155,13 @@ export const GoodsEdit = memo(() => {
         onGenerateFinish={onGenerateFinish}
       />
     );
   }
   return (
     <Screen
       theme="dark"
       onBack={back}
+      withWaterMark={editMode === EditMode.Material}
       title={title}
       screenStyle={styles.screen}
       headerStyle={styles.header}
       safeAreaEdges={['top']}



## File: 'src/bizComponents/goodsHome/components/GoodsDetailModal.tsx'

@@ -53,8 +53,9 @@ 
 import { GoodsProductType } from '@/proto-registry/src/web/raccoon/goods/common_pb';
 import { GoodsView } from './GoodsView';
 import { GuestLike } from './GuestLike';
 import { MeLike } from './MeLike';
+import { MaterializeButton } from '@/src/components/materialize/MaterializeButton';
 
 type Props = {
   mode: GoodsDetailShowMode;
   detail?: GoodsDetailFE;


@@ -551,18 +552,21 @@ export const GoodsDetailModal = memo(
                   </LinearGradient>
                 </View>
               </Animated.View>
             </View>
             <Animated.View style={descAnimatedStyle}>
               {[GoodsDetailShowMode.Me, GoodsDetailShowMode.Create].includes(
                 mode
               ) && (
+                <View style={styles.publishContainer}>
+                  <MaterializeButton logParams={{goods_id:id??'',module:`${logParams.module}`}} />
                 <Pressable onPress={onPublish} style={styles.publish}>
                   <View style={styles.publishInner}>
                     <Icon icon="publish" size={15} />
                     <Text style={styles.publishText}>发布作品</Text>
                   </View>
-                </Pressable>
+                  </Pressable>
+                </View>
               )}
             </Animated.View>
             {showTopLikes && (
               <View style={styles.actionContainer}>


@@ -665,12 +669,21 @@ const styles = StyleSheet.create({
   bgBottom: {
     backgroundColor: '#19191a',
     position: 'absolute',
     width: '100%',
     height: 400,
     pointerEvents: 'none',
     bottom: 0
   },
+  publishContainer: {
+    gap:15,
+    alignItems: 'center',
+    zIndex: 100, 
+    flexDirection: 'row', 
+    justifyContent: 'space-between',
+    width: '100%',
+    paddingHorizontal: 45,
+  },
   goodsContainer: { pointerEvents: 'none' },
   wrapperPosition: {
     backgroundColor: 'transparent',
     alignItems: 'center',


@@ -732,14 +745,12 @@ const styles = StyleSheet.create({
   },
   publish: {
     borderRadius: 100,
     backgroundColor: '#ff6a3b',
     width: 136,
     height: 50,
     elevation: 100,
     flexDirection: 'row',
-    alignItems: 'center',
-    zIndex: 100,
     justifyContent: 'center',
     alignSelf: 'center'
   },
   publishInner: {



## File: 'src/bizComponents/livePhotoPublish/index.tsx'

@@ -59,8 +59,9 @@ 
 import { useEditHandler } from './useEditHandler';
 import { usePlayHandler } from './usePlayHandler';
 import { usePublishHandler } from './usePublishHandler';
 import { getGameIdFromExtInfo, isOwnAudioPlayType } from './utils';
+import { MaterializeXiaoli } from '@/src/components/materialize/MaterializeXiaoli';
 
 const SafeAreaEdges: ExtendedEdge[] = ['top', 'bottom'];
 
 export type Edit = {


@@ -233,13 +234,13 @@ const LivePhotoPublish: FC<Props> = props => {
           style={st.specialEffectWrap}
           onPress={() => setOpenSpecialEffectsModal(true)}
         >
           <Icon icon="special_effects" style={st.specialIcon} />
           <Text style={st.specialText}>添加特效</Text>
         </TouchableOpacity>
       );
     }
-    return null;
+    return <MaterializeXiaoli logParams={{module:'publish', game_type:gameType}}/>;
   });
 
   return (
     <>



## File: 'src/bizComponents/livePhotoScreen/ImmersiveVideo.tsx'

@@ -18,15 +18,16 @@ 
 import { TapGestureWrapper } from '@/src/components/gesture/TapGestureWrapper';
 import { useTapThrottle } from '@/src/components/gesture/useTapThrottle';
 import { BlurImage } from '@/src/components/image/BlurImage';
 import { hideFollowGuideToast } from '@/src/components/popup/detailFollowToast';
 import { BOTTOM_TAB_HEIGHT } from '@/src/constants';
 import {
   useSafeBottomAddNav,
   useSafeBottomArea,
   useSafeBottomWithNav,
   useScreenSize
 } from '@/src/hooks';
+import { useDevicePerformance } from '@/src/hooks/useDevicePerformance';
 import { useRunOnce } from '@/src/hooks/useOneRunning';
 import { useParams } from '@/src/hooks/useParams';
 import { useBehaviorStore } from '@/src/store/behavior';
 import { useDetailStore } from '@/src/store/detail';


@@ -34,12 +35,22 @@ 
 import { useVideoFlowComment } from '@/src/store/video-flow-comment';
 import { darkColors } from '@/src/theme';
 import { GameType, RichCardInfo, VideoFLowScene } from '@/src/types';
 import { StyleSheet, isIos } from '@/src/utils';
+import {
+  getLowResolutionVideoUrl,
+  getVideoUrlFromCard
+} from '@/src/utils/cardUtils';
+import { checkWeakNetwork } from '@/src/utils/device/network';
 import { ReportError, errorReport } from '@/src/utils/error-log';
 import { Event } from '@/src/utils/event';
 import { formatTosUrl } from '@/src/utils/getTosUrl';
-import { report, reportClick, reportExpo } from '@/src/utils/report';
+import {
+  addCommonReportParams,
+  report,
+  reportClick,
+  reportExpo
+} from '@/src/utils/report';
 import {
   IMMER_EVENT_KEYS,
   TImmersiveEventKeys
 } from '../trendingScreen/ImmersiveDetail/constants';


@@ -65,9 +76,9 @@ 
 import { ScaleableVideo } from './ScaleableVideo';
 import { TakeSameSheet } from './TakeSameSheet';
 import { ActionsLayer } from './actionsLayer';
 import { TVIDEO_FLOW_EVENT_KEYS, VIDEO_FLOW_EVENTS } from './constants';
-import { startPlayTimeRecorder } from './timeRecord';
+import { bindwidthRecorder, startPlayTimeRecorder } from './timeRecord';
 
 interface LivePhotoScreenProps {
   data?: RichCardInfo;
   isActive?: boolean;


@@ -268,13 +279,19 @@ export const ImmersiveVideo = memo(
     };
 
     const [transitionEnd, setTransitionEnded] = useState(false);
 
     const videoRef = useRef<Video>(null);
     const timeoutRef = useRef<NodeJS.Timeout>();
     const isPlayingRef = useRef(false);
     const retryTimeRef = useRef(0);
-    const safeBottom = safePaddingBottom || useSafeBottomArea(20);
+    const { isLowEndDevice } = useDevicePerformance();
+    const isWeakNewwork = useMemo(
+      () => checkWeakNetwork(bindwidthRecorder.getEWMA()),
+      []
+    );
+    const defaultSafePadding = useSafeBottomArea(20);
+    const safeBottom = safePaddingBottom || defaultSafePadding;
 
     const bottomHeight = BOTTOM_TAB_HEIGHT + safeBottom;
 
     const [height, marginTop, ratio] = useVideoLayout(


@@ -330,16 +347,41 @@ export const ImmersiveVideo = memo(
         onPause?.();
       } else {
         onPlay?.();
       }
       setPaused(i => !i);
       hideFollowGuideToast();
     });
 
-    const displayVideoUrl =
-      card?.displayVideoUrl ||
-      (card?.cardExtInfo?.value.value as LivePhotoExtInfo | undefined)
-        ?.videoUrl;
+    useEffect(() => {
+      const lowResolution = Boolean(
+        (isLowEndDevice || isWeakNewwork) && getLowResolutionVideoUrl(card)
+      );
+      addCommonReportParams('videofeed', {
+        low_resolution: Number(lowResolution)
+      });
+    }, []);
+
+    const displayVideoUrl = useMemo(
+      () => getVideoUrlFromCard(data?.card, isLowEndDevice || isWeakNewwork),
+      [data?.card?.id]
+    ); // isLowEndDevice不加入deps，防止清晰度切换。
+
+    // useEffect(() => {
+    //   console.log(
+    //     '### isLowEndDevice',
+    //     isLowEndDevice,
+    //     ', isWeakNewwork: ',
+    //     isWeakNewwork
+    //   );
+    //   if (isCurrent && (isLowEndDevice || isWeakNewwork)) {
+    //     console.log(
+    //       '### 使用低分辨率视频',
+    //       data?.card?.displayVideoUrl,
+    //       displayVideoUrl
+    //     );
+    //   }
+    // }, [data?.card?.id, isCurrent]);
 
     const videoSource =
       (shouldLoadVideo || shouldPlay) && displayVideoUrl
         ? { uri: displayVideoUrl }


@@ -393,34 +435,40 @@ export const ImmersiveVideo = memo(
         //   '重试开始-----',
         //   isPlayingRef.current,
         //   retryTimeRef.current,
         //   shouldPlay,
         //   videoSource,
         //   card?.id,
         //   index
         // );
+        const retryVideoSource = getVideoUrlFromCard(card, true);
         if (isPlayingRef.current) {
           clearTimeout(timeoutRef.current);
           timeoutRef.current = undefined;
         }
-        if (shouldPlayRef.current && card?.id && videoSource) {
+        if (shouldPlayRef.current && card?.id && retryVideoSource) {
+          console.log('### 重试使用低分辩率');
           // console.log('重试开始-----', retryTimeRef.current);
           try {
             await videoRef.current?.unloadAsync();
-            await videoRef.current?.loadAsync(videoSource, {
-              shouldPlay: shouldPlayRef.current,
-              isMuted: isMutedRef.current,
-              isLooping: true
-            });
+            await videoRef.current?.loadAsync(
+              { uri: retryVideoSource },
+              {
+                shouldPlay: shouldPlayRef.current,
+                isMuted: isMutedRef.current,
+                progressUpdateIntervalMillis: 50,
+                isLooping: true
+              }
+            );
           } catch (error) {
             console.log('retry error ====', error);
             reportExpo('video_error', {
               module: 'videofeed',
               retryTime: retryTimeRef.current,
-              media_uri: videoSource?.uri
+              media_uri: retryVideoSource
             });
             errorReport('视频重试错误', ReportError.VIDEO_FLOW, error, {
-              media_uri: videoSource?.uri,
+              media_uri: retryVideoSource,
               retryTime: retryTimeRef.current
             });
           }
           // console.log(



## File: 'src/bizComponents/livePhotoScreen/TakeSameSheet.tsx'

@@ -125,13 +125,14 @@ export const TakeSameSheet = ({ data, scene }: TakeSameSheetProps) => {
   const takeSamePhoto = useOneRunning(
     useCallback(async () => {
       // GetImagegenProto()
       reportClick('join_video', { module: 'videofeed', joinType: 2 });
       const extInfo = data?.card?.cardExtInfo?.value.value as LivePhotoExtInfo;
       console.log(
         '### 同款图片: ',
         extInfo.basePhotoGameType,
-        extInfo.templateName
+        extInfo.templateName,
+        extInfo.basePhotoId
       );
       if (extInfo && cardId) {
         go2Create({
           gameType:


@@ -140,13 +141,14 @@ export const TakeSameSheet = ({ data, scene }: TakeSameSheetProps) => {
               | GameType.LIVE_PHOTO
               | GameType.INSTANCE
               | GameType.REIMAGINE
               | GameType.OTAKUDANCE
             >) || GameType.DRAWING,
           gameParams: {
             cardId,
             // extra: card?.extra ?? '{}',
-            photoId: extInfo.basePhotoId ?? ''
+            photoId: extInfo.basePhotoId ?? '',
+            photoUrl: data?.card?.displayImageUrl
           },
           params: {
             source:
               scene === VideoFLowScene.TRENDING



## File: 'src/bizComponents/livePhotoScreen/hooks/useVideoStatusUpdate.ts'

@@ -1,8 +1,9 @@ 
 import { useMemoizedFn } from 'ahooks';
 import { useCallback, useMemo, useRef } from 'react';
 import { KeyEqualToValue } from '@/src/types';
 import { Event } from '@/src/utils/event';
+import { bindwidthRecorder } from '../timeRecord';
 import { AVPlaybackStatus } from '@step.ai/expo-av';
 
 export type TVIDEO_EVENTS_KEYS =
   | 'ended'


@@ -52,14 +53,18 @@ 
 export const useVideoStatusUpdate = () => {
   const eventBus = useMemo(() => new Event<TVIDEO_EVENTS_KEYS>(), []);
   const loadStateRef = useRef<boolean>(false);
   // 只能使用hack方式判断启播
+  const { updater } = bindwidthRecorder;
   const playStateRef = useRef<PlayState>(PlayState.initial);
   const onPlaybackStatusUpdate = useMemoizedFn((state: AVPlaybackStatus) => {
     if (!state.isLoaded) {
       loadStateRef.current = false;
       eventBus.emit(VIDEO_EVENTS.error, state);
     } else {
+      if (state?.loadSpeed) {
+        updater(state.loadSpeed);
+      }
       if (!loadStateRef.current) {
         loadStateRef.current = true;
         eventBus.emit(VIDEO_EVENTS.loaded, state);
       }



## File: 'src/bizComponents/livePhotoScreen/timeRecord.ts'

@@ -1,14 +1,36 @@ 
-export const recorderFactory = (initialNumber = 3000) => {
-  let recordTimes = 0;
-  let total = 0;
+import { BINDWIDTH_TIER } from '@/src/utils/device/network';
+
+/**
+ * 指数加权移动平均：S[n] = alpha * a[n] + S[n] * (1 - alpha)
+ * @param initialNumber 初始值
+ * @param windowSize 移动平均的窗口大小
+ * @param alpha 指数加权移动平均因子
+ * @returns
+ */
+export const recorderFactory = (
+  initialNumber = 3000,
+  windowSize = 5,
+  alpha = 0.5
+) => {
+  const window: number[] = [];
+  let ewma = initialNumber;
   return {
-    updater: (duration: number) => {
-      total += duration;
-      recordTimes += 1;
+    updater: (current: number) => {
+      window.push(current);
+      if (window.length > windowSize) {
+        window.shift();
+      }
+      ewma = current * alpha + ewma * (1 - alpha);
+    },
+    getAVG: () => {
+      if (window.length === 0) return initialNumber;
+      const sum = window.reduce((acc, val) => acc + val, 0);
+      return Math.round(sum / window.length);
     },
-    getAVG: () =>
-      recordTimes === 0 ? initialNumber : Math.round(total / recordTimes)
+    getEWMA: () => ewma
   };
 };
 
 export const startPlayTimeRecorder = recorderFactory();
+
+export const bindwidthRecorder = recorderFactory(BINDWIDTH_TIER.THIRD_G);



## File: 'src/bizComponents/playgoundScreen/meme/MemePublish.tsx'

@@ -21,8 +21,9 @@ 
 import { checkSecurity } from '@/src/api/utils';
 import { ErrorRes } from '@/src/api/websocket/stream_connect';
 import { TopicSelector } from '@/src/bizComponents/topicSelector';
 import { hideLoading, showLoading, showToast } from '@/src/components';
+import { AiTag } from '@/src/components/aiTag';
 import { SaveButton } from '@/src/components/album/SaveButton';
 import { BounceView } from '@/src/components/animation';
 import { CustomBlurView } from '@/src/components/image/CustomBlurView';
 import { MakePhotoEvents } from '@/src/components/makePhoto/constant';


@@ -226,13 +227,13 @@ export default function MemePublish({
   const primaryPublishButton = () => {
     if (showKeyboard) return null;
     return (
       <View
         style={[
           StyleSheet.rowStyle,
           {
             position: 'absolute',
-            bottom: +(paddingBottom || 0) + 8,
+            bottom: +(paddingBottom || 0) + 15,
             width: '100%'
           }
         ]}
       >


@@ -339,12 +340,13 @@ export default function MemePublish({
       <Screen
         theme="dark"
         title="发布"
         headerRight={() => showKeyboard && publishButton()}
         onBack={() => {
           setTopics([]);
           safeGoBack();
         }}
+        withWaterMark
         safeAreaEdges={['top']}
       >
         <KeyboardAvoidingView
           behavior={isIos ? undefined : 'height'}



## File: 'src/bizComponents/rankListScreen/RankListButton.tsx'

@@ -147,29 +147,12 @@ export const RankListButton = memo(function RankListButton() {
       >
         <RNImage
           source={RANKLIST_ICON}
           style={styles.ranklistIcon}
           // onLoadEnd={handleIconLoadEnd}
         />
         {hasNewRank && <View style={styles.redDot} />}
       </TouchableOpacity>
-      {!disableSchoolRank && !schoolRankVisited && (
-        <TouchableOpacity
-          onPress={nav2SchoolRank}
-          style={{
-            position: 'absolute',
-            width: 113,
-            height: 61,
-            right: -16,
-            bottom: -48
-          }}
-        >
-          <Image
-            source={SCHOOL_RANK_BUBBLE}
-            style={{ width: '100%', height: '100%' }}
-          />
-        </TouchableOpacity>
-      )}
     </View>
   );
 });



## File: 'src/bizComponents/rankListScreen/index.tsx'

@@ -590,13 +590,12 @@ export function RankListScreen({
       <View
         style={{
           position: 'absolute',
           width: 20,
           top: 0,
           bottom: 0
         }}
       />
-      <SchoolRankEntry />
     </Screen>
   );
 }



## File: 'src/bizComponents/role-create/generateRole/intex.tsx'

@@ -10,11 +10,12 @@ 
 import Carousel, {
   CarouselRenderItem,
   ICarouselInstance
 } from 'react-native-reanimated-carousel';
 import { ErrorRes } from '@/src/api/websocket/stream_connect';
 import UGCRolePhotoSet from '@/src/bizComponents/role-create/ugcRolePhotoSet';
 import { Icon, Text, showToast } from '@/src/components';
+import { AiTag } from '@/src/components/aiTag';
 import { BaseModal } from '@/src/components/modal/BaseModal';
 import { Confirm } from '@/src/components/popup/confirmModalGlobal/CustomConfirm';
 import CreditWrapper from '@/src/components/v2/credit-wrapper';
 import { ROLE_CREATE_PORTAL } from '@/src/constants';


@@ -340,12 +341,13 @@ export const UGCRoleSelectModal = ({
                     <Text style={$styles.ta}>就Ta了</Text>
                   </LinearGradient>
                 </TouchableOpacity>
                 <View />
               </View>
             </GestureHandlerRootView>
           </GestureDetector>
         </Animated.View>
+        <AiTag />
       </View>
       {confirmVisible && (
         <Confirm
           outerVisible={true}



## File: 'src/components/aiTag/index.tsx'

@@ -0,0 +1,49 @@
+import { useEffect, useState } from 'react';
+import { Platform, View } from 'react-native';
+import { useKeyboard } from '@/src/hooks';
+import { StyleSheet, isIos } from '@/src/utils';
+import { Text } from '../text';
+
+export const AiTag = () => {
+  const nativeShowKeyBoard = useKeyboard();
+  const [showKeyboard, setShowKeyboard] = useState(false);
+  useEffect(() => {
+    if (Platform.OS === 'android') {
+      setShowKeyboard(nativeShowKeyBoard);
+    }
+  }, [nativeShowKeyBoard]);
+  return (
+    <View
+      style={[
+        styles.container,
+        {
+          bottom: isIos ? 15 : 0
+        }
+      ]}
+    >
+      <Text style={[styles.text, { opacity: showKeyboard ? 0 : 1 }]}>
+        内容由AI生成
+      </Text>
+    </View>
+  );
+};
+
+const styles = StyleSheet.create({
+  container: {
+    zIndex: 5000,
+    position: 'absolute',
+    width: '100%',
+    alignItems: 'center',
+    justifyContent: 'center',
+    left: 0,
+    right: 0,
+    bottom: 15
+  },
+  text: {
+    fontSize: 10,
+    letterSpacing: -0.3,
+    fontFamily: 'PingFang SC',
+    color: 'rgba(255, 255, 255, 0.2)',
+    textAlign: 'left'
+  }
+});



## File: 'src/components/icons/icon.tsx'

@@ -125,12 +125,13 @@ export function Icon(props: IconProps) {
           source={iconRegistry[icon]}
         />
       )}
     </Wrapper>
   );
 }
 
 export const iconRegistry = {
+  materialize: require('@Assets/icon/materize-icon.png'),
   add_gray_bg: require('@Assets/icon/ip-list-add.png'),
   flip: require('@Assets/icon/makephoto/icon-flip.png'),
   puzzle: require('@Assets/icon/makephoto/puzzle.png'),
   arrow_right: require('@Assets/icon/icon-action-arrow.png'),



## File: 'src/components/makePhoto/bottomTab/index.tsx'

@@ -167,14 +167,14 @@ export const BottomTab = memo(() => {
         showToast(checkPrompts.toast);
         return;
       }
     }
     takePhoto({ st });
   });
 
   useEffect(() => {
+    console.log('photoLoading----', photoLoading);
     if (photoLoading) {
-      console.log('changePageState effect', Date.now());
       changePageState(PageState.effect);
     }
   }, [photoLoading]);



## File: 'src/components/makePhoto/emojiPannel/EmojiPrview/index.tsx'

@@ -540,13 +540,13 @@ function PreviewBottom({
       style={[
         StyleSheet.rowStyle,
         {
           justifyContent: 'center',
           position: 'absolute',
           width: width - 32,
           left: 16,
           right: 16,
-          bottom: $containerInsets.paddingBottom ? 0 : 8
+          bottom: 20
         },
         $animateStyle,
         $customStyle
       ]}



## File: 'src/components/makePhoto/emojiPannel/index.tsx'

@@ -189,12 +189,13 @@ export function EmojiPannel(props: PanelProps) {
     });
   };
 
   return (
     <Screen
       theme="dark"
       title="炖表情"
       onBack={handleBack}
+      withWaterMark={pageState === PageState.preview}
       rightStyle={$rightWrap}
       backgroundView={
         <PannelBg customBg={BG} loading={loading} showLoading={showLoading} />
       }



## File: 'src/components/makePhoto/loadingView/index.tsx'

@@ -79,15 +79,16 @@ export const LoadingView = forwardRef(
         role2: state.role2
       }))
     );
 
     useImperativeHandle(ref, () => ({
       reset
     }));
 
-    const reset = useCallback(() => {
+    const reset = useCallback(async () => {
+      console.log('reset----');
       resetRef.current = true;
-      videoRef.current?.reset();
+      await videoRef.current?.reset();
       useMakePhotoStoreV2.setState({
         photoLoading: false
       });
     }, []);


@@ -225,13 +226,14 @@ export const LoadingView = forwardRef(
         videoRef.current?.next();
       }
       if (index === 2) {
         await videoRef.current?.next();
         go2Preview();
       }
     });
     const onAllFinished = usePersistFn(() => {
-      videoRef.current?.reset();
+      console.log('onAllFinished----');
+      reset();
       finishedRef.current = true;
     });
 
     return (


@@ -259,14 +261,14 @@ export const LoadingView = forwardRef(
           (pageState === PageState.effect ||
             pageState === PageState.preview) && (
             <TouchableOpacity
               style={{
                 position: 'absolute',
                 top: Number($containerInsets.paddingTop) + 10,
                 left: 16
               }}
-              onPress={() => {
-                reset();
+              onPress={async () => {
+                await reset();
                 useMakePhotoStoreV2.getState().changePageState(PageState.diy);
               }}
             >
               <Icon



## File: 'src/components/makePhoto/previewView/PreviewViewSimple.tsx'

@@ -308,12 +308,13 @@ export const PreviewViewSimple = memo(() => {
       >
         <Screen
           theme="dark"
           safeAreaEdges={['top']}
           screenStyle={{
             width: '100%',
             height: '100%'
           }}
+          withWaterMark
           headerStyle={{
             height: HEADER_HEIGHT
           }}
           headerLeft={() => <Credits />}



## File: 'src/components/makePhoto/previewView/components/BottomActions.tsx'

@@ -24,20 +24,21 @@ 
 import { useStorageStore } from '@/src/store/storage';
 import { GameType, InvokeType } from '@/src/types';
 import { StyleSheet, dp2px } from '@/src/utils';
 import { reportClick, reportExpo } from '@/src/utils/report';
 import { useShallow } from 'zustand/react/shallow';
 import {
   ACTIONS_HEIGHT,
   ActionsConfig,
   TemplateActions
-} from './TemplateActions';
+} from './TemplateActions'; 
+import { SaveToAlbumLarge } from './previewItem/SaveToAlbumLarge';
 
 const isIos = Platform.OS === 'ios';
 
 export const PANEL_HEIGHT = isIos ? 235 : 215;
-export const BOTTOM_MARGIN = isIos ? 40 : 10;
-const GAP_HEIGHT = 12;
+export const BOTTOM_MARGIN = isIos ? 40 : 20;
+const GAP_HEIGHT = 12;  
 const BUTTON_HEIGHT = 44;
 
 export const BOTTOM_ACTION_HEIGHT =
   BOTTOM_MARGIN + GAP_HEIGHT + ACTIONS_HEIGHT + BUTTON_HEIGHT;


@@ -44,18 +45,20 @@ 
 export const BottomActions = memo(
   ({
     currentIndex,
     onNext,
     onShowPanel,
     onHidePanel,
     onCreateLive,
     onTextSubmit,
     onTextFocus,
     onAddPuzzle,
     onChangeIndex
+    ,onSaveImage
   }: {
     currentIndex: number;
     onNext: () => void;
+    onSaveImage: () => void;
     onShowPanel: () => void;
     onHidePanel: () => void;
     onCreateLive: () => void;
     onTextSubmit?: () => void;


@@ -127,15 +130,16 @@ export const BottomActions = memo(
     const showTemplateActions = useMemo(() => {
       return initTab === null && !showTextEdit;
     }, [initTab, showTextEdit]);
     const { width } = useScreenSize('window');
     const renderMainBtn = () => {
       const renderActionBtn = () => {
         const text = photoLiveTemplate?.id ? '用当前图片生成' : '去发布';
         return (
-          <View style={[{ width: '100%' }, StyleSheet.centerStyle]}>
+          <View style={[StyleSheet.centerStyle,{ width: '100%', gap:8, flexDirection:'row' }]}>
+            <SaveToAlbumLarge onSaveImage={onSaveImage} />
             <PrimaryButton
-              width={330}
+              width={220}
               height={BUTTON_HEIGHT}
               useDp2px
               primaryBgProps={{
                 colors: ['rgba(255, 106, 59, 1)', 'rgba(255, 106, 59, 1)']



## File: 'src/components/makePhoto/previewView/components/previewItem/SaveToAlbumLarge.tsx'

@@ -0,0 +1,43 @@
+import * as React from 'react';
+import { TouchableOpacity } from 'react-native-gesture-handler';
+import { Icon } from '@/src/components/icons';
+import { StyleSheet } from '@/src/utils';
+import { Text } from '@Components/text';
+
+type Props = {
+  onSaveImage: () => void;
+};
+
+export const SaveToAlbumLarge = React.memo(({ onSaveImage }: Props) => {
+  return (
+    <TouchableOpacity
+      onPress={onSaveImage}
+      style={[styles.parent, StyleSheet.rowStyle]}
+    >
+      <Icon size={16} icon="download" />
+      <Text style={styles.text}>我的图集</Text>
+    </TouchableOpacity>
+  );
+});
+
+const styles = StyleSheet.create({
+  text: { 
+    fontSize: 14,
+    lineHeight: 18,
+    fontWeight: "600",
+    fontFamily: "PingFang SC",
+    color: "rgba(255, 255, 255, 0.9)",
+    textAlign: "center"
+  },
+  parent: {
+      borderRadius: 100,
+      width: 110,
+      backgroundColor: 'rgba(255, 255, 255, 0.08)',
+      paddingLeft: 16, 
+      paddingRight: 18,
+      gap: 4,
+      height: 42,
+      alignItems: 'center',
+      justifyContent: 'center'
+  }
+});



## File: 'src/components/makePhoto/previewView/index.tsx'

@@ -24,8 +24,9 @@ 
 import { dp2px } from '@/src/utils';
 import { Image } from '@Components/image';
 import { Screen } from '@Components/screen';
 import { StyleSheet } from '@Utils/StyleSheet';
+import { AiTag } from '../../aiTag';
 import { GuideWrapper, showGuide } from '../../guide';
 import MadePhotoGuide, {
   useMakePhotoGuide
 } from '../../guide/guide-content/madephoto';


@@ -40,8 +41,9 @@ 
 import { useShallow } from 'zustand/react/shallow';
 import { PREVIEW_PUZZLE } from './types';
 import { useImageSize } from './useImageSize';
 import { usePreviewHandler } from './usePreviewHandler';
+import { MaterializeXiaoli } from '../../materialize/MaterializeXiaoli';
 
 const ADJUST_TEMPLATE = require('@Assets/makephoto/adjust-template.png');
 const HEADER_HEIGHT = dp2px(44);
 const HEADER_MARGIN = dp2px(6);


@@ -144,14 +146,15 @@ export const PreviewView = memo((props: PreviewViewProps) => {
         screenStyle={{
           backgroundColor: 'rgba(14, 14, 17, 1)',
           width: '100%',
           height: '100%'
         }}
         headerStyle={{
           height: HEADER_HEIGHT
         }}
+        withWaterMark
         headerLeft={() => <Credits />}
-        headerRight={() => <SaveToAlbum onSaveImage={saveImage} />}
+        headerRight={() => <MaterializeXiaoli/>}
         onBack={back}
       >
         <TemplateGuide />
         <Animated.View


@@ -270,12 +273,13 @@ export const PreviewView = memo((props: PreviewViewProps) => {
         <BottomActions
           currentIndex={
             puzzlePhotos.length === 0
               ? currentIndex
               : currentIndex < puzzlePhotos.length
                 ? GROUP_MAKE_PHOTO_COUNT + currentIndex
                 : currentIndex - 1
           }
+          onSaveImage={saveImage}
           onNext={publish}
           onShowPanel={onPanelShow}
           onHidePanel={onPanelHide}
           onCreateLive={onCreateLive}



## File: 'src/components/makePhoto/roleSelector/IPRoleSelector.tsx'

@@ -300,13 +300,13 @@ const styles = StyleSheet.create({
   gradientInner: {
     width: '100%',
     height: 170,
     position: 'absolute',
     bottom: 54
   },
   roleListOuter: {
     flex: 1,
-    marginLeft: isIos ? 82 : 86
+    marginLeft: 82
   },
   listOuter: { flex: 1, marginBottom: 70 },
   containerOuter: {
     marginTop: 20,



## File: 'src/components/makePhoto/roleSelector/RoleList.tsx'

@@ -196,13 +196,12 @@ export const RoleList = forwardRef<
   );
 
   // 这里使用index为key可以复用容器
   const keyExtractor = useMemoizedFn((item, index) =>
     item === createRoleEntrySymbol ? 'loading_key' : `${index}`
   );
   return (
     <FlatList
-      style={{ flex: 1, paddingRight: 8 }}
       scrollEventThrottle={100}
       ref={scrollRef}
       initialNumToRender={9}
       data={validList}



## File: 'src/components/materialize/MaterializeButton.tsx'

@@ -0,0 +1,41 @@
+import { StyleSheet } from "@/src/utils";
+import { memo } from "react";
+import { TouchableOpacity } from "react-native";
+import { Icon } from "../icons";
+import { Text } from "../text"; 
+import { Reminder } from "../reminder";
+import { MaterializeParams, useMaterialize } from "./useMaterialize";
+
+export const MaterializeButton = memo(((params:MaterializeParams) => {
+    const { openMaterializeForm } = useMaterialize(params);
+
+    return (
+        <TouchableOpacity style={styles.container} onPress={openMaterializeForm}>
+            <Icon icon="materialize" size={15} />
+            <Text style={styles.text}>一键做实物</Text>
+            <Reminder text="谷子变实体" style={{paddingTop:1}} top={-4} right={-10} />
+        </TouchableOpacity>
+    )
+}));
+
+const styles = StyleSheet.create({
+    container: {
+        backgroundColor: "#2a2a2e",
+        borderRadius: 40,
+        width: '50%',
+        flexDirection: "row",
+        paddingHorizontal: 20,
+        paddingVertical: 15,
+        gap:6,
+        justifyContent: 'center',
+        alignItems: 'center',
+    },
+    text: {
+        color: 'rgba(255, 255, 255, 0.90)',
+        textAlign: 'center',
+        fontSize: 15,
+        fontStyle: 'normal',
+        fontWeight: '600',
+        lineHeight: 20,
+    }
+})
\ No newline at end of file



## File: 'src/components/materialize/MaterializeXiaoli.tsx'

@@ -0,0 +1,72 @@
+import { StyleSheet } from "@/src/utils";
+import { memo } from "react";
+import { TouchableOpacity } from "react-native"; 
+import { Text } from "../text";  
+import { MaterializeParams, useMaterialize } from "./useMaterialize"; 
+import { Image } from "../image";
+const IMAGE_XIAOLI = require('@Assets/icon/materize-xiaoli.png');
+const YELLOW_STAR = require('@Assets/icon/yellow-star.png');
+export const MaterializeXiaoli = memo((params:MaterializeParams) => {
+    const { openMaterializeForm } = useMaterialize(params);
+
+    return (
+        <TouchableOpacity style={styles.container} onPress={openMaterializeForm}>
+     
+            <Text style={styles.text}>一键做实物</Text>
+            <Image
+                source={IMAGE_XIAOLI} 
+                style={styles.imageXiaoli}
+                resizeMode="contain"
+            />
+            <Image
+                source={YELLOW_STAR} 
+                style={styles.imageYellowStar}
+                resizeMode="contain"
+            />
+        </TouchableOpacity>
+    );
+});
+
+const styles = StyleSheet.create({
+    container: {
+        backgroundColor: "#ff6a3b",
+        borderTopRightRadius: 19,
+        borderBottomRightRadius: 19,
+        height:28,
+        flexDirection: "row",
+        paddingRight: 9,
+        borderColor: '#fff',
+        paddingLeft:25,
+        borderWidth: 1, 
+        alignItems:'center',
+        gap:2.5, 
+    },
+    imageXiaoli: {
+        position:'absolute',
+        width: 32,
+        left:-10,
+        height: 33,
+        
+    },
+    imageYellowStar: {
+        position:'absolute',
+        width: 10,
+        left: -17,
+        top:-5,
+        height: 10,
+    },
+    text: { 
+        fontSize: 12,
+        lineHeight: 18,
+        fontWeight: "600",
+        fontFamily: "PingFang SC",
+        color: "rgba(255, 255, 255, 0.9)",
+        textAlign: "left",
+        textShadowColor: "rgba(255, 37, 1, 0.28)",
+        textShadowOffset: {
+        width: 0,
+        height: 1
+        },
+        textShadowRadius: 1
+    }
+})
\ No newline at end of file



## File: 'src/components/materialize/MaterlizeMask.tsx'

@@ -0,0 +1,52 @@
+import { StyleSheet } from "@/src/utils";
+import { memo } from "react";
+import { TouchableOpacity } from "react-native";
+import { Icon } from "../icons";
+import { Text } from "../text"; 
+import { MaterializeParams, useMaterialize } from "./useMaterialize";
+import { Gesture, GestureDetector } from "react-native-gesture-handler";
+import { runOnJS } from "react-native-reanimated";
+
+export const MaterlizeMask = memo(((params:MaterializeParams) => {
+    const { openMaterializeForm } = useMaterialize(params);
+    const gesture = Gesture.Tap().onBegin(() => {
+        runOnJS(openMaterializeForm)();
+      });
+    return (
+        <GestureDetector gesture={gesture}>
+            <TouchableOpacity style={styles.container}>
+                <Icon icon="materialize" size={15} />
+                <Text style={styles.text}>一键做实物 · 试试把图片换成实体谷子吧 </Text>
+                <Icon icon="mall_arrow_right" size={20}  color={'#fff'} containerStyle={styles.arrowRight}/>
+            </TouchableOpacity>
+        </GestureDetector>
+    )
+}));
+
+const styles = StyleSheet.create({
+    container: {
+        position: 'absolute',
+        bottom: 0,
+        backgroundColor: "#00000066", 
+        width: '100%',
+        height: 38,
+        flexDirection: "row",
+        paddingStart:13,
+        gap: 6,
+        justifyContent:'flex-start',
+        alignItems: 'center',
+    },
+    arrowRight: {  
+        position: 'absolute',
+        right: 12,
+    },
+    text: {  
+        fontSize: 13,
+        fontWeight: "500",
+        fontFamily: "PingFang SC",
+        color: "#fff",
+        textAlign: "left",
+        display: "flex",
+        alignItems: "center", 
+    }
+})
\ No newline at end of file



## File: 'src/components/materialize/useMaterialize.ts'

@@ -0,0 +1,34 @@
+import { Linking } from 'react-native';
+import { useCallback } from 'react';
+import { errorReport, ReportError } from '@/src/utils/error-log';
+import { showToast } from '../toast';
+import { reportClick } from '@/src/utils/report';
+
+const FORM_URL = 'https://wvixbzgc0u7.feishu.cn/share/base/form/shrcndPQVhq9knSiBSSjxbGllQB';
+
+export type MaterializeParams = {logParams?:Record<string,string>}
+
+export const useMaterialize = (params:MaterializeParams) => {
+    const showErrorToast = useCallback(() => {
+        showToast('出错啦～无法打开页面')
+    },[]);
+    const openMaterializeForm = useCallback(async () => {
+        console.log('openMaterializeForm',params.logParams);
+        reportClick('create_button', { ...(params.logParams ?? {}) });
+        try {
+            const supported = await Linking.canOpenURL(FORM_URL);
+            if (supported) {
+                await Linking.openURL(FORM_URL);
+            } else {
+                showErrorToast();
+            }
+        } catch (error) {
+          showErrorToast();
+           errorReport('openMaterializeForm',ReportError.REQUEST,error)
+        }
+    }, []);
+
+    return {
+        openMaterializeForm,
+    };
+};
\ No newline at end of file



## File: 'src/components/publishEntry/GameEntryCard.tsx'

@@ -6,12 +6,13 @@ 
 import { Image } from '@/src/components';
 import { Text } from '@/src/components/text';
 import { LOGIN_SCENE } from '@/src/constants';
 import { useAuthState, usePersistFn } from '@/src/hooks';
+import { useDevicePerformance } from '@/src/hooks/useDevicePerformance';
 import { useFontStore } from '@/src/store/font';
 import { useResourceStore } from '@/src/store/resource';
 import { typography } from '@/src/theme';
-import { dp2px, isLowEndDevice } from '@/src/utils';
+import { dp2px } from '@/src/utils';
 import { formatTosUrl } from '@/src/utils/getTosUrl';
 import { Reminder } from '../reminder';
 import { useIsFocused } from '@react-navigation/native';
 import { ResizeMode, Video, getDebugInfo } from '@step.ai/expo-av';


@@ -87,12 +88,13 @@ export const GameEntryCard = ({
   const { loginIntercept } = useAuthState();
 
   const { getResource, getVideoSource } = useResourceStore(
     useShallow(state => ({
       getVideoSource: state.getVideoResource,
       getResource: state.getResource
     }))
   );
+  const { isLowEndDevice } = useDevicePerformance();
   const layoutInfo = LayoutInfo[scene];
   const isFocus = useIsFocused();
   const [error, setError] = useState(false);
   const AlibabaPuHuiTiHeavy = useFontStore(



## File: 'src/components/publishEntry/index.tsx'

@@ -48,8 +48,9 @@ 
 import { Source, reportClick, reportExpo } from '@/src/utils/report';
 import { Text } from '@Components/text';
 import { useAuthState } from '../../hooks/useAuthState';
 import { Icon } from '../icons';
+import { MaterializeButton } from '../materialize/MaterializeButton';
 import { Image } from '../image';
 import { CustomBlurView } from '../image/CustomBlurView';
 import { SkinnedImage } from '../skin/SkinnedImage';
 import { CONFIG_KYES } from '../skin/getSkinConfig';


@@ -368,13 +369,15 @@ function PublishEntryCom({
                       onClick={() => {
                         setVisible(false);
                       }}
                       active={visible}
                     />
                   </Animated.View>
 
                   {publishEntry.visible ? (
-                    <Pressable
+                    <View style={$publishContainerWrapper}>
+                      <MaterializeButton logParams={{module:'feed'}} />
+                      <Pressable
                       onPress={() => {
                         publishEntry.onPress?.(publishEntry, { lishi: '1' });
                         setVisible(false);
                       }}


@@ -382,14 +385,14 @@ function PublishEntryCom({
                       <Animated.View
                         entering={fadeInAnimation[2]}
                         style={[$publishContainer]}
                       >
                         {publishEntry.icon?.()}
                         <Text style={$publishText}>发布作品</Text>
                       </Animated.View>
                     </Pressable>
+                  </View>
                   ) : null}
-
                   {visible ? (
                     <Pressable onPress={onClose}>
                       <Animated.View
                         entering={RotateInDownLeft.delay(100)}


@@ -446,12 +449,21 @@ 
 const $entriesContainer: ViewStyle = {
   display: 'flex',
   flexDirection: 'column',
   alignItems: 'center',
   justifyContent: 'flex-end',
   height: '100%'
 };
-
+const $publishContainerWrapper: ViewStyle = {
+    gap: 15,
+    marginTop: 40,
+    alignItems: 'center',
+    zIndex: 100, 
+    flexDirection: 'row', 
+    justifyContent: 'space-between',
+    width: '100%',
+    paddingHorizontal: 45,
+  }
 const $drawingBg: ImageStyle = {
   width: dp2px(375),
   height: dp2px(114),
   resizeMode: 'contain'


@@ -577,14 +589,13 @@ 
 const $publishContainer: ViewStyle = {
   display: 'flex',
   flexDirection: 'row',
   alignItems: 'center',
   backgroundColor: StyleSheet.darkTheme.background.skeleton,
   paddingVertical: 12,
   paddingLeft: 20,
   paddingRight: 24,
   borderRadius: 40,
-  marginTop: 40
 };
 
 const $publishText: TextStyle = {
   fontSize: 15,



## File: 'src/components/screen/index.tsx'

@@ -40,8 +40,9 @@ 
 import { Text } from '@Components/text';
 import { StyleSheet } from '@Utils/StyleSheet';
 import dp2px from '../../utils/dp2px';
 import { SkeletonLoader } from '../SkeletonLoader';
+import { AiTag } from '../aiTag';
 import { ScrollViewRef } from '@/app/detail/[id]';
 
 interface BaseScreenProps {
   /**


@@ -66,12 +67,14 @@ interface BaseScreenProps {
   headerRight?: () => ReactNode;
 
   headerTitle?: () => ReactNode;
 
   headerShown?: boolean;
 
   maskAreaShown?: boolean;
 
+  withWaterMark?: boolean;
+
   backButton?: boolean | ReactNode;
 
   loading?: boolean;
 


@@ -299,11 +302,12 @@ 
 export const Screen = (props: ScreenProps) => {
   const {
     KeyboardAvoidingViewProps,
     needDismissKeboard,
     keyboardOffset = 0,
     safeAreaEdges = ['top', 'bottom'],
     StatusBarProps,
+    withWaterMark = false,
     headerShown = true,
     maskAreaShown = true,
     backgroundView,
     useKeyboardAvoid = true,


@@ -330,48 +334,51 @@ export const Screen = (props: ScreenProps) => {
       <>
         {renderBody(props)}
         {maskAreaShown && <MaskArea />}
       </>
     );
   };
 
   return (
-    <View style={[$containerStyle, wholePageStyle]}>
-      {backgroundView}
-      <View
-        style={[
-          { position: 'relative', flex: 1, overflow: 'hidden' },
-          $containerInsets,
-          $screenStyleOverride
-        ]}
-        onStartShouldSetResponder={
-          needDismissKeboard
-            ? () => {
-                Keyboard.dismiss();
-                return false;
-              }
-            : undefined
-        }
-      >
-        <StatusBar
-          style={theme === 'light' ? 'dark' : 'light'}
-          {...StatusBarProps}
-        />
-        {headerShown ? <Header {...props} themeColors={themeColors} /> : null}
-        {useKeyboardAvoid ? (
-          <KeyboardAvoidingView
-            behavior={isIos ? 'padding' : undefined}
-            keyboardVerticalOffset={keyboardOffset}
-            {...KeyboardAvoidingViewProps}
-            style={[{ flex: 1 }, KeyboardAvoidingViewProps?.style]}
-          >
-            {renderMain()}
-          </KeyboardAvoidingView>
-        ) : (
-          <View style={flex1Style}>{renderMain()}</View>
-        )}
+    <>
+      <View style={[$containerStyle, wholePageStyle]}>
+        {backgroundView}
+        <View
+          style={[
+            { position: 'relative', flex: 1, overflow: 'hidden' },
+            $containerInsets,
+            $screenStyleOverride
+          ]}
+          onStartShouldSetResponder={
+            needDismissKeboard
+              ? () => {
+                  Keyboard.dismiss();
+                  return false;
+                }
+              : undefined
+          }
+        >
+          <StatusBar
+            style={theme === 'light' ? 'dark' : 'light'}
+            {...StatusBarProps}
+          />
+          {headerShown ? <Header {...props} themeColors={themeColors} /> : null}
+          {useKeyboardAvoid ? (
+            <KeyboardAvoidingView
+              behavior={isIos ? 'padding' : undefined}
+              keyboardVerticalOffset={keyboardOffset}
+              {...KeyboardAvoidingViewProps}
+              style={[{ flex: 1 }, KeyboardAvoidingViewProps?.style]}
+            >
+              {renderMain()}
+            </KeyboardAvoidingView>
+          ) : (
+            <View style={flex1Style}>{renderMain()}</View>
+          )}
+        </View>
       </View>
-    </View>
+      {withWaterMark && <AiTag />}
+    </>
   );
 };
 
 // Screen.displayName = 'Screen';



## File: 'src/components/video/index.tsx'

@@ -31,9 +31,9 @@ 
 export interface VideoHandle {
   next: () => Promise<void>;
   play: () => void;
   show: () => void;
-  reset: () => void;
+  reset: () => Promise<void>;
   hide: () => void;
   changeSources: (payload: VideoItem[]) => void;
 }
 


@@ -67,19 +67,15 @@ export const Video = forwardRef<VideoHandle, VideoProps>((props, ref) => {
     opacity: opacityFrontVal.value
   }));
 
   const $videoAnimationStyle = useAnimatedStyle(() => ({
     opacity: opacityVal.value
   }));
 
   const initLoad = usePersistFn(async () => {
-    await videoBackRef.current
-      ?.loadAsync(getVideoSource()[0].source, {
-        shouldPlay: false
-      })
-      .then(() => {
-        onLoadFinish(false);
-      });
+    await videoBackRef.current?.loadAsync(getVideoSource()[0].source, {
+      shouldPlay: true
+    });
     preload();
   });
   // 初始化 预加载
   useEffect(() => {


@@ -97,15 +93,18 @@ export const Video = forwardRef<VideoHandle, VideoProps>((props, ref) => {
       return;
     }
     if (status.isLoaded) {
       if (status.didJustFinish) {
         onFinish();
       }
     }
   }
-  function onVideoError(err) {
-    errorReport('ExpoVideo', ReportError.MAKE_PHOTO, err);
-    onFinish(true);
+  async function onVideoError(err, isFront) {
+    errorReport(
+      'ExpoVideo ' + (isFront ? 'front' : 'back'),
+      ReportError.MAKE_PHOTO,
+      err
+    );
   }
 
   return (
     <>


@@ -112,15 +111,13 @@ export const Video = forwardRef<VideoHandle, VideoProps>((props, ref) => {
       <Animated.View
         style={[StyleSheet.absoluteFill, $frontVideoAnimationStyle]}
       >
         <ExpoVideo
           isMuted
           ref={videoBackRef}
           style={[StyleSheet.absoluteFill]}
           resizeMode={ResizeMode.COVER}
-          // eslint-disable-next-line react/jsx-no-bind
-          onError={onVideoError}
-          // eslint-disable-next-line react/jsx-no-bind
+          onError={e => onVideoError(e, false)}
           onPlaybackStatusUpdate={onVideoFinish}
         />
       </Animated.View>
       <Animated.View style={[StyleSheet.absoluteFill, $videoAnimationStyle]}>


@@ -124,15 +121,13 @@ export const Video = forwardRef<VideoHandle, VideoProps>((props, ref) => {
         />
       </Animated.View>
       <Animated.View style={[StyleSheet.absoluteFill, $videoAnimationStyle]}>
         <ExpoVideo
           isMuted
           style={[StyleSheet.absoluteFill]}
           ref={videoFrontRef}
           resizeMode={ResizeMode.COVER}
-          // eslint-disable-next-line react/jsx-no-bind
-          onError={onVideoError}
-          // eslint-disable-next-line react/jsx-no-bind
+          onError={e => onVideoError(e, true)}
           onPlaybackStatusUpdate={onVideoFinish}
         />
       </Animated.View>
     </>


@@ -137,27 +132,20 @@ export const Video = forwardRef<VideoHandle, VideoProps>((props, ref) => {
       </Animated.View>
     </>
   );
 
   async function preload() {
     const nextIndex = index.current + 1;
     if (nextIndex >= getVideoSource().length) return;
     const showFront = showFrontVideo.current;
-    if (showFront) {
-      videoBackLoadStatus.current = false;
-    } else {
-      videoFrontLoadStatus.current = false;
-    }
     const nextItem = getVideoSource()[nextIndex];
+
+    console.log('preload----', showFront, nextIndex, nextItem);
     const nextRef = showFront ? videoBackRef : videoFrontRef;
-    await nextRef.current?.unloadAsync();
-    await nextRef.current
-      ?.loadAsync(nextItem.source, {
-        shouldPlay: false
-      })
-      .then(() => {
-        onLoadFinish(!showFront);
-      });
+    // await nextRef.current?.unloadAsync();
+    await nextRef.current?.loadAsync(nextItem.source, {
+      shouldPlay: false
+    });
   }
 
   async function show() {
     resetRef.current = false;


@@ -169,32 +157,14 @@ export const Video = forwardRef<VideoHandle, VideoProps>((props, ref) => {
   }
 
   function hide() {
     opacityFrontVal.value = 0;
     opacityVal.value = 0;
     index.current = 0;
     showFrontVideo.current = false;
   }
-  function onLoadFinish(isFront) {
-    if (isFront) {
-      videoFrontLoadStatus.current = true;
-    } else {
-      videoBackLoadStatus.current = true;
-    }
-    if (
-      (showFrontVideo.current && isFront) ||
-      (!showFrontVideo.current && !isFront)
-    ) {
-      play();
-    }
-  }
   function play() {
-    if (
-      (showFrontVideo.current && videoFrontLoadStatus.current) ||
-      (!showFrontVideo.current && videoBackLoadStatus.current)
-    ) {
-      return getCurrentVideo()?.playAsync();
-    }
+    return getCurrentVideo()?.playAsync();
   }
 
   function getVideoSource() {
     return sourceRef.current || [];


@@ -227,21 +197,25 @@ export const Video = forwardRef<VideoHandle, VideoProps>((props, ref) => {
   }
 
   function getCurrentVideo() {
     return showFrontVideo.current
       ? videoFrontRef.current
       : videoBackRef.current;
   }
 
-  function reset() {
+  function resetStatus() {
     opacityFrontVal.value = 0;
     opacityVal.value = 0;
     index.current = 0;
     videoBackLoadStatus.current = false;
     videoFrontLoadStatus.current = false;
     showFrontVideo.current = false;
-    videoBackRef?.current?.unloadAsync(); 
-    videoFrontRef?.current?.unloadAsync();
+  }
+
+  async function reset() {
+    resetStatus();
+    await videoBackRef?.current?.unloadAsync();
+    await videoFrontRef?.current?.unloadAsync();
     // resetRef.current = true;
   }
 
   function changeSources(source: VideoItem[]) {



## File: 'src/components/waterfall/WaterFall2.tsx'

@@ -8,22 +8,26 @@ 
 import {
   ForwardedRef,
   MutableRefObject,
   forwardRef,
   useCallback,
   useEffect,
   useMemo,
   useRef
 } from 'react';
 import { View } from 'react-native';
 import { CellCard } from '@/src/bizComponents/feedcard/cellcard';
 import { CellCardScene } from '@/src/bizComponents/feedcard/types';
+import { bindwidthRecorder } from '@/src/bizComponents/livePhotoScreen/timeRecord';
 import { useAuthStore } from '@/src/store/authInfo';
 import { useBehaviorStore } from '@/src/store/behavior';
 import { useHistoryStore } from '@/src/store/histroy';
 import { CommonColor } from '@/src/theme/colors/common';
 import { Theme } from '@/src/theme/colors/type';
 import { CardInfo, CardType, GameType } from '@/src/types';
+import { getVideoUrlFromCard } from '@/src/utils/cardUtils';
+import { checkWeakNetwork } from '@/src/utils/device/network';
+import { getIsLowEndDeviceFromCache } from '@/src/utils/device/performanceTier';
 import { ReportError, errorReport } from '@/src/utils/error-log';
 import { Event } from '@/src/utils/event';
 import { preloadVideo } from '@/src/utils/preloadVideo';
 import { getPageName, reportExpo } from '@/src/utils/report';


@@ -271,18 +275,21 @@ export const WaterFall2 = forwardRef(
     const { run: handlePreloadVideo } = useDebounceFn(
       (now?: number[]) => {
         now?.forEach(index => {
           const card = data?.[index]?.card;
           if (
             card?.type === CardType.VIDEO &&
             cellcardScene === CellCardScene.HOME
           ) {
-            const uri =
-              card?.displayVideoUrl ||
-              (card.cardExtInfo?.value.value as LivePhotoExtInfo | undefined)
-                ?.videoUrl;
+            const uri = getVideoUrlFromCard(
+              card,
+              getIsLowEndDeviceFromCache() ||
+                checkWeakNetwork(bindwidthRecorder.getEWMA())
+            );
             console.log('### preload Video:  ', uri);
-            uri && preloadVideo([{ uri }]);
+            if (uri) {
+              preloadVideo([{ uri }]);
+            }
           }
         });
       },
       { wait: 1000 }



## File: 'src/hooks/useChangeRoute.ts'

@@ -809,12 +809,13 @@ export const useChangeRoute = () => {
                 target = undefined;
               }
             }
 
             if (target) {
               const params: Partial<WebviewScreenParams> = {
                 url: `${DECISION_H5_URL}?id=${target}&${qs.stringify({ ...queryParams, cardId })}`,
                 title: '',
+                withAiTag: '1',
                 webviewParams: JSON.stringify(decisionWebviewParams),
                 screenParams: JSON.stringify(decisionWebviewScreenParams),
                 ...queryParams
               };



## File: 'src/hooks/useDevicePerformance.ts'

@@ -1,9 +1,10 @@ 
-import { useEffect, useState } from 'react';
+import { useEffect, useMemo, useState } from 'react';
 import {
   clearPerformanceCache,
   forceRefreshDevicePerformance,
   getDevicePerformanceDetails,
+  getIsLowEndDeviceFromCache,
   getManualDeviceTier,
   setManualDeviceTier
 } from '../utils/device/performanceTier';
 import type {


@@ -72,13 +73,16 @@ interface DevicePerformanceHook {
  * if (isLowEndDevice) {
  *   // 降级渲染逻辑
  * } else {
  *   // 正常渲染逻辑
  * }
  * ```
  */
 export function useDevicePerformance(): DevicePerformanceHook {
-  const [isLowEndDevice, setIsLowEndDevice] = useState<boolean>(false);
+  const initialIsLowEndDevice = useMemo(getIsLowEndDeviceFromCache, []);
+  const [isLowEndDevice, setIsLowEndDevice] = useState<boolean>(
+    initialIsLowEndDevice
+  );
   const [performanceDetails, setPerformanceDetails] =
     useState<DevicePerformanceInfo | null>(null);
   const [deviceTier, setDeviceTier] = useState<DeviceTier | null>(null);
   const [isLoading, setIsLoading] = useState<boolean>(true);



## File: 'src/store/makePhotoV2.ts'

@@ -903,14 +903,15 @@ export const useMakePhotoStoreV2 = create<States & Actions>()((set, get) => ({
     if (role2) {
       const arr2 = formatPromots(additionPrompts2, presetPrompts2);
       prompt += ';' + arr2.filter(i => i).join(',');
     }
     return prompt;
   },
 
   takePhoto(extraInfo) {
+    console.log('takePhoto----');
     const id = uuid();
-    set({ makePhotoGenId: id });
+    set({ makePhotoGenId: id, photos: [], regenIndex: 0 });
     const { style, getRoles, sref, photoSize } = get();
     const roles = getRoles();
 
     const prompt = get().getAllPrompt();


@@ -1085,15 +1086,13 @@ export const useMakePhotoStoreV2 = create<States & Actions>()((set, get) => ({
           },
           true
         );
       }
     );
 
     set({
       $pendingPhoto,
-      photoLoading: true,
-      photos: [],
-      regenIndex: 0
+      photoLoading: true
     });
     reportTimeStart('takephoto_end-expo', 'duration');
   },
   regenPhoto(curIdx, extraInfo) {


@@ -1098,12 +1097,13 @@ export const useMakePhotoStoreV2 = create<States & Actions>()((set, get) => ({
   },
   regenPhoto(curIdx, extraInfo) {
     const { currentProtoId } = get();
 
     if (!currentProtoId) return;
 
     let resolved = 0;
     let rejected = 0;
+    console.log('regenPhoto----', curIdx);
 
     set({ photoLoading: true });
     let firstPhoto: PartialMessage<PhotoProgress> | null = null;



## File: 'src/utils/cardUtils.ts'

@@ -0,0 +1,32 @@
+import { CardInfo, PlainType } from '../types';
+import { LivePhotoExtInfo } from '@/proto-registry/src/web/raccoon/common/livephoto_pb';
+import { ReimagineExtInfo } from '@/proto-registry/src/web/raccoon/common/reimagine_pb';
+import { VideoResolution } from '@/proto-registry/src/web/raccoon/common/video_pb';
+
+export const getVideoUrlFromCard = (
+  card?: PlainType<CardInfo>,
+  isLowEndDevice?: boolean
+) => {
+  const videoUrl = card?.displayVideoUrl || getExtVideoUrl(card?.cardExtInfo);
+  const lowResolutionUrl = getLowResolutionVideoUrl(card) || videoUrl;
+
+  return isLowEndDevice && lowResolutionUrl ? lowResolutionUrl : videoUrl;
+};
+
+export const getExtVideoUrl = (
+  cardExtInfo?: PlainType<CardInfo>['cardExtInfo']
+) => {
+  switch (cardExtInfo?.value.case) {
+    case 'otakudance':
+      return cardExtInfo.value.value.video?.url;
+    default:
+      return (cardExtInfo?.value.value as LivePhotoExtInfo | ReimagineExtInfo)
+        .videoUrl;
+  }
+};
+
+export const getLowResolutionVideoUrl = (card?: PlainType<CardInfo>) =>
+  card?.cardVideos
+    ?.filter(i => i.resolution !== VideoResolution.VIDEO_RESOLUTION_ORIGINAL)
+    ?.sort((a, b) => Number(a.resolution) - Number(b.resolution))[0]
+    ?.displayVideoUrl;



## File: 'src/utils/device/network.tsx'

@@ -4,11 +4,27 @@ 
 export const checkNetWork = async () => {
   try {
     const state = await NetInfo.fetch('');
     if (!state.isConnected) {
       showToast('小狸走丢了');
       return;
     }
   } catch (error) {
     showToast('小狸走丢了');
   }
 };
+
+/**
+ * KB/s
+ */
+export const BINDWIDTH_TIER = {
+  EDGE: 256 / 8, // 256Kbps
+  DSL: 1024 / 8, // 1Mbps
+  THIRD_G: 2048 / 8, // 2Mbps
+  FOURTH_G: 16384 / 8, // 16Mbps
+  WIFT: 65536 / 8 // 64Mbps
+};
+
+export const checkWeakNetwork = (
+  current: number,
+  base = BINDWIDTH_TIER.THIRD_G
+) => current < base;



## File: 'src/utils/device/performanceTier.ts'

@@ -445,9 +445,15 @@ 
 export function clearPerformanceCache(): void {
   console.log('[DeviceInfo][clearPerformanceCache]: 清除内存性能缓存');
   S_MEMORY_CACHE = null;
 }
 
+/**
+ * 从缓存中获取，主要用于hook首次执行的初始值设置
+ */
+export const getIsLowEndDeviceFromCache = () =>
+  S_MEMORY_CACHE?.info?.tier === 'low';
+
 /**
  * 手动设置设备等级（仅在当前App生命周期内有效）
  * 主要用于开发调试，不会持久化到存储中
  * @param tier 要设置的设备等级



## File: 'src/utils/report/index.ts'

@@ -293,13 +293,13 @@ export const reportExpo = (
   }
   const name = `${getReportPage(params!)}-${element}-${type || 'expo'}`;
   sendBizEvent(name, params || {});
 };
 
 export const reportClick = (
   element: string,
   params?: Record<string, string | number | boolean | undefined> & {
-    module: MODULE_TYPE;
+    module?: MODULE_TYPE;
   },
   type?: string
 ) => {
   const name = `${getReportPage(params!)}-${element}-${type || 'click'}`;



## File: 'package.json'

@@ -50,18 +50,18 @@ 
     "@step.ai/android-mainapplication-mod": "^0.1.1",
     "@step.ai/android-mod": "^1.0.0",
     "@step.ai/android-new-file-mod": "^1.0.2",
     "@step.ai/android-project-build-gradle-mod": "^0.1.1",
     "@step.ai/android-settings-gradle-mod": "^0.1.1",
     "@step.ai/api-module": "^1.8.1",
     "@step.ai/apm-module": "^1.0.0-lipu.1",
     "@step.ai/app-info-module": "^1.24.2",
     "@step.ai/appdelegate-mod": "^0.1.0",
     "@step.ai/connect-api-common": "^4.2.0",
     "@step.ai/devtools": "^0.3.2",
     "@step.ai/eas-build-hooks": "^1.9.0",
     "@step.ai/event-monitor": "^0.6.3",
-    "@step.ai/expo-av": "^14.0.7-dev.2",
+    "@step.ai/expo-av": "^14.1.0",
     "@step.ai/expo-cocoapods": "^0.3.0",
     "@step.ai/expo-extensions": "^2.0.0",
     "@step.ai/expo-navigator": "^1.1.2",
     "@step.ai/expo-vibration": "^0.1.2",



## File: 'proto-gen.lock.json'

@@ -1,14 +1,14 @@ 
 {
   "repos": {
     "passport": {
-      "commit": "289b69af42744028aca7d7312ad10c84db2695a6"
+      "commit": "7b1cd0586df2eaa04ddc40efe8d3dd4772274322"
     },
     "step-bridge": {
       "commit": "23d74262f05e9ecc75a94905bdae5381c998d9f3"
     },
     "raccoon": {
-      "commit": "289b69af42744028aca7d7312ad10c84db2695a6"
+      "commit": "7b1cd0586df2eaa04ddc40efe8d3dd4772274322"
     },
     "trpc-infra": {
       "commit": "77fc3539f5d5f285f5a89ba2f20bfebef60c2561"
     }
=====

Note that lines in the diff body are prefixed with a symbol that represents the type of change: '-' for deletions, '+' for additions, and ' ' (a space) for unchanged lines.


Response (should be a valid YAML, and nothing else):
```yaml
```

## Prompt Statistics
- System Prompt Length: 5101 characters
- User Prompt Length: 88505 characters
- Total Length: 93606 characters
- System Prompt Lines: 79
- User Prompt Lines: 2829
