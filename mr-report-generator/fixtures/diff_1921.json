[{"new_path": "app/bbs-feed/index.tsx", "old_path": "app/bbs-feed/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -1,6 +1,6 @@\n import React, { useEffect } from 'react';\n import { ImageStyle } from 'react-native';\n-import { DiscussFeed } from '@/src/bizComponents/feedScreen/DIscussFeed';\n+import { DiscussFeed } from '@/src/bizComponents/feedScreen/DiscussFeed';\n import { Image, Screen } from '@/src/components';\n import { darkTheme } from '@/src/theme/colors';\n import { reportExpo } from '@/src/utils/report';\n"}, {"new_path": "app/feed/index.tsx", "old_path": "app/feed/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -41,7 +41,7 @@ import { MessageScreen } from '@/src/bizComponents/messageScreen';\n import TopicScreen from '@/src/bizComponents/topicScreen';\n import Trending from '@/src/bizComponents/trendingScreen';\n import { UserScreen } from '@/src/bizComponents/userScreen';\n-import { PageTab } from '@/src/bizComponents/userScreen/types';\n+import { UserPageTab } from '@/src/bizComponents/userScreen/constants';\n import { showToast } from '@/src/components';\n import { useGuideToMakePhotoStatus } from '@/src/components/guide/guide-content/make-photo-guide';\n import { PublishEntry } from '@/src/components/publishEntry';\n@@ -127,7 +127,7 @@ export function NewFeed() {\n   useEffect(() => {\n     if (\n       params.profileUpdateTimestamp &&\n-      params.profilePageTab === PageTab.SECRET &&\n+      params.profilePageTab === UserPageTab.SECRET &&\n       params.profileRefresh === '1' &&\n       params.scene === Go2HomeScene.VIDEO_GENERATING\n     ) {\n@@ -247,7 +247,6 @@ function MyTabBar({\n \n   // 折叠状态\n   const $unfold = useSharedValue(1);\n-  const sharedValue = useSharedValue(0);\n \n   useEffect(() => {\n     $unfold.value = withTiming(isTabVisible ? 1 : 0, {\n"}, {"new_path": "app/follow-fan/index.tsx", "old_path": "app/follow-fan/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -9,10 +9,12 @@ import { Tabs } from '@/src/components/tabs';\n import { useSafeAreaInsetsStyle } from '@/src/hooks';\n import { useAuthStore } from '@/src/store/authInfo';\n import { darkColors, darkTheme } from '@/src/theme';\n+import { getThemeColorV2 } from '@/src/theme/colors/common';\n import { ReportError, errorReport } from '@/src/utils/error-log';\n import { log } from '@/src/utils/logger';\n import { reportExpo } from '@/src/utils/report';\n import { StyleSheet } from '@Utils/StyleSheet';\n+import { Theme } from '../../src/theme/colors/type';\n import { useShallow } from 'zustand/react/shallow';\n import { FansList, FollowList } from './components';\n \n@@ -28,7 +30,10 @@ const items = [\n     label: '粉丝'\n   }\n ];\n-export default () => {\n+export default (props: { theme: 'light' | 'dark' } = { theme: 'dark' }) => {\n+  const themeColor = getThemeColorV2(\n+    props.theme === 'light' ? Theme.LIGHT : Theme.DARK\n+  );\n   const { defaultTab = 'follow', uid } = useLocalSearchParams();\n \n   const $containerInsets = useSafeAreaInsetsStyle(['top', 'bottom']);\n@@ -109,8 +114,10 @@ export default () => {\n     <PagePerformance pathname=\"follow-fan/index\">\n       <Screen\n         safeAreaEdges={[]}\n+        theme={props.theme}\n+        StatusBarProps={{ style: props.theme === 'light' ? 'dark' : 'light' }}\n         headerShown={false}\n-        style={{ backgroundColor: darkTheme.background.page }}\n+        style={{ backgroundColor: themeColor.background.page }}\n       >\n         <View style={[{ marginTop: $containerInsets.paddingTop }, st.header]}>\n           <TouchableOpacity\n@@ -119,7 +126,7 @@ export default () => {\n               router.back();\n             }}\n           >\n-            <Icon icon=\"back\" color={darkTheme.text.primary}></Icon>\n+            <Icon icon=\"back\" color={themeColor.text.primary}></Icon>\n           </TouchableOpacity>\n           {/* <Animated.View style={[]}> */}\n           <Tabs\n@@ -167,7 +174,6 @@ const tabStyles = StyleSheet.create({\n   },\n   $tabItemTextStyle: {\n     textAlign: 'center',\n-    color: StyleSheet.hex(StyleSheet.currentColors.black, 0.4),\n     fontWeight: '600',\n     fontSize: 16,\n     lineHeight: 26\n"}, {"new_path": "app/magic-video-history/history-item/index.tsx", "old_path": "app/magic-video-history/history-item/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -2,7 +2,7 @@ import dayjs from 'dayjs';\n import { router } from 'expo-router';\n import { Pressable, Text, TextStyle, View, ViewStyle } from 'react-native';\n import ImageBox from '@/src/bizComponents/magic-video/imageBox';\n-import { PageTab } from '@/src/bizComponents/userScreen';\n+import { UserPageTab } from '@/src/bizComponents/userScreen/constants';\n import StatusCode from '@/src/bizComponents/videoMagic/statusCode';\n import { Go2HomeScene } from '@/src/hooks/useChangeRoute';\n import { useLiveStore } from '@/src/store/live';\n@@ -54,7 +54,7 @@ export default function HistoryItem({ item, index }: IHistoryItemProps) {\n             profileUpdateTimestamp: Date.now().toString(),\n             profileRefresh: '1',\n             scene: Go2HomeScene.VIDEO_GENERATING,\n-            profilePageTab: PageTab.SECRET\n+            profilePageTab: UserPageTab.SECRET\n           }\n         });\n         break;\n@@ -67,7 +67,7 @@ export default function HistoryItem({ item, index }: IHistoryItemProps) {\n             profileUpdateTimestamp: Date.now().toString(),\n             profileRefresh: '1',\n             scene: Go2HomeScene.VIDEO_GENERATING,\n-            profilePageTab: PageTab.SECRET\n+            profilePageTab: UserPageTab.SECRET\n           }\n         });\n         break;\n"}, {"new_path": "assets/image/goods-shef/goods_bg.webp", "old_path": "assets/image/goods-shef/goods_bg.webp", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "Binary files /dev/null and b/assets/image/goods-shef/goods_bg.webp differ\n"}, {"new_path": "assets/lottie/greenlight.json", "old_path": "assets/lottie/greenlight.json", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "--- \n+++ \n@@ -1 +1 @@\n-{\"v\":\"5.12.1\",\"fr\":30,\"ip\":0,\"op\":90,\"w\":300,\"h\":300,\"nm\":\"绿灯导出\",\"ddd\":0,\"assets\":[{\"id\":\"image_0\",\"w\":300,\"h\":300,\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsCAMAAABOo35HAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAQlBMVEVHcEw9Yz03gjc/bj8A/wAA/wAA/wAA/wAA/wAA/wAlrCUoaihWd1YniCcpVilJbUkyWzIz9Egdsh4DPwOetZ53l3csSp0QAAAAC3RSTlMA/n+6HBAkFQUKSxq1tI8AACAASURBVHja7F2Hdts4EIRlQgQUifQ5z///q2dRKFtmAaq6RJDsuOXdaTI7O1tIO/c8z/M8z/M8z/M8z/M8z/M8z3PJCcbjefoYPTFr4bTu5/5FlJx67T1WpY/CP4hVoIixRzkOIlY+Cf+sRpHT/uI/RTDyOgkgDDcbzPJ3w6/nV+VERQDA4gDHKGYFpd8s+IGilV+1CrgEIUdNUE+J/i/CLPOggqZppOGhX3QSs0Dj8HfxS0ZSQovRSCCkTvk7hIzu1+HFYqYGX31jGDlMLfBjVPTcL8mP5N+eqBSLPxB9mlZKwVTu/Mn0Cgwp+uJCCaf1x6mPHOWZ+/HBSC24DjMXzkZMAleJytLjD4vIwN+4Ppsafg1gPBx/NqtuChQ3ZIEL18+zXsRhB0qpG+GE6fUzU2NNTPRlrAYqlsc6vJj5+knMCgQr5tTb6FCE8KNPsZpGfk5mzCEA3KQJlY3L2Zg5UQj9kMJGVCh2wAkUMDrwR3U05sz4E7hV/kVDP/wYIHFVHBYShhZmTnIrfFe10iUzoJUIqRNnGqciFutfsJOjoz3pb0ov2tRrIJVfaIGAAqKf9SdiW96EagVujb9ray84wyjEwqJClCjwMIhVf4iEZAwmwZx0qt9RrZzjhY3T4ZeVJ8PAMAHckrARxPqC//2a9UytCKmcKej5BRfaxNghV6Q/HUhQIgVzzKR+N0dP1MoiVXlBFKgoGBZWwFW5FVt+QpfW34lZpbmA2nlVqdhr7rHJwiuQsC30spPjt1KupAlsWiWFKkVfQEp0GWL5o4bWf7vdkiAjcB2prj0kOWasQDSWwYb7FnHIsqBhqjKpbgmV/AJASw4nvzQr4izoYPW3llgePJr2q9oNWAWRtvNX+/ngWM2MaxpbyX19FmB8VG890LhxtaSesCp8Ca/EoEs5BV7RNBDrPcqPtQkWg06M3M9/mcyXbiiubyqvTEr5dVAx4vluvQ1dF0Hr4dyigzpe7QutMpXKRISdFmwEN+JHgo5F0kH9IguRzRXsXKV4AEAZjOIA1aeNGqJYshHIRdBG/UO5VS27w+VNKpUDq2wopRBOglMCPgCZwIx6VaFcZMfCPb7dnP9x+JwLuIWOkgs0BJskwaLAC5CLdsGQm3/0IJb866Dyhup6aKAledMglv5+MyRTEYQd/aOjkMs6avLp6Mv5n+MU+xjZiHUJJtBywkI8ALFA+kOoGrQbn5VTJ8DOgwkhDFNj7Xs1BhoPC8RA+jFOR6AKQNsXGGcsjxUcQ/6etfVxX/ARXj7wqbM53TJYld/ZCKHTRs2UfBCKdXf3MV4er8aYjp2TymQWwQU8DMAS7tEDqDLBA0qKIbgHefkARvPZWilp9wUwMwgLGOaD/NGhl+ZWNEzEY9qnhcISsGStILVsUo0CEBiFyzfSN0/4Cu2yE6Nuc/FliLtxq24+2bMbs/qzOOUrHPkDL2BKxOsSzIIMb/LevdWcoXKiLQobVzkEGzpFAm0UvPL6w4TXiDNl1JkxQGqRJsTduBX0loyIQUYs3xQrK/AI1+inHDqvBT8K5fKoeQMXk8LdwjD3r0AEKmFvidUIYg0LFoSxYm2yiyVG0OMqfutucciWalkUBskqHy2xKtHkuwCNVmBW0DC7tIdQ3Lq3kw+wz1CHUqpmRiXwSMR8PBMulhqxzTc6qppb7n5lYpDG3andmIJW/p8Fdp0bzbORQuBRtGjLJyLAImpB3IFcwcllBsor1bqyffoNEOqkRlUuBs0tPiK7U6GDeCX0ylN5N6u/DmbDWsgWpEYtXSgrQg9xJ2bpMofuDVFzBcVK2AKNzkAe6QvpuQoxEZA8KQasW7RMDLesBtE0NQZm3D1rWxWUKFSjDZX+wsBAxLD5pFyj7kbrriDlFu8xh3sIVjsP4sqGqLpvxBxjFmDV0KBWwmpsKJeVE28fh3KniCIGm+y4BWNQikNkPQqeyH15bFJVUhTcYoF4S2ZBnKpnSFYUirpFq0weDhhEiuA0QLeqZF4yq0wVUbvmJrIVaFvGgd1jUQ7iNIgDUITf6Y+CT343IMQGZCWAS0WhGI1ADLeilb5MXigWNAyjhVRNeRmFChPg1cA0vmTLpkPlft6uqcONZQs0GiIaz+uhDY4+LegFqyGzKb8vEA5CvGDpmJRrFH1BXlY328y3ESzgsQhWdSLBWGX1P2tUscArCFE6JQA57ZTQj1YjlaIVrEi8FbnEjNDCCtiG0Xto14npHOpzGMQnhFgVMm5bGWIetSK816IFVOtmPcC6s62Walki5K59LA12U6uolGcOMZQQZtxKgNRIPYQY/kS2lXT7kjqIpYZmRciYZQ4gRinerQMQFKDhUAR+y2rLs3HPbdwoJVeeO7NEKALQ6yzIMRqrjvcQo2HIgZIm37NiEXp5dVGGuEnLtRYLFzpBF4SyH9qk1TCsYJYBHAENab1ucklu6ZWRq9vxwYmrTMhGgyhzJLFk23gQnuoyoChctQBiyuW73IrGis3V4wuoWEywUhAyYtl6xa2UcbblnRmQY8VMM8uPujkvJ9Voh/K6jBjqtRNWb/RU46iOjNCrQQlWebkAJwKXjVuthwyPCvtbFrVY2zRcCBQa5shWQ2KWTayBQ4ZptS1vx0f6Y2tTjJkv6LmMIRn28te6B7heq0rCGIVxxyUzLYsNPiWIyp8D+QSjNhJydbglR9Wqt3WtxldyBdT0s4YTqntMahXi1TmnKqG26ZPt8lHBaWtYsBG7Lj+O1jIEikRyV6lwbVFomFEp7iUNyiBMOXBU1EoEyvzZDlsWgBk+KyrHGoZNboEojNHcobwAMHE7UVgTRsO3e45UoRbUqqpU7LEl70l8tqzEICyEbm7JUIz4BhqXmncpWLwmhA1kre4DkCyFS47BdFIgkrgkoEGkOhuW2m2pif5FtAqSWYbFApMco9FnOiuCWAFpyHJVv0bkfzsYBaPWLdk8Bf2HCG7ffJV5N8fPgFgetK1qr2qsWsVFaisgYzjRDweEF0yKXhbVqnFa1juvxisY1xF2Wg3NYlBqVUUIwLTlZCMqxmJxHEQjdcCRGGFzS13jc7m8I4slvLs0WB6gNKLII0xqIqUwG7Za7E2kRtM7hAiQOt+XBuVJAbHABhYedkkrmisaouBrD0kFVl4EXQhR+YC6h68EnsuseqcG07trwZKmgY8jOLOUrG/PgUzHYj8KK1RifEFV67Lbd+JUyPcaCLFGs4Ml9YpWMNvLoNqeoNLsGnGHS897mps1F6hWcM0iRyqWFnerat4OAw2+4VJq4WCEjgusQLQmY2cSy+hiBdYelRaLtfsGqFdbhlUDp116a+LF6x/utAalWSM28hFd3nMes4K6hkLdRca3iWXMJGoSHCyUdviTFl5C4gcw8mm5+KJa7pIpot1uUFNVMwg1UplXvKoRfOrgp20Eq61H1uCyqkRVT0c9RTwvDqFiYY81etVwH8kCg0iDmFYLHrvdZvP6uk/n9XWz2TWB1NTCTQjfVi2kWyuLRGMEvYZYwpAiXkGoFiQ2BSV2PiHbdTwq1a30H5ahKPumbKCPVSuco1igjxWsVKiAQqlwO5gedFeQ+oCAbQz50imRTn6MqkepVvVa/CY/K91722JJYo2o1TAyO0q8FQjBJlIZLxiMpDfYsqdgF4mvfQPVOq/17qwFozax1MyZtWJUAO42PZzy2dhJEc3KbLMV5Saz6pmuz4foPjzaN1BmoZpQlziQVxuA1EcTrp2uFjliRLWGVkXNL3dFF6qcZbTAAqmaQMtUiLY+thZWOwmRTbDZYBcyW21mRa+XmPXG1hnEQrvuwehjwS1Iy4sCrVoRgQtU8/H9K5B6aU/Z1hvzWrhnCu9/t5JZ8O594qovdZlzo4tVR10gAtedmcC1WcOtzkxfLXzTmdjafKhuvY0ky5PlNdV0r+vqiljSLTCB+ujQ6oTUAtpma5LLphZFK0JmRXGHn7M6pI0xhWKWoJYun1Uu3AGIWoI17+tzv1tDLcCtDrN0M/6S3jtetu2PKBpytQ4kwqwUhsfnvN91qmqwASFUS3WYYYv5/IYDz4XsItUR7I2qzihwWD25mo+ISKTmfXnMGxaJA7JbzbaWurqOd0tXXpoPGw6KWbzzLprJyjeoitD2oW/TX3L+vM1F3uciW0cg547MQ9XSzRp7zLNuKGbd7pet+UXLu0s7ClvIBq9mBlQ+0wuR94TU8WxAUa3bNe2O6QmugG8gdZOdd+VJpXGAvOpi9fbXPNNc2DUn3VoicddQeXhxgZcFdWs43ScW+jWyDC4xrZceC/hRyawdgopH3zT9Wc40/Z0yXBWofHa4Y9OULfMKKHVLxZWdh97N1nguRFfv8ikhZ9auzaoE059peZ5OZddM4vCI1k6OFGFBPeCBfsvEr5ry4N9XZW8YUWZpcQf1IMBqZkhRlJZPl8fnN98SVhWuvTXzkZHIgnC0rVZcre6B3R4rNDfY1NU5vEOqW34NwZoEVOhMR3pNPAo/z2urW1OaD0jjY49Z3SlPQMt+/FI5cTWFGH/JPpbujWqsCFSTAdTxWwu7ZgnXpm3l5XXoLAzN8nC9avVuNUPtO7pRkW68N8V9ZlAZvMrfm/6+zOLsuDeVqgVa8YaJl8veK+rDgHf9rIspjNWikXcbyJEziZeCFcPpUB6UX8uT6Pv8wgNxAP5hMIeICij1m0Jce8N0zRVgHWI1e8kbAyvBqgPCazoB9kZ59QL8g5pd8Dj0dhxGuSCyQuZ1YVhcllfM8o2lNW3dcQxOkybV8Xk4CLSWx55C9fm2Jh/K3Ull4o3rW1cV0ZcySy0Y8VS4sbAqUXhIaNHD4PqMw32F6vhOG3kwzme3dTM68fA3YHTsA9gGsWwWuJsFyYV6BQvmwUmqFT3vJ6wOlVrTtN8XZn0+GLWMatrsarWj0PW8g4P3aoXJUBCr3O9DmixFrJn6q4nK1eFAiPV+eP/vfWHWgWfFj8ysBS1uH+TUtbkej25JBq8hWH09tL2fDK5UZblQ+nfaIc4lDuEVj79PqN7/W8AqzMopcQnElwwXpxbKhvaUR133FHEIBotWxhSsO4lGzMLe/VTlpTyY9erA4DpC9YnVCSwi86eU+HESrMSsF+K1pHvQw2nfQisavz+rcz00D8EABjvQNqhhIYHrNY8cSBD+mZBaLVAdsfoE611o/JFc88cJqKRary2BR1deeNylgcwKvVsgurCSWao9A6bQyryfOi01EaoorFj99/6eMiKNxE9q7SuvFmrpjWZe7wymh29dgdi0WcG6l1FAzRmAlc6FNApnQq2JCxbRqxNUBavCLKpb8weB6uVlw138/6xdi5KjNhA0trDAD2A39v//ajAIaR49g/Ce925zla2rSnVao9a8GnTIc6VlHEPQqVVRYa1hllqdkvtmjLLOhZS0ErHuAxFYgFc/nz9luVVi1n14UWLJc3ilDVuLmkHZ0h69pbv66xBaB/S84c+ZPFFd3OIJPZaagyLW/amhWsBK1LpzafpYYvxMKnUOVQUR7X4ord7kIB4VWnCpmF7ggJil5gPUszBlhRdubdFd8iqdwd8fxKxniVrTKzOr4efw6icAxfj03rrJgzFLFO57Z82FeBjykHWhJYdGRSzEK8IsciGuOv79KFCxcyg1vKZWfvD0VdehobQ6HLPw4KpoctCVaEGty7iVZbaa151fhStaCiyKFsHr/U4Baz2L7H2o3jwKq7j74Nkfbf0bs0LUY6pMOJT6DJMNOccgebWC9fsUWmvNPby2q3D5OLllfRLbI0Kr28lmfcOsfBlGLLNua4Vh5VbD6l05vK+y/fcHM+v5lEGLfM47BURrZlPHLL0h94Bw0CumEbNCRHUd+tbZItbn2yRe0E94BhOxNrT4MRxe72bEYKkEYACDTxVC66/Maj1mwfJqAWssNdJBnMI7ew8qtESQT+/DNzmFIMJfTVGqMlqtuVbrL7chb6ARve+ylZtn3y9rzm5llsi6ryGrAiuqtN6vwquJy9K9LPwhZp123tFVT0PQ8gekA70Mx1wk5aeQ3oQKrAwYjVofoNcIv0K1pGluVqeWnEB0MlrYoHQnYp28biNQjA6obs+C1tYBs3yYcNgOoYIqMatEeSbiH4xZzU5mOdQxq9N9R4dqOx6zYr0m3YD6MKsR+YZ7SfbZn1/FrJFg1YCyRY2E92/DneIOdM0GOyNVkzJoNWLM2pCizJKHsBarxKwxITVjNU1uOSwaEr6Fo4d1iwsOM6t1uxwYs3L96rGCNZDbMIV3F6tfHbMemVczYCazCl4Vj0PTaLMuPePFrAqdFRRYiVnsMlyY9VNPrEU6LGBNCa3Jvg31hLn5OASuYm5W+Vtmpf+mfWZ9yAWY9dzh1S/TpQtY43YKp4VZN2+srpZZQGn57aSnqkSpblNmxR2p4BVYpQCWiMUfOvJWZEpr1Vk0ZjWovVTEdzD01KPBw1pmfX8bgsUETJeytqqBMOtZDuL2QVj9ygfPR5ROiVgTA+uKSocV8d1glncbejFLKHgvwktmXShYU4pZVGj95sTfr4EVi+/D61WiezNdrDJr6mgDqwv2k/DuOq3vb8Ntq73GC4I1rjpr4MksA6wi4GmAb+aHdA5Y03TxxzWNU7jzNqyr7pyqbkM0iOIxqzSivakoVaWKX4WVTtHMMutNTuEk27RYxWKvjwYxi5hr+rdh132VdQCLHApc51JBHps3b50RaIH0n6yHfULWIxNr/nZ2mCVr+Plt2P8pn7XDLObWBPNZTqZ0xSmfw2HQWD1drGTIeuQzOFGw+PIC5g518G1Yl886dd/ms8R1SOvRpEuonMNBpkqfbg6+KIdxju8Eq2lnxDy6t2H7ZRsNtGeHrQ5qMho/DYmEbzZyNQtYd5XR0jktkYMvzHq/3ileKeUg1/nEqqxDp81bv+gorX4b5vsQ5x0upJdjfDw+51AxS2RqZFaZMGs+hRNh1kUKeKe9W+az2PxOd6T1b+82tKs7emyHJ7TOpO9lHF/pOhQ3ImcWK7GycsXj9coXIQ9ZKumQBwcC6AupysEbhTCjS9JhVqvW1rG+bvo6zJ0Jn7L7u2FKi92HIKd8FyHrtZzCErJuTns3XuQDhyy+ysGfPGsPZ8tmcIqs10uTW/UStViDpEprUV6JlqOZWONEPhen9S8Gc6kdYlZ1923N9ov84okMML2pZ52UpC1HqX48LnC9HyuzBtKknNDS8Uq0lS7EIsdwOO+MtMZo7fPWFelDvQ6nzms5kvPRXhuNYNatSU0vC2aPF43wmVvbMfzlvCpdNIhY003pd71oZWcGv7ME6V7nn7MZ0W5WRhktXg67lKg1I/Z+J2ap9LKQDLK8Oj8LV2IlWk3DZX/WCTfh9nW9Dqe6WpgYy2zFshAcs6xu5UKthVyvB3vy5OzD75a/UmI0p/1eDaHVMJ0lscBYOWqS1K0Oag9GV2GrDeSDORPWWr1sqsIzllM4fx6vZoAy/oey6kkneIaE1aMANf9q9PQv0O9W0sFZaHc6rLM65REN++ADLLSKfgfSyfGRD69pAB24Pz98FOXJeTUuh3C6F7jOvnw3Q9buxlIzZnV1phViDY0btJTSurLGl2aOPZM8iU9CqYyVCFgvEq7mb40/zeq1dvshy8uTWprU2U8a3aAFWmk4WOPrPegpp8/nP8AqhVWCi63xMZuz9he0yd3w+15reqtDbzfCW4kH8z6U1JrRmgBad8YqyauxALVQC03uSJVVM2CB3VqPzt6bGS3Z7rBXO9wuxPJ5bCeRxq070VV8NPOD1WMNWMNKq2E4X135DvbhmtUKkE/e368C/KKt/aTaEROUeGjnkaDWjFazSHmElwjt9/FVLsIhYXXRN+F1Z+dYawxnOttd61bYGeYxrTudGUM1tT7cknoLz98vwn09g0Pi1fxRkyhBHkHRTGqNZiLvMF/Cm6to9MI/9TqEQxaaW+e1erX+/vzjIwWaNNHLhqTF6P2sND6x/Z7l1aAPIZg2RI1s1uSOuAd3VxVYC0M6rOFBHn6HWp+DOOV63/2D13sjF1xVsLa9L7R6N/kADiuxLp4cpcwKeyoL+bRWLaPRdrXW69C6Do2W5TWvlaFaPvflKH7gQkcxzeqM7yVcDTlWrXDxQ8h7/qIxP2culAQbJWv3broroVpzmFXOlIP1WTdSSE4kW7FoStJmINP2s2h/J1rdS7BajuHtyjWWYWrhGn+05l1YtTHEKuJrDd+ibKnfAbigNamvFY9HDl7la/nJB6qhyIVpxeu2uzg4gkY2T2X1cmfwQWaRYdYOl6X1Tqjgb/w7b7VR+rWCMgM2Zpzu4yP9yyZpqxKtBkthXT2RlSKHCFmd3oF7wCXFX+nqbewGTW0BrGabSB3r86f7HLqmKUGzwEP+OMuF+5Aj+waVvAjBhBNYcSTXZ7V/83ZCk7+9cvrAm5WDM0onuJVQSmW/9ddMnplLBablYG5SQdAKYCX1aASjhnZHiF5xVBOxOlC3kFuoZVObTj1E54W4ncRMrQ2qhV/zr7F5PMbtvXxf4rqg1SD3dwf00hHpd28RjYKrem93t3sOW7sB0Njdo6N8ZlX6NBs0/EsDtd2DgFdX1yalbWuWZ50q5Ls3wCNctlsnURPgBh+w3LxhSJkfIqwyXpNlOGCXofUqam9rzwEzW7SeTe9msxc8wC0remPwxYUo80kA9dHtN+j8EdQAa3CXQSmgehWzqv12gCxFQ5o8qRVo91FUPk4icDU2VBtcCSN6Bs/AWsZw8IvRO4fwLjzizNqxSuvJ2CjJ7duj4RKmQrxeno8pJc4eRUrTSrdy5yVjmljR7I403dX2t/656kEus1MbOMGDGqgtSa6BiHT6qqGf6exYFV2dor1eFkwfhWCxcq0tpNrb3fONknL9JnKONvw+AoQr4VMIhShlQ3WFLu8qk9US4769NuV6bxS0dKxnlelWbO4GNTF0GLFF2LnhaNGAzvTC5ux02/FXi75L0e6Y4SGzsM4ateDdNO5aSdgVbxmqnS8plJMrUJ7Ay9mxwNKOTiK+b5nKdu9dSDZQVzqkoLZlKrVauedcJ5iBTdE12HDdzpdBKk/yaSy7MF3PoQcwmGblwD2zBzYDXT23zHwpCvEtUqbaM1OGrRvFq0FATcxY7YZ4dUVWYVozxIKTMJNG7dz19oYdMkix+mkEWoHOAVdzKx3I8+VyWcXVrO+ZZ59hFBb0np5dSwa3FH1AvZuWrNogRUr4/Z3UtmHYl062QftvwwUhyrxCqPceZByqTYpA4aLHLtJmA5Lad34lcuuAb6b9Q4dXurlBmbL6hehjTmEVpn3sgah1KXkjRuDh7vvW3vZQDJ61oarZi2FMNdtE+7K6L8zcOyNh2lXGeJ009Y14Dln+BprsE1mssHcTtp567w5b0PkJ0850c2/9g8jLPd9ZbssjeIVrp62bUESsnMlCvTPdkWNIzAkcY6dWVlxBo0gItZH+AFLYnzw4HaRMvLe4pnM6zCrc0Ya7AHepBZdT+4K+Bim92W/Nu+sBsMgk1s5OxC3d0B1RWiRXY0atVkUtuIAMa4hvyRVYL6RVUg2WLTJKkALTzGNQoRxgrxfiyvEUpzceuIcdhot2jKIHIdoc2ToP6B5qrGMRSzbU2K3L0tPdWOQN7Gy/iPXBPIPBUu2ZV1ZuBvYnn76MWr5/5r40pS1uygrkAFwB4MRrz5BaLU+PsmEd7gBJHD6+iPBGoO+V2BLUws7uQZf182m67klV4rZwDeoIlm4ZfQ+CLJb2Jer/pLH8Xi21A1DWEcV8K61R66oPpxe7IQNLwIhkjEghg3jVKi0qZAMoQ9dnsZy5J6vDdKMWrfWAkwjLYwwuesiuxPFWHD9EKRGx9EUYjYFocQyrPQ1rPS3AdYi0KX5T48dPlgBX4fnO8YOnTxTp4QbliB3vO6uk81V097ZLdjrEm2bJQfnMQIoJdhF4CogIMLLwXT9zgOG9mXr/E686K/3Q45wp7ZwEG+5EUd+Ai4kw1XKF1AJcCVIWc0cOVCu23fawcab7w10IYryGq3fkFgn1xlG8MpjQz64oVpGwjoo57CKUchS6znWnL6HKshQlH7iTJqHW9j/TkvKokmGkcCxKASuP4OaQi8IiLqzKRa1uosI6goSVgl9qyBxJeZ0MFItyPcDA6USBXbVu42q9VljGRtK/RCySjPdXefOaa7SlvAxeNXChRExx1Y5SM6TGIppEBm61gFmmx05tHj4fRlnNV9ajquwKRgp42ka/r2ugYgmGKloVXrVG03uV49wBpeWbJcvSWMQ98qqqGEM4gBg5fSFyaaWtYqLFq23ZheGT2f0JqjI4TallzPPQpm+1rDpSSRQD3ai4F79KnBO29s6YKr8JW7Mz+dT9GSZd6UG1ns6P8m203z76akQco+c10ub2ECNgVVr6QnllSVHUNvPtw1DELlHR73VT7hble505RZMFpCGBnC6qCBjdYvHQYXEv+pVnkBvFPveVvWtH34eeEzfLx29aHtgg8syNE+8FdjHC7kcZ1yPQV06b3+nfHMVO9U4addcWI4YSXECj5u63CJgV2e0XYrSCVWRXoKGvYAH6H9yDOmKdPG6l2xlpeXgM+SL5WLKpUfOJf9fBKpIrkF6DZsIP7ED8J3h1/EF9kukHcRRVmdqwBdaBnqpL/lugFqAg2WK7zDK0rmVTd+q6f8Ys04kbXIk4yKMshA467GoE3BJ/JXgitFXViRaXc3Llq/v7TSie1No2uYfzYr28j6Je4ybOIpeXNFlIM9Mmp3Q5laqr1kqNEjV6Ov0bqGSyxrNN1snTaO9xEyKcIxJ4Vgf+BbawXBzAVh9BbdrEmPUPjyHsjv+/vStJchuGgTgMS83/fziHjEUsDZKSZckei84hlaUq7jTAxspKHwOua4hRSkjbdFCzia/EVpP6zWL/a5pj775Qq5/0gBwJGI0RgyXysn7Hyeexo8LtJ61ztY1YASin3PlmnjAagGPuQ17sqWQjWSGCjW8w8gAABYdJREFUSyXnU9A4Jj99aEkouCWBZYcpjlJbuoESg4cunOBSrneSXEOcikovLMFjEXeVtYC8wsPDcovVxyoRXD5ro5vmN8HmWhiyAo6WDKZ9OwzJ4XDRkIPmOnNDUJ14rt/nHAr75lvgcn5q8SVnVp6oeZctjhUOrtQTGmvsPOLquZiKGPuuIbOU7S0xHhwVclQbiNPuOBAuWxrrDGAUo7dqSEW0QmzZBRUR7LqTYeCv1qbkA7KjE0Ufmn4Irkvl5mNs/UijbqDXuhch2p9ZmVJMhBOSfbJxTO5JIc8WMNte5roGZa6q6KWEvtlGQK1LLIgEdQ3ug8UpbUoOL8NLl3y84yLMap6rFipS9SgL82PFfJqzIqSqNIVcybRJHJN7LbuyrsA4VcAcPcNN34uJtNAJmCWVCoXyim1OkeNk6GDINauPAd63ajlfY6KrmVRkVrHxTOF+iuYX0lvQjtfj1ayyK1giu2qGly/DEqIs9MMx0ranseq5qzXJpEUDXg5Xq1L7gcSQh2gdgpXGjFwuldIhE+n4oEiBKqxnq/VH5Lb0pH5087rtrfZhWdQPE1iyYkTp6PWkEdL0vLzaDnVARR9xYOV95b24r19Kl1ylh1bUC+k+fJzhrUgGguW3lEAN9HJc6JBrye2yRTOJq7JdHy4tCpNukrMQe3DLJyGqyQmm7Cq6U6luItRqy1yDqhWtyatyZ4Hk6xdZq7xZTxY8vUllTpibkpw9T9XGLZPVv6oR4QTfjsgtjpeWqAiAsS+snL/78fhZizMtUlGyM04Fb4VzPBbCWAF5YOwhUaPqCoAZo/JYqWbQUp1Q8GudqgpSwUeYTroHecNpnmqOvbrh+5X4KVFp1DKwvdXgkdQGEYqpVxwkjYHqUoT+JjWaTglerP9hoFtesZ2jDi1cAVXbb4Mstv6FK7PGnGM9QhXCq+aswo7kwVu05wKWW+LDJii3SvUaacLsWCJUhzbdDPJJ9+CoIUkQyVW9pk9INXkymB4myJSVfm4I8vrIeXbGh8eKagAySi9Ks83H6XXSUmT6pi4FShd2ueeC8rs7wOjAqAJmmryy/7iriQU/tZIiVXlR46ljkEqC5sRd4XpP32QE110x9bwTJP3ibEV6Wp7PsgoXKi1iigm7rN08Qyn1AhPfgWWa22GAwlUmaFPzK6lSg1wjt7oLr8alVfRSQtnc1ZWKQWKLgCrBQfpQadup8yR7hOVQeIM2fIRFWEhC28vopRIQ0m7FGch0Ciql0n8/ZWJ0NlcStRX08pN3EPEIgwW0G8L03IQL36XtDH6+SUC5K64X4PXCOx3YpKDwBwsMEaoVrZ07zWmPBCODlOl+1DGsvInbMnkIoX3zAS9DsfwDc/+N8VL3jZxX9drqtuw/UakuSSLsAF2CFfoA+dUMuEoiPF21lq6BKYrlf6b2nFQcdYbeHfDuaPm+eUGu6qvPflFSjY7usZMnNztdlrxRt6MgdWIY+fc5rEwF5X381FxwDbGqq2uQnVhyAqn1da93iAF3B0Lg24ePO+LeqYfgYqG+Oz2vOwVxLF7im/jeVoVuF6tNLx4Jl28PBZd/n+bkZVZNzF9+YpN7HwxVDLCh+kh2OnzX6ehIhc/FiAyn/dYYZSC9Bk6q/T3Z9DTHx0Dml3K44GQLndROX5df/2xquXSXBYwEdA439+vqN31M8wegEvJN4LsskaIm7ld9LHN5jfnF/HKQGffvUQPjmQ8OPkep7zJHo1db01SwQ8eykLn+cxj146BMdbPeT4vbRwv13R7fvlsTZyU9Qi738gWANRMC6+YVJB+fpPoGqJJsIZNlMaP4hdhIuPR9lc+68+/kUGJAGAZK8vWY8dAOQfHL1+M0ofbvM22b99moLe4zw68bqvvc5z73efb8A4fKpUpN8oxQAAAAAElFTkSuQmCC\",\"e\":1},{\"id\":\"image_1\",\"w\":96,\"h\":96,\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABgCAMAAADVRocKAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAZlBMVEVHcExFRUVcXFwoKCgyMjJUVFRFRUVGRkZERERCQkI0wkowMDAsLCwCAgI1NTVXV1dGRkY7OztBQUFQUFBMTExUVFRbW1tZWVkgICCjo6MSFBJ6enqUlJQtmj4tejloaGggYioiRyiik6JaAAAACnRSTlMAPO//2d2GZBeuhDYgPgAABupJREFUaN6tWod2ozAQNKEjRYhiDAjHyf//5KmzAglwcuuSvJSZ2YI0gG+3S1FEeVam6cdjeDw+0rLM8qi4/a8o8iwdhuHFXxz+8VDfPuo0y4v/gV5qbBkPSVE/hlq8PeryjxyRgy7BJYGJgROV0e/Fp1D7Ywcv417f0/xX+PnHMfidP1X8iiJKh2FTeQm/UihoE29SFOUWW+E/HPkGvpOvrPhbdaD2Vbhm6MTzchJFtoXfNHUbAl6+riVRpC68KAxU73BIXA0vkrjAEH34R37tLACXxbEsXdenpwdFLvpqtDvyhe6+p21P3do70Xf5Kb63r3WPxvnTxhyTVhXm7qD3Zwy5d+LrNv70xNhsxPcyjhhyU50azHyHP4MxUohuOIIM0XYs+cj0I8Cb53EcYxHjOM8rhQC2GfCIQvO5mxugfp7jXci2jFThg+iKMP5afC6/BV0NhOBALj7tqfd4yOrNWtnZ1o7xQXAKngTE58/M0+B6WBsrjtf+ErymaFf18o3m+wLBpZIfRPSsOE7MjUGX7/xrESzQXRIQjR9fjBlJ7TxkApSW2wI9nH2KvCNf1YmY6vSKxJ3V1Cxlajmjb+PzuPdWvojUTcBZK/vz7uL9j9o71TVSkTsJwH2kO8dHX54fvnoAz78rnATADj6e6698BOhlwWnLH2sKqbMDkgv41bdZlWDQgQKKNrGLnLMH0tP+omdV/fCFb/8b1q8Z8DCDVDobbHyGjzl+NfsWv5gyWRzxFKGPhWK1N5yCnhZI4FcBDayjhkI8VJtzaA+68SyBL4H//Jy9GiiT0nUGbW4r1Bl7QM8WCIlffQcI4qVvtzUq7tp8rAmMZ/hhghcDCbSyRrljDU4KpPGrMUSAJwCvapRB/OY4AYNfzSGCmD0seqNqlELvMR52wOJXwTTxiwF8cawV0NkcV2jFfwYTiLtJozfyrbhF0Di1Bwngb4tffYUJ0NQr8ZKhiUSPtXr+hg4SAPjhIRKD+pLyG/XMeY+B7xvDBD8Av/oJE2DGJLwiabJbCY3fHJwhB/9giPgYLToDSVLeSujLgi1w8aujY+U1KXT1lvAp1fo1gfc/kYv/PDpWhqkBkdzuwJS1AQJcbeIJ4nvzx/XUa/kyhVu/IRhP9bvxtd3a6onCFG6OqfQS4CP857zd2uqpW+FJ42RAfQSH+p+iqOOmyfcV3magHNPdM0To+R4+J6AWnjTk1hl0/tVH8AUbuqvPHj9mkoCoB2lvKTWmnrOMvsMAnF5ewMdssur5MxEEyhYLBuInMDGe48fxNBENzt8IP5KV29Ou2EvAT/lU/FzAJ9OiCyQ4OEEGPfHr2LB8nePz/YAR0wCZQa7BZRqvC47Irnf+BY9Ng8aWX/JbBOwk5b5xvkYwy7Nm33Yw9QpbsUS3Ajpuyg4JLuC300JgcN+SQs99SIDBhuM9MZcVYka8CGGwS2CI26ELewr8A/CDzm7qYALCtuQrPLdMywVX9C2WLP+f1bBCCBFhvAoLLiwZa+Zz2xiov0yAGXT+IEja69I6bmG5l/lkiL7D9eEJTK0l4Bmoc5ycajcvg3Xj4RAd6bcJcPk8CFL2vYBute2WwyE60q8TkOiyREifZ5bQcDeMHQzRoX4iE0A2A2wuJ0QwA96FIdjjryP9/BhYRAJSv3zZ89gEOO6mrScSIHge4r8mvgyp4qhI1hNxYLhFkRbkHaLneITfThOTug3+moBuc2NMN128DM94DtdfNGBpEIwEXgxR6o1f6idfo/F4gt8C9U4CugvKEEsWzuDJYRzHgwGaKJSPnQTEIBlPr5IY+JryxqUigT848AhvrtqV1hArS/kWQ+vgY4lfbi8KUn1SYqJfpuEi/jA59cGcASe7K6e5A8+DiuPyAnzDJtlfUxoBj7Hn+nUG0YWrbPmROVySz4gtjoLfFUgWKQHwynYMfPbqQ/heyO9tZRS6r0CSobXoxppR8f91sFDi19OLCHCAHsLnswpdsd65JQbzcVC+9vBfNWt1bARv5PBGEwFPCKBoGR/CaWEDtcceauuX+KFSL6Ft9f0NXhu92tbVe5BOcgiahbHFfM96O/ZAPY6zo5s4mTWVm6ADY5ONhb0aoB2oj3F2chuKmAIB+6F2cP7kO2rftmoc96UX8PHp/cYocaUju4MjdzHDjnKNjy7cvS4Sv3pIgd2x1+LjOLl2MzOD2slePdprx/JieXn1jm+eQHyCPNJ34nGM8nduWMvKELCD69UA7bULAvyGfJsE2gdGe3QVyfufTciTTXW2I2+0cw6U/e5zCYkzlgjvK6/UZ7/+9EZeAni0rY0ezV9/cEN/eiPZlV2px38U73y8JdnAK/Ay/4+f0ImyskwSIrucJGWZXf34zz90DEqGHoD9sQAAAABJRU5ErkJggg==\",\"e\":1},{\"id\":\"imgSeq_0\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,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\",\"e\":1},{\"id\":\"imgSeq_1\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,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\",\"e\":1},{\"id\":\"imgSeq_2\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,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\",\"e\":1},{\"id\":\"imgSeq_3\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,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\",\"e\":1},{\"id\":\"imgSeq_4\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_5\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,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\",\"e\":1},{\"id\":\"imgSeq_6\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,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\",\"e\":1},{\"id\":\"imgSeq_7\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,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\",\"e\":1},{\"id\":\"imgSeq_8\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_9\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,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\",\"e\":1},{\"id\":\"imgSeq_10\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_11\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,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\",\"e\":1},{\"id\":\"imgSeq_12\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,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\",\"e\":1},{\"id\":\"imgSeq_13\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_14\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_15\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_16\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_17\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_18\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_19\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_20\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_21\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_22\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,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\",\"e\":1},{\"id\":\"imgSeq_23\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,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\",\"e\":1},{\"id\":\"imgSeq_24\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,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\",\"e\":1},{\"id\":\"imgSeq_25\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,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\",\"e\":1},{\"id\":\"imgSeq_26\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,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\",\"e\":1},{\"id\":\"imgSeq_27\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_28\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_29\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_30\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_31\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_32\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_33\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_34\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_35\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_36\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_37\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_38\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_39\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_40\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_41\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_42\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_43\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_44\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_45\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_46\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsBAMAAACLU5NGAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAD1BMVEVHcEz0//Tx//H6//r///9lfK+tAAAABXRSTlMACxtA2pNmue4AABHQSURBVHja7FxbYts4DCRuAOoGkm+Q9Abt/c+0JoEBhpTSlWw37YfZbTd10miM5+DBlPI+7/M+7/M+7/M+7/M+7/M+7/M+7/M+7/M+7/M+7/PlkfsvxX/yr6DSI5j/GqbDl/6GBsuRcJpG/xoiTQn5L/l35Kbz3wic/hUwUn5r3vKXrCo+0pSMpF5N09/nmRIC08H05Ssx6Tfr8BCwHoH741KTOXjKIBD6m/4uiPwhcbEOdSeqRKbfE8/0yFTcJzW+REb7L98qsd8nQB30riSyP2RYcmD+KggWevB5/SY3VFaaC2DwNmFtTv7xapS6M5GMm4MJcZCQI53+IeYX8lAoUKFKGI/MHvE/Qe9VQYvNpoESkqgcZqn4m77cqlh7ZlU9HOVHiUo5ZsxG8Fr7UhlDJ0dIe0EMl06S0dkeX6dAyW8rFggE0UElzQz6LDL8S/meDC798SpfRZAeMPQ4R+hrDUszu8mMQcWFNqppyITyel+U6bvZ41R2ynFiD9UGNJ3cU18psfsjFdISNyU/xyRBRxSyF/4TgI4LrwaJ0rDuK20NpxgZ7UsjqsCx4oEuvn5K/0imTEihQuX1IT7ebjxG3BmBgOJ8SKuQtKZvJc9qUTk6hz2705G0zMIGJ0hA9K9ersHRERX0ykJ70DEZ/JDkNpVLL8A3vmcRBFTpwjKMJq3+V9mHLU22+IpAz9bE4aln6ih9wvT8T0cYeISinXxRFDzMAENYIa0mmiokJ/zppk3qk70r6hNq3LMntYjlATR6NurCs88oXnF+mP77QpqlFLG0PwpPNOm5uASxK0KcxystoxPEJ18HUbzGUWAKcZl2DYfapzVwDSxCn60gJX5rWK+qw0ljgyBHaWkwWdiW6guKRdkXe2FEPWoBT8JRZ6j9fy5KhcJ2DPFpLWrIo0vOzahD8fDu0Dzu21ch1pogZSh2nwlfWsayXgpBaB/UwGgVKj5wV3UTFNi4Rv7Up3QoRADciIMqqCTbMtkBs1sezMvxentV4zs/E7h04nEO6fA4T5SItfai8x2lwvKpakO58sHjYGQkJpxKLoBQ6jqE+cuzqUeILkhGG0jj6IBTg7qq6xKMJoKVylNaVOp3axrNgQ7ztf4Viq/l2BYCfEnNKNxecAHsjsarmnViekbEPJ3Y9ANgqGqAlbhLpTH5fyy3UGeYHjJBofL7qYCqQ63j6I6EVQVpMNChqIXduXO6tJ6I9dxmgPWmsBqSKlUrOJfsLU6S+yCmyr5jcrnBNrUZJd87gDVMd2Q6qXLWKgKsCLFpfYbPQFpIbe0R3aLa7/ZBF1uVr08JaZmsh9bg5eaSDn0WLZMTVldjV6VICmwXQCqYGUj/w67ItZOXePy8DqNLqjZM1QXXPyGkOVI5cbIy9oIfStTRSZDBDxsAAxK4AJisf4yyCKVUqckVG2M8SbaIKJikpJK0NAUWrimWJhOZva+xwntQncj4mQvxaLf6ereyapZP4grPrMkUvXpUiOsy81LSI4oGLUN0h7QaItNoe4GhhKV1EXsWgm2Vx4dmYEru45p2U0NM+LPjKh434stkyOaGSSTD/KVmiY6FqxEVGEmTjCExUCGwWhVhrGtVIrplQpKU1iOMXsiJwf4ibrm0EpWBksDRg2sk8omP+Vvkhs/14OXllEjhZFgHSAksdYhYa1/s3oAez9BRuchpgkVKUjkI4QCX9JccQ6hzEBdovXrTRB/gglRVoDlEuKQeHYFDappVxwffNB5GA8ZHOqpot2jWzmY0h8ewIqQyWNXKwT7KDuqKXW56T7JyxwolLnVdWFw96PfcPeR0SXF5+f9cT7KAmCNC2HMd07LccdUlw0THK8LS6u+BakmX1mFX9ZIeo1aGXdFD19WgJS6XFn2RGjTJbrQMpnutZyrRgyCqovTIde2g1gEXRCXQHCQobPRCnTi5bFjZOmPbgqAc2UrW1UPpGGotNQ16nIPoFeYF3ZsfcgGmocB2tiawlaPqkY+aq0iU/WkeD9HTLK0CWJhVbZi2jgtqjJA6R7Qsj6gpLhcRKXVwS5G0Loqld7Nalu2OquFaWIVHecnsPvuHzxVmkSt24d1R2VkAjERW54hag+UjTUdh9v8VR7SfFO2QnvPTthLYGqjItr7ISxVEMGqohybEmvqUbL8bAXVh1bthbR/349Ja62+Os7H+NqtL6xKF4HmmN9js9xhKV9Ph7WbyOkCydFdlaRFN9X7nQyUGGHMW985fmuN1Hd5un5+f26G41rrYl6W5cXOuTIPKc5NW37oAP0KzIUyn4bjjuoNqSmSxDAGkecIaWRwj0XDya0MgmTYxFFRZ3OB7xunSclxbD6nroMEeZulFL/uNBGoJ1ny5I6IjMSUna6CWjuqXKbE9fR012ENIhI5edjgdjE7XaQIxegclHhQ9Mknr189P1+JaA5nZ1d36+stLSKuVbIqOrkefSwV/rIxFy01rmNbagEFaP83kDaw5Q+2o7l+wpNkJcRv4Yzm/GcEtJ01pVa/yI0nfH2vSctviON+ojrmEIV4QuwT1IvU4rlX74cNgNZlPFnPEe9zq8cGltZruDEPHFZkpyCDPq2gYey0tqnCEL1ReNAdrGto+7qhum7MIoFo8kjZp9sy0RIiQ2DKZdm3kFAl01WP0pEM1057TiVYPp4gPC7jh6uLcPDWZtLIsi0UFubivIdOCT/s+3o0k+tCV9LHdnEJUKNI0aBmziRK8R9HPoeIVctOTadobbtZ88DSd9Y4fy4m3bYtgvlan9ghrn5kwRbmK5Y0NOSspwTuJGO+NmEgrbltJuKrHKEPVw5o7RGQmat3MUOTC3kMMRlhaEbyJby2IAo7LRdlywEeX1pqBC6SZulxX1qIkVip0yj1u8MvmMWDZUMYCldOe248ebCEtpbLssZ1ir6OtSjFZweI7pHULUP2xVv0s7qQd1Xq3ux8/f36kbXmhEUPJ3J46lRSHRSbqJWSAsIqnK8/S4ep5p1uVVWrOL27hiTCtKgd2LifHBBGxvPWgVEpDc2uT1GYswTXXqQXqj5YEwhPVOyYRnuXSZt7UrxhKVw8QHdjSAnhQl4gQET2aJ97Dx0dyxOjjpO3KlcVKBTelNyekQZhUSyzb4ozPexIrTs/kWyc9UVynKypViidAydysdGDETJcIDBuE1UxrjQzu0BDXPDExscHeXsznT2hSMfqQwrPDSqZ16wWP2ZXLak1Ld3Nbw09TWjT8vDIvYGoWCzLR9XZpbcC1WbtmdVQeF5qTrkgDycSkRrnvxWdsIZ+jgd6FLzExdQKoFcygFRbQoaNaM31DXiuxLa4Uw+Yfn6BrlmOS0roFrurSsqgFCUGPPcoGMOF2M8bD52wrN+WCBLpxgT1YCjYGbzpcjIJRlgxUWdWqt2xi5+bg/t4JZ4wFSs1pnRl9U+KvXyimDZXJqivxo8sRYWLhOrGGI5q0zoYIGWbbueOXjRp/8h0Wng1cC5yhMVLE/tVcIaZCtJr9xcXB/82L4j0oIU9czLY+g8I7HXWeXI3dR1MiehDZdov9vKIXLuIJU/pM97LzxFFaxmosPf/siNGErjtUOjRq5Gob3DmHykHcckdcPRVa4dohf/4w6ueACVcy+WBMZ9Fodo9wyWEM8/bsu/n0MO8NkQW9EKN+JkiqsW2A5r1m34e4PuQUlNSStpVjHguZt4jfqA5Xc9IPcKx16ItTiZiTDGH/Otn59mEBOAQabpb2PpDt1qy0kS6948VtOB/mcaZ+bN0gV2xzk6GyuDxm1jQfjxAf6MOtw/Qse5QSqxp6duNAqS9faMs0cmLNzNepQYzIFtjdls3UhaYF6sDAZVQOdthONW4kGxFC0vIgsVg5mAOgxVpeWyRxHgVppZWIaOaepqa5VF6GNYNkzQulu7lbus4cK+d5ua0UexWXTIumPTnuHGdjROB3yNA7GoBN2xBa6Oc1nKn2x0UTJvOsQ29SrgfSWtdd31mUKteY5D64mhE1QI4wMtDX9auhhY0+B8y0apPhdBgK6xU9WuqJwevBlsFOicbsR1lx0YOopXq0NnMiV0tOrni7hhqVJK9xyGg9ChqhK0VS32Dd3+I6WY8BVxLU3I1advsPmmNiGqmbApln5Sb9sLKp56BJ3jyhKca0SVAPhnXLjNrqHejQ5wT6+Aaxv624pKLzEgRFyh2wYU9JYJ243qIiux8ccWmOEatzQSL2qIykVzavYY3EJuhpW5k99HQ4ldGyEleYvMi0h7QfAQ+8oacHNy5cfVOVh5YNSoldZC1xiwFmr+N2lG15HpkdVrqyQJyuR11c3ddBo8ILZdjuqe6ZcETdTaklkw4PerTkSuUDnCsaK3R/J1H5qh1WL/QI1bSIV3jdulz/MRa88KVlXJH3tVex/WqTVAc7RlsT3rwWnj9vQOMhco0/UDepDHMybGhhioPNWNqUio7ksLCYV4B2fbQLovKYh71hvnfheGjpyFbmfVfLYe5uREiQ34duAk5rtFrKuPxdUQhh90hSra5Zr78mYeF2Qnnmx1aEy/DdHvW2s+ZKoi+i5vp8IM9dcMGVtyxDZaLCp/pu1ODxuyvjfZXY+FOoEyuUIbw6Xn3A6sIzl/Y1VkdQncfgwOU1lH6KpdcKyaktOE81WDjQVGld6p5mo66lDOVVa+XqI4hLRKnUYXK/uFSp5aE1FvoBXzTLoviltCVMrlZziffwak0sMstgIZd0yMsJWuiaD/bLpqt2WETn7dRKV1cUU61nrwFmMYbL71xrQFRx20AHz8MqRvAGiQ0fna6anq6nZc9y4k5k3smowhezFHdq5m10XCnQ/9q7lmS3YRgm3AC9/2W7qEmClJWPI6dZEItOptP2qaJEQvwgvqq3GWC1olX8jFKmOcl5OBHFrJB2dmsiep/SrMMjh1Zy1zgfpRzW1smhwzR4I1JPVUVoVwsW04nng51q4iNoMCVo+L6DgDw1bLKcPoXu8/mPNkq9lQ7OIjcQvO26qPOqnusCtMl5cLlXud8hLYHXjxY0YFulTUf1nfaoRAvTHbA/k5j8ZfLAxCJoid14nRnTiVGp4WHAlT5sZBnTvB/HZfEFCumA6Bz4kKIPlx+/eKWX1gMt54sqJvNJy3BUf2wmilbvt/eLHTTbsujlsIS+dDDv0ufDtMSjg/XfYv5Y8IXrx1CkWpBEb5zAkxh5fvztHvAo2uLot7ESwpBu9egAlcHgIcoa4kg3Ai4qd/geNQ4hpnSu7jfUiUzKOyQSfGVB0Owz/GJK1kwHEA6faXs3zMJ5ZD8aYa9WqTVtM7xjZ1g6z06K9dMx+pciPMQ46Lg0U7A48JxWe6zFaLmIFdCnBqzmq+24fMDOL7mJUG8bzlDsTllZSLbQRhDNWY1Qy4vMAz/ZNc5vtUh+umswD2AFJkvoD0uVXsqEvBCCUjCDO7AReicUGdTjMYiidoXKfq+vqcp20pWIhgt2DNfJiCFSemwXErJDbxEyKjKy1NfRzRgSNamNVtV1Kp3cY8bp3zgREDVyEWERUceyOJ1KTjsUkiOvlBLpJnoElDn6w4eU67ZL4mpMssy+W5jlVi3k0eWjjnLvrOjJDTeS6XTybLdMjouxsfQwivIcxP4dEzFf/e8jlWxT3xPCF5dqEj5ek0YOaFNq6vN3UbIQ1sXUN8aN+1V8DNNhgbz4XAcspZDnZ88mP19itkkACCtHcBUhj5xrYPtlFtO9yzKF/k5W8VqK5Cpqzo+fL4XpnDE7Vvon5PQho4OzvNF3smZM1wB14/y+EQtvcI+kNaYsLCerUoyLWaf8Q5a1DIvl84kkLc2AcQhu/ZIAPlMj4KPf4zY10VO/hbj+cbgf5M530fcHB4sn+Xo5XZkYh0ugOtzbLLmMTCoMzvIXeNvZQv1qjhd+5JYc/MsclUX9ND6cmHXcEXAWK5s2CuWGYNxIHWaLFCKA6TNrwuBLX4rBOlFYmCKe8u+7DImToZsyx6uGw713cZnT4UmWZ2xXuX9Cc3jmWVfFBoyvbtWJDbGI7F858w/L8ZSz9R++L+qwI2dNB6zj6JdRFWjxNcu9mBBY3MVfAfFzS2o0Go1Go9FoNBqNRqPRaDQajUaj0Wg0Go1Go9FoNBqNRqPRaDQaP42/A4lGtUtTw7sAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_47\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsBAMAAACLU5NGAAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAIVBMVEVHcEy4/7iX/5fA/7+6/7TO/7/M98LR8MnZ8dXn9eT+//5GxDOlAAAAC3RSTlMACgcRGyc9WXeZ+xncz/0AABgDSURBVHja7Fv/WSJLENzrCB5LAsNmwMdmsJCBmAGagZqBkgGYgUb5prurembBu8ci3l9vEE/9PC2ra6p/zNA0/6+fWNKkr9/G6+vvkr8MNuXH+Avyl7n6AlPKXz8hy+k7By9/K4qikBTa6AH60uh7fy5WX32Jb/UD9I3VJD8WXPujx6yQMDEi+T6lAoQP/7vST0A6oUoa/3UGz6SXgBwbst4M6Yd1j4cYVY1LPJhsyKN/loru8LfJ7YVVMeZE+a+rdmLqjKXGsSp3xPFDG7FAcjooqZSXJFho8vepa6g3J7VwdFO6pJJToqySWwCNPaVUPL4T6g1bFXSlWyuqKLxpUtiVc+GADBmw2bcYEmFA6590O2GlQlminAml0OUOmxIRgtcExlP1Y24YRHeBsWkl4EkFZKRvqtAY+wGnl4ouF1TRlL3LO9De6W6suMKGTanK6reCxx/OrUVVRci6QNMJsFFw0JZ7xYj5m+ZC0pZidW5XFSR8RnDOljSRseWGohducW5DZr8KYF7/eDhTKM61VaDI7VQltehLzhP1JyVH1yI//F9dQh6NqWR0wUklnOw2QUQ0zIxAREdQ52shlL3bQ2TO26VtlgX+41NTjKr700odoBmylGrNpxtsw1Igs0RGV3GCo23bMS4s37GQ/03KweR/nVCzqhBEMLUjQIpp4R+Ur6OuSCz6m3RDm/C/ju0D269KRwtTfP6ndeWPgBlXleaRuL7Za0ktiIQQ1qrKaDJJSpTjau0DooJTRNKSm6VFVJ3YhVBVKvFzILPZYtbq09Etqh1pbInUiL6FLZXST5q6Wq6pyjhaBbRo21kGlZHpF5U36L5DnYP3dYn0rZ41NaV4d6MsXGUtAZODmhlEY2yk+yahhL5BwSVnLaFllEJVXoZJI+hLP1X2NJZFYUyLVP236EKRhO4Pf7BGUEiVxm9RMAU0xXkax5Si15XvVKlsA0kb7Kpo3bk6BeXILJIRyFLGwgnlRgVEjBsoLP2ltvV+szLc4hSpQ1FYbUG5lixpgvfUsI4BARnVzHbg7A+4FhHHqHHYcHybMbfUYu7i9UtrSh+tpT0qXLMZw/hPaTjkm74lxUoFymLKUVS10pe25vOZPguuxYJh9CiyKEzfGEtEekarbNsxUOUQBiqF4m/6VIBlTzKMEDwV0cgXU8QJ1V/k/To9t+ahhSkFtprnBUjB2KLgAl0Cv/+WqQr71egnzlDNHdkcgVwuV3P7mA7WUl7Czfgtl8fPwBP1Ccv1iGCGMMdarvxfxzQP3de42HyzR0hX8SQo4MflqNpo2Xw1qPxuCXQzaD/7KnFFXX+L6gb7USIX5uzMTaibz0FlMKuVPueMZXhFlpfhkpibyNUDQnSbliqiHWQiZAh9262UpXo5X/QKzUItTILdQIyY5Pqsg2xYbcN2NuJqdbYAjHwhN0oM56qa5IoK0P+XcOp3gkqd6mtUK8bScanqo3NM0W5eEUeJUMahhEjswuBquVz9bjmuOd1+AafHdFyuHqbWgyOh4LW9ASq384KjXw3+Rly+HyF75EVPPjFYmjxJZW+OeVkqbGEXjmQ19H1/hmtZ+NJNUvWzUYp/p1pGV0y2WgirRqVQBoLqiWweUdRSlXsRLd01c1SJkxFhNjS2MirnyjfhEsEztvp+WPcZ2zA4TI/jDFkIhb04WUnKccJ1uTqGIFLrfTaOYAakD/1nbQgzWACjS7Q+YvLe/8pEHYchmE6xNWTl5xnZuMoIeuJaK6j8oQazD/9iQ6TmlTisT1E3pclkpVLHWwi1eh8LK/9yxTOsDZKxpZ8oX/RVhBHqQs/YiPz+5PRPmSfyDoa2Xjt4PWNVjKPK7AyGy5EZqvxh3gDDANnDJHQq0KEzq0vUKxwiNTF8x7QoyAKqlRHUr8crM9ivXPcme7N6S9g8q+L50BR0cXbE2TXzIXIhN2HmyhCsz5exNbi66BEt82IqJ9npOpuPAzlr7UEW3V1/9/orUBpHRBFFjtU3ixAX/+KJ7SJa8sQjlF+VOywRwuxR/RjUpo5jX3BhMy7qDghyl0nlcpm7JiqeNakqC6kwbCEj0rW+01WQrQwYKgmvUjGub0SubRdTdb0B2ZBkzY0sdQRfGcxmA2Tru0LXyLu0SuV4MF0zRU3VUTyL+IWKa5QL+0C1WYMorlEc1bvmrG8kThF8I17e8/M7y8FlcsXDSs3f1doRQAveFmyNcA0lNSIBtZzxNnJVayace2uh5Vl6LHgFRqp8bXVttoZsA2N1upZe1msCYnVTDvBkamkaR+Sp+8WKBqj6EsE7C+B2ayxlXEYcBIYcNGKrkzgBnXQAJMVP41QwFM+0s2IMMwKFoojyurNn/gS4kIPcutgxCi+fXNX4lLN6Y8vS4TKUhYzjUto6KKzCF1J2FKk44WCqnZKreS7DkbfXWm0XJQ2LrCKsMSoVWAYWtuoJG/ka0xupe/10scUL76HYAVeX2SoFPLaho9qeg1JcG2xHiAuTEgwtq/s3VwQyNeV23ay0YfD3oZY71+4+gMElBtBFSwVdIuV+TDNJ8bA89BZW0rDZ4T68oy3kdb/d7Xb67gTXaihsaYXaxuxGRKbYvIxOW80fYkTjZA1ABRslKj5d9zRVj+OILfESoGlkYvrBTYcG1QPZcli5kIpU6BG8z2ju73cP+ZGR3QPXxoJolfUc4nJD7XwYKzFEu3wnNjxMCVCxD6n4sPbtLgPLmB70eb+jwjbwelZdFsVqAjcx9XBSWkbL3lto+QvBgyvzAuUqg3nwdf+g1JUoDtrWKi4On5l/GmSgC/lKdd3FmVZ0h0vrwcxKmQgzNzuCsrV7uKfqQ/TYit7/+FFedLATjgdSPT5CmsZGHFaFLA/hGFXG5WFEwma6Lv0P2kWZcG2w+paY06CoYfHg2dCt1OXuaB4fH5+enhwY1FXKm5qtLsaCUybhvBbS8Ji1bTl58Eq5r/RegXry5QIzydteHNQiluwXi7YiK05QfNTMMcQtbMEdkHTUEZyqx+e8Hh2Xu5fvxbq6qY40JlXy5d6qH7SK94cLourdHoKsvAsRwGdf+SPFpbtxcxdlRJlYLpirp+3EppwR6B+FQXxVatEezBx22IVPT8+xXF6weu+AlKxlYatjS3Zx/eDlRjS/OHoqmQdeihpr534Frl5eXhTVc45jHUSkRaSf2Ikx2E1TsqJPx1JRvE2PbOxgNeldJXjn6uX19fXtRXG5SxRxVemnLcPKaXVNihlzOU4pA5HeFW8RpGM5WW+2Xl8zLtJlQazrZqtQoS3PiZfhktJKn2ZEupYp3nFB70bWy8sbVv7ENmM4V+/pZx5sdbgUIc2EG5Z0iLjysCjlQ0/TMmWRq0yWgjoc90rXa9A1kjyPptpKW5NnlLzCaUPAuowfBle8uUMR1uvb2/54VFwqL6Mr2FoD15wdPyoI9NYXxLE6iSynT8qWSougMl2OStl6orD2x/fPo/OlexFGrz6PrVjNbeLUeuoQgocqHUtTJmrFtXG2CqpXI+vz03Fluh65FTG7YcUFtji2uTj9SNOcseX+4Ki8AryLED6WEL5nWB/Ho4kebPlW1MlzmQouRmylC3cir9XYmw222rGbmuTDHXwTquCVLOAqUdySrfmoPkXugejl4qQop2zNfSc6W5Z4lC26O8myMB40ig+jrbj6gi0GcNKUsmiLhbyXgMM62DKu1Bte1a0yrA+DpnSZuDyKMK45K66uPsK79JogX4Gi313OgXkvBGwhhiALqI4KyHEprIot1oFVNX/yorI0wbpY14xNHvVDiaGjetu/GywV2MdBMxC2Yszfam3xHuPlL3lLvEfmbvrrzOR734fbXY3qoHg+jqDrkDVf5+oVDMLPDcoY4mKypCQfqRoMsLUkWzmK95kthYUI7vdOltF1QFoMbcEgarYkXv92eUZMEvNJmvwczVgP19q6Oby5vX8cjpms/X7vrAVbd1sPYdX7FLZGw/bLbgs3vON9Mn9g/eBsaYbe7w+q9sPxkNdefeJDgxiap2+dsJW+erHgf9IVLyTqAGxZuek6pPWi+dnUvn/TLH1QkX0cYieisFlV1TzYalJ1Mnhh7kmcjOQgkq2SEjcbx2Wwjp9HcyxDZTpzWI9VrjY/xcHionQ+MqFPjEsm5UJ8xdYq2HrQGL6oL3yonA5Edcgga99aozzVQ4NZ1fpMOhkur1jz2FfaMrbYjVldqoo/IuMQVV7vn4cqJ1Z14Fhb1YtAp52vcLjlbPFYzBUPtvbvyM8FVbYKTdUPOrnZjocj5UaEy33CODDuZTBVxzURFDZr4spsve0/zahydtZi+dVKif07KghWp73imlPxXd2QNRebPG8DnrNVZsz0hzetR49GVm7FsLL43Ux3daMIbWEeWB11Ths140JgxZZfMbCduHGDeN5/fqiDfhy8EXtGlfOqG3EXbGHwVk1P40UaF9/iSnFXCCN5AKsbH2TqpwzrqHvP20PDBXQPdbkVA655HGTgXtKlqCJHlW5/7FvciVZtPb/l9KemkFG9PFdLpyP3tK1hfT6Z5ysYL65qcBUQtXxHbWFu2nuqdrbyTjxg+41Amd5LV621vBpENZnn62ymX7FJ6aSCcEP1wgbF6SPGDv+2d0XHbuMwkEEHkhug1YFH7kByB7Y7cNLBmyv/LBJYLGW/iyXLyX0Qk0ny9/BACgSB3eU/HqSvX8l+8tkzDPNoKWmxZKH8tnFqd3Br2bSO9Rlsb03Fabpe3PfT9Nd9k399eT/w5s3mQccrJUwwoPW2cGpnh4/X8nZ/PZ/yDSPXzLZwqU/zy9u6Vm3lDkSfk3zjdCkc0vJaNhWwxQwsDBDSEQVXulJ/+W66xygv3hec8ubp3a9GOyOK4woI1YtnIyBSQYr2VlHMp+urd2vuTv3K/qAz7x2IY86miuOigeJSAJDBWALdfLSESPiVMbdrrpNTv3Sfl535q7coFWyTEf5tW2IEXxyiC3ib0TvNrTchqJhPp/W0ZmWg0grebDZshTy6bg21ThfBPKMPCwD5bnCr9s01zcTgVxGo2+XiLbcxI/LwIWJEtqxkDkBWW4IwkNvOS5tcQ9xzxHUepmmCpyPFPOg8jj1BIRoCLjrNLb5Y1wTnc6PRnPFRR+2d5qti4dct2/Vi88Sz95l7tJn3NhS2Qae8XjR72exjOyAzyK97lrjd0iDxZxpvTiMoTBPPdus5ElrKpxhrQBBSdpqBrkmf4mgt3WvK9dMEMU2qJ4+uV8yqbT6mOZ6mnAIFgsVgZttcysoi3JZvrtRrvqpTKVaY7CsCaJh9h2hvCZLDS9QH8TnMHBOoRcSgBeoIlEH6k1y6MnBkRHZI5cMB3e/O9u0KaKBm+WiEAh++atPmooPqW04VaQjr8AxD/xztoq8N3dZYBvYJxtfHiQ7f4r11wFWRBxkX8gYLeAHaoGw/MJ8skOzBqzcfY7patDjPD3lUfR7PjygWXcAEykteDQX4tAGa30YrcdmhqKkresGFcFm08iTj8hiq0wmYygyKVTi/j6EMEPvyPRE1oCn4CEXLvkUF/qSYPAX9nDNwccjlMqHwWlCafVggCw+gSISjnLe0OzLAr4ujbE4EWcz7fTAgC2+tsiO/APkDbrYE4hw1JRJ2hF/mygQLVJBngd1KvRo9EPcTE5QoW3EpbBEgm9j5LeNglzL36+RGkdJxXR727IB1A9Qg+tq9PlEEvD4y909ZUP3O72VjgTfNyQqA2ONo7AffWe09WrqxJKzg1BRYeawh0G5Ad06ROQGgm/6cHNV81CXMdbxVWiaXFRcNyCJBCaMXzuhEHJSrgu2VMtiZvDqTU1Q8NG3r8wsRArcuIofonSyDYa3ksow6DKATpEUrA5WID54cDnxI27wnLmzXRP9207fcafJi+PexZ8fGEwJ1KtHfuojoTia9A0edLgoW7ouCBuoPx383edNj14/jeH6gFqQL/mBLiAYzKCLLOYostAVRH71c7zI9xJCnPQVsRsXoewygDoQcJoGbFVzh6Ax0xzQbo7vPH2Me4g3PWBhjPxyxgtkvY+ZKFMcNL86moUA0K09rb2Sa3BZMP3uckzEmrPMA+pGXpXpOdwGQshiWwX6iz8lUv2Rqc2HGYjj+IW3rTNLyVKXVO0hRjQWra718WCmyE4NLeGibS5mJh9yt3BklcewnrxIBIn2aQ/ZK8fuNExRbW0OxMVdYqCQYiR2itLYf3n1DJTFx/jJ/ZUhErbTT0/3LNhb6IW3BrJaV6niCEX9Be7XWLsrnIROjUspP+MrjsWevbMO3GizL7yYBsJSmRWQXJ2O0nesr2DIO6tex17XTI2dIXu1mpMmIDM/f/ELfqDuf228tDTRAMkiku9TCGvKq4ihU3QMnTTpnxbiPC2stACGCseKzaIbx4QvmMuIzgPbKvFck+C7rY5ko1QqicERxg+JZVPNEScJpHcHnHJDVie5Nn2Hnn6GUpbKscExoJhWJcpd3fWMbrGcWsxczB08NvLfAK5CFi2hQKS0lMlh3mrO02F3TtjlwwEqqd+Neud4IRJxWC6AILtY2NjBqPOl47EAWnvnU5H1FlQOEF1yTlSc5azYYpJFMPUo56Dnh75ytCOo5R8pZNKQniE0li4Pl485SdsEEKkoVj/ti7vp0iGevDofizOl4PA1tveWRKpAthvDIqwFcxM74d02Wijnkr0/FbHYNpyz02rCGa4oahMsuKa5+0rqmTm4tQC1j8qpPC0tCNoiVkTgdALGSLQxWLuFtOseh7izfu295u9neU/mTtqBfFJVDXLN6Ac3dyIIQ0yKyCpH1tncN9pTdKRoXnwsqo/KWoCH9NiJU01u8Ggho7FwbwlYPu4q0+qAoFcWVaFbIVESXSIqeu6JpBM6UpEgXye+qTYeMZbqUjst9W39LaC7lWoGukORZ6tA0JI7UdK6iJlkIwhDvq7XKhECCJm3kI6C0vwpBsF1T5NCkqbNnrcXAMOa4ptR6iglnYQ/rSey/USpLenj7PfwKqh/gJ+Ebwk0IF0SF/WRMy9M236m6qUAMtrvJZKnY7RtbSx4RgyoRSoqB+5azK5za58+CZTwtiUqByV979NjIE80fZ2fkg0iPYqjV7JOUjid3XCskgKny1pcohAJiZifCpXKFLTwySZHUmaED2vWHBdLqb+p3+rjMBStm4p24b+87S1QcLNx2mE3wrgweZhrBRYngVwoLSTxmCVaWYXUFFUVGrRR0m61iLMd4OV4d5Yk98pi5x7K1Aplfi5bQN/62RZNSiZDWpnXcP/kvLmD0sSyFVf8Gj2eTa6LwP6r77h90h+/XEp2wOlcM8hdxA6+iX7CtOnni2EwMeWJsOq8bA5VtpLbFmzdB9V1z8Wuty298iiSbLlL8em+Hq0COC861uez33PxBGK3d7XEF1k54d68LF4VirwLgVYf87yxMKBlM9Z3T8kbPKljPIKoSFz3F0UmcWZYqd2l+S+x0K9zGpbLvJSrKlY/toKLy7JVXsv5EhQXKf8O3l5FbwoY+Ea4MoXRvQuSC05mUhpn0t0Em9V9RdcuCXzlMBhDVFD+woFfCSHLDYatFDPzigEnrRWAkCtF91/CV8kqIEnkbsfQCt+tIRjypwq9gcEowIatIIsix6Kxv41UBSjPFBHyR/GqVeP6g7RS32k/PUqqhdExZPxoiLkR+NwQER9MqsRS/9SKWBIAYTJDDAH7lI1/G5g0u4Pb0l9wkdxWviPgWEZYdg4CVRkuEcb8snrWlZ7oSgma9VsH+PoHj6oonkahM3vKhIXk4xay5FFAKeykLMj5NS8m3DwYLp20kXAKe+METUYiPhA0fgHk6NKMfpYBZF5i012gIBi/hva7ySxm1/CEaLSfsOOHd21dSaIjKR8IVJJCYjGV7cXCV3wbjk5z8gUUMXJIIjcBl/i5Z8Pc9vNT+9ONofhX6vi7jeu8zbzE99pWKfYZpiR/CaIxKufry4XjFJwGS+as/s3bfpz1CCUWvd5WXK7pMRIpx/Hik4mPnxMMVXoaafyCtRpy/fr+ScvYss633B/x6GGrzTvvco5IvBcu3lveiIWY5e2Ly8yb/HcXZrVn+4Cabh2t+9EUa/f0de+AXykfuE0vmQPLgDvbVWuXxLYNlmSJu1vnY7gOInuPlf+EZla4S/uLKVatWrVq1atWqVatWrVq1atWqVatWrVq1atWqVatWrVq1atWqVatWrVq1atWqVatWrVq1atWqVatWrVq1p/Yv9s9CJV8mhbIAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_48\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,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****************************************************+n8Dq2/p9LIWLM34ufuUPWMACFrCABSxgAQtYwAIWsIAFLGABC1jAAhawgAUsYAELWMACFrCABSxgAQtYwAIWsIAFLGABC1jAAhawgAUsYAELWMACFrCABSxgAes/wfoDIrgNXjq0ZTIAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_49\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,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\",\"e\":1},{\"id\":\"imgSeq_50\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_51\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_52\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_53\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_54\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_55\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_56\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_57\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_58\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_59\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_60\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_61\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_62\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_63\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_64\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,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\",\"e\":1},{\"id\":\"imgSeq_65\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,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\",\"e\":1},{\"id\":\"imgSeq_66\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,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\",\"e\":1},{\"id\":\"imgSeq_67\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_68\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_69\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_70\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_71\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_72\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_73\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_74\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_75\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_76\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_77\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_78\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_79\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_80\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_81\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_82\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_83\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_84\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_85\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_86\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_87\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_88\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"imgSeq_89\",\"w\":300,\"h\":300,\"t\":\"seq\",\"u\":\"\",\"p\":\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAAEsAQMAAABDsxw2AAAAJHpUWHRDcmVhdG9yAAAImXNMyU9KVXBMK0ktUnBNS0tNLikGAEF6Bs5qehXFAAAACXBIWXMAAAABAAAAAQBPJcTWAAAAA1BMVEVHcEyC+tLSAAAAAXRSTlMAQObYZgAAACJJREFUaN7twTEBAAAAwqD1T20KP6AAAAAAAAAAAAAAAHgZLbQAAf/pCWsAAAAASUVORK5CYII=\",\"e\":1},{\"id\":\"sequence_0\",\"layers\":[{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_0\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":0,\"st\":0,\"op\":1,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_1\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":1,\"st\":1,\"op\":2,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_2\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":2,\"st\":2,\"op\":3,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_3\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":3,\"st\":3,\"op\":4,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_4\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":4,\"st\":4,\"op\":5,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_5\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":5,\"st\":5,\"op\":6,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_6\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":6,\"st\":6,\"op\":7,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_7\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":7,\"st\":7,\"op\":8,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_8\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":8,\"st\":8,\"op\":9,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_9\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":9,\"st\":9,\"op\":10,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_10\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":10,\"st\":10,\"op\":11,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_11\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":11,\"st\":11,\"op\":12,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_12\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":12,\"st\":12,\"op\":13,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_13\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":13,\"st\":13,\"op\":14,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_14\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":14,\"st\":14,\"op\":15,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_15\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":15,\"st\":15,\"op\":16,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_16\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":16,\"st\":16,\"op\":17,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_17\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":17,\"st\":17,\"op\":18,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_18\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":18,\"st\":18,\"op\":19,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_19\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":19,\"st\":19,\"op\":20,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_20\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":20,\"st\":20,\"op\":21,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_21\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":21,\"st\":21,\"op\":22,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_22\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":22,\"st\":22,\"op\":23,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_23\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":23,\"st\":23,\"op\":24,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_24\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":24,\"st\":24,\"op\":25,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_25\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":25,\"st\":25,\"op\":26,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_26\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":26,\"st\":26,\"op\":27,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_27\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":27,\"st\":27,\"op\":28,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_28\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":28,\"st\":28,\"op\":29,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_29\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":29,\"st\":29,\"op\":30,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_30\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":30,\"st\":30,\"op\":31,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_31\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":31,\"st\":31,\"op\":32,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_32\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":32,\"st\":32,\"op\":33,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_33\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":33,\"st\":33,\"op\":34,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_34\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":34,\"st\":34,\"op\":35,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_35\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":35,\"st\":35,\"op\":36,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_36\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":36,\"st\":36,\"op\":37,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_37\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":37,\"st\":37,\"op\":38,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_38\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":38,\"st\":38,\"op\":39,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_39\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":39,\"st\":39,\"op\":40,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_40\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":40,\"st\":40,\"op\":41,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_41\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":41,\"st\":41,\"op\":42,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_42\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":42,\"st\":42,\"op\":43,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_43\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":43,\"st\":43,\"op\":44,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_44\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":44,\"st\":44,\"op\":45,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_45\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":45,\"st\":45,\"op\":46,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_46\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":46,\"st\":46,\"op\":47,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_47\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":47,\"st\":47,\"op\":48,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_48\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":48,\"st\":48,\"op\":49,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_49\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":49,\"st\":49,\"op\":50,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_50\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":50,\"st\":50,\"op\":51,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_51\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":51,\"st\":51,\"op\":52,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_52\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":52,\"st\":52,\"op\":53,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_53\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":53,\"st\":53,\"op\":54,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_54\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":54,\"st\":54,\"op\":55,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_55\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":55,\"st\":55,\"op\":56,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_56\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":56,\"st\":56,\"op\":57,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_57\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":57,\"st\":57,\"op\":58,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_58\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":58,\"st\":58,\"op\":59,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_59\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":59,\"st\":59,\"op\":60,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_60\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":60,\"st\":60,\"op\":61,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_61\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":61,\"st\":61,\"op\":62,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_62\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":62,\"st\":62,\"op\":63,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_63\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":63,\"st\":63,\"op\":64,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_64\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":64,\"st\":64,\"op\":65,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_65\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":65,\"st\":65,\"op\":66,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_66\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":66,\"st\":66,\"op\":67,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_67\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":67,\"st\":67,\"op\":68,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_68\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":68,\"st\":68,\"op\":69,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_69\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":69,\"st\":69,\"op\":70,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_70\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":70,\"st\":70,\"op\":71,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_71\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":71,\"st\":71,\"op\":72,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_72\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":72,\"st\":72,\"op\":73,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_73\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":73,\"st\":73,\"op\":74,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_74\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":74,\"st\":74,\"op\":75,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_75\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":75,\"st\":75,\"op\":76,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_76\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":76,\"st\":76,\"op\":77,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_77\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":77,\"st\":77,\"op\":78,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_78\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":78,\"st\":78,\"op\":79,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_79\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":79,\"st\":79,\"op\":80,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_80\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":80,\"st\":80,\"op\":81,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_81\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":81,\"st\":81,\"op\":82,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_82\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":82,\"st\":82,\"op\":83,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_83\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":83,\"st\":83,\"op\":84,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_84\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":84,\"st\":84,\"op\":85,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_85\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":85,\"st\":85,\"op\":86,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_86\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":86,\"st\":86,\"op\":87,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_87\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":87,\"st\":87,\"op\":88,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_88\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":88,\"st\":88,\"op\":89,\"sr\":1,\"bm\":0},{\"ty\":2,\"sc\":\"#00ffff\",\"refId\":\"imgSeq_89\",\"ks\":{\"p\":{\"a\":0,\"k\":[0,0]},\"a\":{\"a\":0,\"k\":[0,0]},\"s\":{\"a\":0,\"k\":[100,100]},\"r\":{\"a\":0,\"k\":[0]},\"o\":{\"a\":0,\"k\":[100]}},\"ip\":89,\"st\":89,\"op\":91,\"sr\":1,\"bm\":0}]}],\"layers\":[{\"ddd\":0,\"ind\":1,\"ty\":0,\"nm\":\"绿闪电_[00000-00089].png\",\"cl\":\"png\",\"refId\":\"sequence_0\",\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[150,150,0],\"ix\":2,\"l\":2},\"a\":{\"a\":0,\"k\":[150,150,0],\"ix\":1,\"l\":2},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6,\"l\":2}},\"ao\":0,\"w\":300,\"h\":300,\"ip\":0,\"op\":4,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":2,\"ty\":0,\"nm\":\"绿闪电_[00000-00089].png\",\"cl\":\"png\",\"refId\":\"sequence_0\",\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[150,150,0],\"ix\":2,\"l\":2},\"a\":{\"a\":0,\"k\":[150,150,0],\"ix\":1,\"l\":2},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6,\"l\":2}},\"ao\":0,\"w\":300,\"h\":300,\"ip\":64,\"op\":67,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":3,\"ty\":0,\"nm\":\"绿闪电_[00000-00089].png\",\"cl\":\"png\",\"refId\":\"sequence_0\",\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[150,150,0],\"ix\":2,\"l\":2},\"a\":{\"a\":0,\"k\":[150,150,0],\"ix\":1,\"l\":2},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6,\"l\":2}},\"ao\":0,\"w\":300,\"h\":300,\"ip\":46,\"op\":50,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":4,\"ty\":0,\"nm\":\"绿闪电_[00000-00089].png\",\"cl\":\"png\",\"refId\":\"sequence_0\",\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[150,150,0],\"ix\":2,\"l\":2},\"a\":{\"a\":0,\"k\":[150,150,0],\"ix\":1,\"l\":2},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6,\"l\":2}},\"ao\":0,\"w\":300,\"h\":300,\"ip\":22,\"op\":27,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":5,\"ty\":0,\"nm\":\"绿闪电_[00000-00089].png\",\"cl\":\"png\",\"refId\":\"sequence_0\",\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[150,150,0],\"ix\":2,\"l\":2},\"a\":{\"a\":0,\"k\":[150,150,0],\"ix\":1,\"l\":2},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6,\"l\":2}},\"ao\":0,\"w\":300,\"h\":300,\"ip\":11,\"op\":13,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":6,\"ty\":0,\"nm\":\"绿闪电_[00000-00089].png\",\"cl\":\"png\",\"refId\":\"sequence_0\",\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[150,150,0],\"ix\":2,\"l\":2},\"a\":{\"a\":0,\"k\":[150,150,0],\"ix\":1,\"l\":2},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6,\"l\":2}},\"ao\":0,\"w\":300,\"h\":300,\"ip\":5,\"op\":8,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":7,\"ty\":2,\"nm\":\"绿.png 合成 1_00010.png\",\"cl\":\"png png\",\"refId\":\"image_0\",\"sr\":1,\"ks\":{\"o\":{\"a\":1,\"k\":[{\"i\":{\"x\":[0.463],\"y\":[1]},\"o\":{\"x\":[0.415],\"y\":[0]},\"t\":0,\"s\":[0]},{\"i\":{\"x\":[0.667],\"y\":[1]},\"o\":{\"x\":[0.512],\"y\":[0]},\"t\":11,\"s\":[100]},{\"t\":90,\"s\":[0]}],\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[150,150,0],\"ix\":2,\"l\":2},\"a\":{\"a\":0,\"k\":[150,150,0],\"ix\":1,\"l\":2},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6,\"l\":2}},\"ao\":0,\"ip\":0,\"op\":90,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":8,\"ty\":2,\"nm\":\"绿.png\",\"cl\":\"png\",\"refId\":\"image_1\",\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[150,150,0],\"ix\":2,\"l\":2},\"a\":{\"a\":0,\"k\":[48,48,0],\"ix\":1,\"l\":2},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6,\"l\":2}},\"ao\":0,\"ip\":0,\"op\":90,\"st\":0,\"bm\":0}],\"markers\":[],\"props\":{}}\n+\n"}, {"new_path": "assets/lottie/redlight.json", "old_path": "assets/lottie/redlight.json", "deleted_file": true, "new_file": false, "renamed_file": false, "diff": "@@ -1 +0,0 @@\n-{\"v\":\"5.12.1\",\"fr\":30,\"ip\":0,\"op\":90,\"w\":300,\"h\":300,\"nm\":\"红灯导出\",\"ddd\":0,\"assets\":[{\"id\":\"image_0\",\"w\":300,\"h\":300,\"u\":\"\",\"p\":\"data:image/png;base64,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\",\"e\":1},{\"id\":\"image_1\",\"w\":96,\"h\":96,\"u\":\"\",\"p\":\"data:image/png;base64,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\",\"e\":1}],\"layers\":[{\"ddd\":0,\"ind\":1,\"ty\":2,\"nm\":\"红.png 合成 1_00150.png\",\"cl\":\"png png\",\"refId\":\"image_0\",\"sr\":1,\"ks\":{\"o\":{\"a\":1,\"k\":[{\"i\":{\"x\":[0.463],\"y\":[1]},\"o\":{\"x\":[0.415],\"y\":[0]},\"t\":0,\"s\":[0]},{\"i\":{\"x\":[0.667],\"y\":[1]},\"o\":{\"x\":[0.512],\"y\":[0]},\"t\":11,\"s\":[100]},{\"t\":90,\"s\":[0]}],\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[150,150,0],\"ix\":2,\"l\":2},\"a\":{\"a\":0,\"k\":[150,150,0],\"ix\":1,\"l\":2},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6,\"l\":2}},\"ao\":0,\"ip\":0,\"op\":90,\"st\":0,\"bm\":0},{\"ddd\":0,\"ind\":2,\"ty\":2,\"nm\":\"红.png\",\"cl\":\"png\",\"refId\":\"image_1\",\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[150,150,0],\"ix\":2,\"l\":2},\"a\":{\"a\":0,\"k\":[48,48,0],\"ix\":1,\"l\":2},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6,\"l\":2}},\"ao\":0,\"ip\":0,\"op\":90,\"st\":0,\"bm\":0}],\"markers\":[],\"props\":{}}\n\\ No newline at end of file\n"}, {"new_path": "src/api/asynccard/index.ts", "old_path": "src/api/asynccard/index.ts", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -3,6 +3,7 @@ import { AsyncCard } from '@/proto-registry/src/web/raccoon/asynccard/asynccard_\n import {\n   DeleteAsyncCardReq,\n   GetAsyncCardsReq,\n+  GetUserShowAsyncCardCntReq,\n   ReGenerateAsyncCardReq\n } from '@/proto-registry/src/web/raccoon/asynccard/asynccard_pb';\n import { PartialMessage } from '@bufbuild/protobuf';\n@@ -20,3 +21,9 @@ export const regenAsyncCard = (\n ) => {\n   return asyncCardClient.reGenerateAsyncCard(payload);\n };\n+\n+export const getUserShowAsyncCardCnt = (\n+  payload: PartialMessage<GetUserShowAsyncCardCntReq>\n+) => {\n+  return asyncCardClient.getUserShowAsyncCardCnt(payload);\n+};\n"}, {"new_path": "src/api/goods/index.ts", "old_path": "src/api/goods/index.ts", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -14,7 +14,7 @@ import { PartialMessage } from '@bufbuild/protobuf';\n \n export const goodsClient = createSocketConnect('Goods', Goods);\n \n-/** 该用户狸小窝信息 */\n+/** 该用户痛墙信息 */\n export const getUserPlaceInfo = async (\n   placeParams: PartialMessage<GetPlaceReq>\n ): Promise<PartialMessage<GetPlaceRsp>> => {\n"}, {"new_path": "src/bizComponents/feedScreen/discussPanels/discussPanel.tsx", "old_path": "src/bizComponents/feedScreen/discussPanels/discussPanel.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -29,8 +29,8 @@ import { TabItemType } from '@/src/types';\n import { CommonEventBus } from '@/src/utils/event';\n import { reportExpo } from '@/src/utils/report';\n import { getBbsCommentLines } from '../../feedcard/ugcCard/BBSCard';\n-import { BBSTabItem, EXTENDED_STATE } from '../DIscussFeed';\n import FakeTabContainer from '../fakeTabContainer';\n+import { BBSTabItem, DISCUSS_EXTENDED_STATE } from '../type';\n \n function DiscussPanel({\n   tabItem,\n@@ -165,7 +165,7 @@ function DiscussPanel({\n       onRequest={fetchRecommendList}\n       footerStyle={[footerStyle]}\n       isActive={isActive}\n-      extendedState={EXTENDED_STATE}\n+      extendedState={DISCUSS_EXTENDED_STATE}\n       onScroll={onScroll}\n       customListProps={{\n         renderAheadOffset: 250\n"}, {"new_path": "src/bizComponents/feedScreen/rolePanels/myRole.tsx", "old_path": "src/bizComponents/feedScreen/rolePanels/myRole.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -58,9 +58,25 @@ const MyRole = memo(\n       );\n \n     useEffect(() => {\n-      clearRoleData(FixedRoleFeed.myCreate);\n-      clearRoleData(FixedRoleFeed.mySave);\n-    }, []);\n+      // 注释掉清除数据的代码，因为这可能会干扰从其他页面（如 userScreen）进入时已加载的数据\n+      // clearRoleData(FixedRoleFeed.myCreate);\n+      // clearRoleData(FixedRoleFeed.mySave);\n+\n+      // 当组件挂载且有焦点时，主动加载数据\n+      if (isFocus && isRender) {\n+        fetchMyRoleList({\n+          isInit: true,\n+          id: FixedRoleFeed.myCreate,\n+          reqParams: { filterType: RoleFilterType.RoleFilterTypeCreate }\n+        });\n+\n+        fetchMyRoleList({\n+          isInit: true,\n+          id: FixedRoleFeed.mySave,\n+          reqParams: { filterType: RoleFilterType.RoleFilterTypeSave }\n+        });\n+      }\n+    }, [isFocus, isRender]);\n \n     return (\n       <FakeTabContainer active={isActive} lazy>\n"}, {"new_path": "src/bizComponents/feedScreen/DiscussFeed.tsx", "old_path": "src/bizComponents/feedScreen/DiscussFeed.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -16,23 +16,8 @@ import { CellCardScene } from '../feedcard/types';\n import DiscussPanel from './discussPanels/discussPanel';\n import { useCachedTab } from './recommendSecondaryTab/cacheTab.hook';\n import FakeTabContainer from './fakeTabContainer';\n-import { RecommendSecondaryTab, TabItemInfo } from './recommendSecondaryTab';\n-\n-type BbsQueryKey = 'sort_type' | 'type_filter';\n-type BbsQueryValue = 'hot' | 'new' | 'picture' | 'oc';\n-\n-enum BbsTabKey {\n-  hot = 'bbs_hot',\n-  new = 'bbs_new',\n-  picture = 'bbs_picture',\n-  oc = 'bbs_oc'\n-}\n-\n-export interface BBSTabItem extends TabItemInfo {\n-  key: BbsTabKey;\n-  queryKey: BbsQueryKey;\n-  queryValue: BbsQueryValue;\n-}\n+import { RecommendSecondaryTab } from './recommendSecondaryTab';\n+import { BBSTabItem, BbsTabKey } from './type';\n \n const tabs: BBSTabItem[] = [\n   {\n@@ -66,15 +51,6 @@ const tabs: BBSTabItem[] = [\n   }\n ];\n \n-const fixedTabs = [tabs[0], tabs[1]];\n-\n-export const EXTENDED_STATE = {\n-  reportParams: {\n-    tab: EWaterFallTabReportType[EWaterFallTabType.DISCUSS]\n-  },\n-  tab: EWaterFallTabType.DISCUSS,\n-  scene: CellCardScene.DISCUSS\n-};\n export const DiscussFeed = ({\n   pending,\n   active: isPageActive,\n"}, {"new_path": "src/bizComponents/feedScreen/index.tsx", "old_path": "src/bizComponents/feedScreen/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -55,7 +55,7 @@ import PreloadImg from './preload/preloadImg';\n import { useIsFocused, useRoute } from '@react-navigation/native';\n import * as APMModule from '@step.ai/apm-module';\n import { useShallow } from 'zustand/react/shallow';\n-import { DiscussFeed } from './DIscussFeed';\n+import { DiscussFeed } from './DiscussFeed';\n import { RecommendFeed } from './RecommendFeed';\n import { RoleFeed } from './RoleFeed';\n import { FeedScreenPageParams } from './type';\n"}, {"new_path": "src/bizComponents/feedScreen/type.ts", "old_path": "src/bizComponents/feedScreen/type.ts", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -1,4 +1,9 @@\n+import {\n+  EWaterFallTabReportType,\n+  EWaterFallTabType\n+} from '@/src/components/waterfall/type';\n import { RichCardInfo as RawRichCardInfo } from '@/src/types';\n+import { CellCardScene } from '../feedcard/types';\n \n export enum ETagIp {\n   HOT = 'hot',\n@@ -101,3 +106,34 @@ export interface FeedScreenPageParams {\n   refresh?: string;\n   timestamp?: string;\n }\n+\n+export type BbsQueryKey = 'sort_type' | 'type_filter';\n+export type BbsQueryValue = 'hot' | 'new' | 'picture' | 'oc';\n+\n+export enum BbsTabKey {\n+  hot = 'bbs_hot',\n+  new = 'bbs_new',\n+  picture = 'bbs_picture',\n+  oc = 'bbs_oc'\n+}\n+\n+export interface TabItemInfo {\n+  title: string;\n+  key: string;\n+  isFire?: boolean;\n+  brandId: number;\n+}\n+\n+export interface BBSTabItem extends TabItemInfo {\n+  key: BbsTabKey;\n+  queryKey: BbsQueryKey;\n+  queryValue: BbsQueryValue;\n+}\n+\n+export const DISCUSS_EXTENDED_STATE = {\n+  reportParams: {\n+    tab: EWaterFallTabReportType[EWaterFallTabType.DISCUSS]\n+  },\n+  tab: EWaterFallTabType.DISCUSS,\n+  scene: CellCardScene.DISCUSS\n+};\n"}, {"new_path": "src/bizComponents/nestedScrollView/hooks/usePullDownGestureHandlers.tsx", "old_path": "src/bizComponents/nestedScrollView/hooks/usePullDownGestureHandlers.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -0,0 +1,188 @@\n+/* eslint-disable react-hooks/rules-of-hooks */\n+import {\n+  SharedValue,\n+  runOnJS,\n+  useSharedValue,\n+  useWorkletCallback,\n+  withTiming\n+} from 'react-native-reanimated';\n+import { SCROLLABLE_DIRECTION } from '../constants';\n+import type { GestureEventHandlerCallbackType } from '../types';\n+import { useTabGestureEventsHandlersDefault } from './useTabGestureEventsHandlersDefault';\n+\n+/**\n+ * 下拉手势处理器工厂\n+ * 返回一个对象，包含：\n+ * 1. getGestureHandlers - 返回处理手势的处理器函数\n+ * 2. pullDownProgress - 下拉进度的共享值\n+ */\n+export const usePullDownGestureHandlersFactory = (options: {\n+  onPullDownEnd?: (progress: number) => void;\n+  scrollPosition: SharedValue<number>;\n+}) => {\n+  // 下拉进度值\n+  const pullDownProgress = useSharedValue(0);\n+\n+  // 手势方向\n+  const gestureDirection = useSharedValue(SCROLLABLE_DIRECTION.UNKNOWN);\n+\n+  // 记录手势开始时的滚动位置\n+  const scrollPositionAtStart = useSharedValue(0);\n+\n+  // 触顶偏移量：-1表示未触顶，>=0表示触顶时的translationY值\n+  const topOffsetY = useSharedValue(-1);\n+\n+  // 标记之前是否处于顶部状态\n+  const wasAtTop = useSharedValue(false);\n+\n+  // 返回一个函数，该函数在调用时返回手势处理器\n+  const getGestureHandlers = () => {\n+    // 获取原始手势处理器\n+    const originalHandlers = useTabGestureEventsHandlersDefault();\n+\n+    // 手势开始时重置下拉进度\n+    const handleOnStart: GestureEventHandlerCallbackType = useWorkletCallback(\n+      function handleOnStart(source, payload) {\n+        // 记录开始位置\n+        scrollPositionAtStart.value = options.scrollPosition.value;\n+\n+        // 重置触顶偏移量（-1表示未触顶）\n+        topOffsetY.value = -1;\n+\n+        // 记录初始顶部状态\n+        wasAtTop.value = options.scrollPosition.value === 0;\n+\n+        // 重置下拉进度和手势方向\n+        pullDownProgress.value = 0;\n+        gestureDirection.value = SCROLLABLE_DIRECTION.UNKNOWN;\n+\n+        originalHandlers.handleOnStart(source, payload);\n+      },\n+      [originalHandlers.handleOnStart]\n+    );\n+\n+    // 手势变化时监控下拉进度\n+    const handleOnChange: GestureEventHandlerCallbackType = useWorkletCallback(\n+      function handleOnChange(source, payload) {\n+        const { translationY, translationX } = payload;\n+\n+        // 确定手势方向\n+        if (gestureDirection.value === SCROLLABLE_DIRECTION.UNKNOWN) {\n+          gestureDirection.value =\n+            Math.abs(translationX) - Math.abs(translationY) > 0\n+              ? SCROLLABLE_DIRECTION.HORIZONTAL\n+              : SCROLLABLE_DIRECTION.VERTICAL;\n+        }\n+\n+        const isAtTop = options.scrollPosition.value === 0;\n+\n+        // 检测是否从顶部变为非顶部状态\n+        if (wasAtTop.value && !isAtTop) {\n+          pullDownProgress.value = 0;\n+          console.log(\n+            '[GestureLog][PullDown][LeavingTop]',\n+            JSON.stringify({\n+              scrollPosition: options.scrollPosition.value\n+            })\n+          );\n+        }\n+\n+        // 更新顶部状态记录\n+        wasAtTop.value = isAtTop;\n+\n+        // 检测是否刚刚触顶\n+        if (isAtTop && topOffsetY.value === -1) {\n+          // 记录触顶时的translationY值\n+          topOffsetY.value = translationY;\n+        }\n+\n+        // 计算超出顶部的下拉距离\n+        let overTopDistance = 0;\n+        if (isAtTop) {\n+          // 如果已触顶（topOffsetY >= 0），计算超出部分\n+          // 如果未触顶（topOffsetY === -1），使用全部translationY\n+          overTopDistance =\n+            topOffsetY.value >= 0\n+              ? translationY - topOffsetY.value\n+              : translationY;\n+\n+          // 确保不为负值\n+          overTopDistance = Math.max(0, overTopDistance);\n+        }\n+\n+        const isVerticalPull =\n+          translationY > 0 &&\n+          gestureDirection.value === SCROLLABLE_DIRECTION.VERTICAL;\n+\n+        // 更新下拉进度\n+        if (isVerticalPull && isAtTop && overTopDistance > 0) {\n+          pullDownProgress.value = overTopDistance;\n+        } else if (pullDownProgress.value > 0) {\n+          pullDownProgress.value = 0;\n+        }\n+\n+        originalHandlers.handleOnChange(source, payload);\n+      },\n+      [originalHandlers.handleOnChange, options.scrollPosition]\n+    );\n+\n+    // 手势结束时处理\n+    const handleOnEnd: GestureEventHandlerCallbackType = useWorkletCallback(\n+      function handleOnEnd(source, payload) {\n+        const isAtTop = options.scrollPosition.value === 0;\n+\n+        if (pullDownProgress.value > 0 && isAtTop) {\n+          const finalProgress = pullDownProgress.value;\n+\n+          if (options?.onPullDownEnd) {\n+            runOnJS(options.onPullDownEnd)(finalProgress);\n+          }\n+        }\n+\n+        // 平滑重置下拉进度\n+        if (pullDownProgress.value > 0) {\n+          pullDownProgress.value = withTiming(0, { duration: 300 });\n+        }\n+\n+        // 重置所有状态\n+        topOffsetY.value = -1;\n+        wasAtTop.value = false;\n+        gestureDirection.value = SCROLLABLE_DIRECTION.UNKNOWN;\n+\n+        originalHandlers.handleOnEnd(source, payload);\n+      },\n+      [originalHandlers.handleOnEnd, options?.onPullDownEnd]\n+    );\n+\n+    // 手势完成时处理\n+    const handleOnFinalize: GestureEventHandlerCallbackType =\n+      useWorkletCallback(\n+        function handleOnFinalize(source, payload) {\n+          // 平滑重置下拉进度\n+          if (pullDownProgress.value > 0) {\n+            pullDownProgress.value = withTiming(0, { duration: 300 });\n+          }\n+\n+          // 重置所有状态\n+          topOffsetY.value = -1;\n+          wasAtTop.value = false;\n+          gestureDirection.value = SCROLLABLE_DIRECTION.UNKNOWN;\n+\n+          originalHandlers.handleOnFinalize(source, payload);\n+        },\n+        [originalHandlers.handleOnFinalize]\n+      );\n+\n+    return {\n+      handleOnStart,\n+      handleOnChange,\n+      handleOnEnd,\n+      handleOnFinalize\n+    };\n+  };\n+\n+  return {\n+    getGestureHandlers,\n+    pullDownProgress\n+  };\n+};\n"}, {"new_path": "src/bizComponents/topicScreen/components/topicRankBanner/components/TopicScrollingRow.tsx", "old_path": "src/bizComponents/topicScreen/components/topicRankBanner/components/TopicScrollingRow.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -10,7 +10,7 @@ import { StyleSheet } from '@/src/utils';\n import { styles as sharedStyles } from '../styles';\n \n interface TopicScrollingRowProps {\n-  row: Array<TopicOrBrand>;\n+  row: TopicOrBrand[];\n   index: number;\n   onTagPress?: (data: TopicOrBrand) => boolean | void;\n }\n"}, {"new_path": "src/bizComponents/trendingScreen/ImmersivePost.tsx", "old_path": "src/bizComponents/trendingScreen/ImmersivePost.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -47,7 +47,7 @@ interface ImmersivePostProps {\n   eventBus: Event<TVIDEO_FLOW_EVENT_KEYS>;\n }\n \n-export const ImmersivePost: React.FC<ImmersivePostProps> = memo(\n+export const ImmersivePost = memo(\n   ({\n     data,\n     isActive,\n@@ -197,7 +197,6 @@ export const ImmersivePost: React.FC<ImmersivePostProps> = memo(\n               data={data}\n               isImmersive={true}\n               onCardPress={handleCardPress}\n-              onCardReady={() => {}}\n               onLayout={layoutRectangle => {\n                 // 保存测量到的卡片高度\n                 setVoteButtonTop(\n"}, {"new_path": "src/bizComponents/trendingScreen/index.tsx", "old_path": "src/bizComponents/trendingScreen/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -40,7 +40,7 @@ import { AsyncAppendCard } from '../feedScreen/asyncAppendingCard';\n import { useAppendCard } from '../feedScreen/hooks/useAppendCard';\n import { AbsoluteIconBack } from '../livePhotoScreen/actionsLayer/AbsoluteIconBack';\n import TopicScreen from '../topicScreen';\n-import { PageTab } from '../userScreen';\n+import { UserPageTab } from '../userScreen/constants';\n import { DanceTogetherExtInfo } from '@/proto-registry/src/web/raccoon/common/dance_together_pb';\n import { LivePhotoExtInfo } from '@/proto-registry/src/web/raccoon/common/livephoto_pb';\n import { PortalHost } from '@gorhom/portal';\n@@ -200,7 +200,7 @@ const Trending = memo(\n                 pathname: `/user/${uid}`,\n                 params: {\n                   id: (uid || '').toString(),\n-                  profilePageTab: PageTab.SECRET\n+                  profilePageTab: UserPageTab.SECRET\n                 }\n               });\n             },\n"}, {"new_path": "src/bizComponents/userScreen/components/baseFlowList/index.tsx", "old_path": "src/bizComponents/userScreen/components/baseFlowList/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -3,36 +3,31 @@ import {\n   NativeScrollEvent,\n   NativeSyntheticEvent,\n   Platform,\n-  Text,\n+  ScrollViewProps,\n   View\n } from 'react-native';\n-import { GestureDetector, GestureType } from 'react-native-gesture-handler';\n-import { InfiniteListRef } from '@/src/components/infiniteList/typing';\n+import { InfiniteScrollViewProps } from '@/src/components/infiniteList/typing';\n import { WaterFall2 } from '@/src/components/waterfall/WaterFall2';\n import { IWaterFallProps } from '@/src/components/waterfall/type';\n import {\n   RequestFeedReturnActions,\n   RequestFeedReturnData\n } from '@/src/components/waterfall/useRequsetFeed';\n-import { useWaterfallGesture } from '@/src/components/waterfall/useWaterfallGesture';\n import { BOTTOM_TAB_HEIGHT, LIST_BOTTOM_SAFE_HEIGHT } from '@/src/constants';\n import { useScreenSize } from '@/src/hooks';\n import { dp2px } from '@/src/utils';\n-import { PageTab } from '../../types';\n+import { UserPageTab } from '../../constants';\n \n interface Props {\n-  pageTabKey: PageTab;\n+  pageTabKey: UserPageTab;\n   data: RequestFeedReturnData;\n   fetchList: RequestFeedReturnActions['fetchList'];\n-  onScroll: ReturnType<typeof useWaterfallGesture>['onScroll'];\n-  scrollViewProps: ReturnType<typeof useWaterfallGesture>['scrollViewProps'];\n+  scrollViewProps: InfiniteScrollViewProps;\n   waterfallProps?: Partial<IWaterFallProps>;\n-  listRef?: React.RefObject<InfiniteListRef>;\n   onMomentumScrollEnd?: (e: NativeSyntheticEvent<NativeScrollEvent>) => void;\n   isRootPage?: boolean; // 是否是根页面\n   $safePaddingBottom: number;\n-  nativeGesture: GestureType;\n-  currentTab: PageTab;\n+  currentTab: UserPageTab;\n }\n \n export const BaseWaterFlowList = memo((props: Props) => {\n@@ -40,14 +35,11 @@ export const BaseWaterFlowList = memo((props: Props) => {\n     pageTabKey,\n     data,\n     fetchList,\n-    onScroll,\n     scrollViewProps,\n     waterfallProps,\n-    listRef,\n     onMomentumScrollEnd,\n     isRootPage,\n     $safePaddingBottom,\n-    nativeGesture,\n     currentTab\n   } = props;\n \n@@ -58,53 +50,50 @@ export const BaseWaterFlowList = memo((props: Props) => {\n       key={pageTabKey}\n       style={{\n         width: screenWidth,\n-        marginTop: 5,\n         position: 'relative',\n         flex: 1\n       }}\n     >\n-      <GestureDetector gesture={nativeGesture}>\n-        <WaterFall2\n-          key={pageTabKey}\n-          data={data.sourceData}\n-          loading={data.loading}\n-          error={data.error}\n-          hasMore={data.hasMore}\n-          onRequest={fetchList}\n-          onScroll={onScroll}\n-          customEmptyProps={{\n-            children: '小狸在等你的作品！',\n-            type: 'darkProfile'\n-          }}\n-          ref={listRef}\n-          isActive={currentTab === pageTabKey}\n-          scrollViewProps={{\n-            ...scrollViewProps,\n-            bounces: false,\n-            hideRefresh: true,\n-            lockScroll: false,\n-            onMomentumScrollEnd: onMomentumScrollEnd\n-          }}\n-          footerStyle={{\n-            paddingBottom:\n-              $safePaddingBottom +\n-              (isRootPage ? BOTTOM_TAB_HEIGHT : 0) +\n-              LIST_BOTTOM_SAFE_HEIGHT +\n-              dp2px(Platform.OS === 'ios' ? 90 : 20)\n-          }}\n-          customListProps={{\n-            canChangeSize: true,\n-            applyWindowCorrection(offsetX, offsetY, windowCorrection) {\n-              return {\n-                ...windowCorrection,\n-                startCorrection: -10,\n-                endCorrection: 170\n-              };\n-            }\n-          }}\n-          {...waterfallProps}\n-        />\n-      </GestureDetector>\n+      <WaterFall2\n+        key={pageTabKey}\n+        data={data.sourceData}\n+        loading={data.loading}\n+        error={data.error}\n+        hasMore={data.hasMore}\n+        onRequest={fetchList}\n+        customEmptyProps={{\n+          children: '小狸在等你的作品！',\n+          type: 'darkProfile'\n+        }}\n+        isActive={currentTab === pageTabKey}\n+        scrollViewProps={{\n+          ...scrollViewProps,\n+          bounces: false,\n+          hideRefresh: true,\n+          overScrollMode: 'never',\n+          lockScroll: true,\n+          onMomentumScrollEnd: onMomentumScrollEnd\n+        }}\n+        footerStyle={{\n+          paddingBottom:\n+            $safePaddingBottom +\n+            (isRootPage ? BOTTOM_TAB_HEIGHT : 0) +\n+            LIST_BOTTOM_SAFE_HEIGHT +\n+            dp2px(Platform.OS === 'ios' ? 90 : 20)\n+        }}\n+        customListProps={{\n+          canChangeSize: true,\n+          applyWindowCorrection(offsetX, offsetY, windowCorrection) {\n+            return {\n+              ...windowCorrection,\n+              startCorrection: -10,\n+              endCorrection: 170\n+            };\n+          }\n+        }}\n+        {...waterfallProps}\n+        enablePullRefresh={false}\n+      />\n     </View>\n   );\n });\n"}, {"new_path": "src/bizComponents/userScreen/components/likeFlowList/index.tsx", "old_path": "src/bizComponents/userScreen/components/likeFlowList/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -1,5 +1,6 @@\n import { useLockFn } from 'ahooks';\n-import { memo, useEffect, useMemo } from 'react';\n+import { memo, useEffect, useMemo, useRef } from 'react';\n+import { ScrollView } from 'react-native';\n import { feedClient } from '@/src/api';\n import { CellCardScene } from '@/src/bizComponents/feedcard/types';\n import { hideLoading, showLoading } from '@/src/components';\n@@ -9,21 +10,15 @@ import {\n   FetchMethodPayloadType,\n   useRequestFeed\n } from '@/src/components/waterfall/useRequsetFeed';\n-import { useWaterfallGesture } from '@/src/components/waterfall/useWaterfallGesture';\n-import { usePersonalCenterStore } from '@/src/store/personalCenter';\n-import { FlowCommonProps, PageTab } from '../../types';\n+import { UserPageTab } from '../../constants';\n+import { FlowCommonProps } from '../../types';\n import { onRefreshError } from '../../utils';\n import { BaseWaterFlowList } from '../baseFlowList';\n \n-interface Props extends FlowCommonProps {}\n-\n-export const LikesFlowList = memo((props: Props) => {\n+export const LikesFlowList = memo((props: FlowCommonProps) => {\n   const {\n     id,\n-    updateUnlockTop,\n     $safePaddingBottom,\n-    scrollY,\n-    nativeGesture,\n     queryRefresh,\n     queryTimestamp,\n     queryPageTab,\n@@ -32,6 +27,8 @@ export const LikesFlowList = memo((props: Props) => {\n     currentTab\n   } = props;\n \n+  const scrollViewRef = useRef<ScrollView | null>(null);\n+\n   const fetchLikeFeedMethod = (payload: FetchMethodPayloadType) => {\n     return feedClient.userLikesCards({\n       uid: id ?? '',\n@@ -51,29 +48,22 @@ export const LikesFlowList = memo((props: Props) => {\n   }, [id]);\n \n   useEffect(() => {\n-    if (queryRefresh && queryPageTab === PageTab.LIKE) {\n+    if (queryRefresh && queryPageTab === UserPageTab.LIKE) {\n       lockFetchList(RequestScene.INIT);\n     }\n   }, [queryRefresh, queryTimestamp]);\n \n   useEffect(() => {\n-    if (currentTab === PageTab.LIKE && isRefreshData) {\n+    if (currentTab === UserPageTab.LIKE && isRefreshData) {\n       showLoading();\n       lockFetchList(RequestScene.REFRESHING).finally(() => {\n-        scrollViewProps.ref.current?.scrollTo(0, 0, false);\n+        scrollViewRef.current?.scrollTo(0, 0, false);\n         hideLoading();\n       });\n     }\n     // 这里只监听 tab 点击更新逻辑\n   }, [isRefreshData, currentTab]);\n \n-  const { onScroll, scrollViewProps, listRef, onMomentumScrollEnd } =\n-    useWaterfallGesture({\n-      active: currentTab === PageTab.LIKE,\n-      updateUnlockTop,\n-      scrollY\n-    });\n-\n   const waterfallProps: Partial<IWaterFallProps> = useMemo(\n     () => ({\n       customEmptyProps: {\n@@ -89,16 +79,14 @@ export const LikesFlowList = memo((props: Props) => {\n \n   return (\n     <BaseWaterFlowList\n-      pageTabKey={PageTab.LIKE}\n+      pageTabKey={UserPageTab.LIKE}\n       data={data}\n       fetchList={lockFetchList}\n-      onScroll={onScroll}\n-      scrollViewProps={scrollViewProps}\n+      scrollViewProps={{\n+        ref: scrollViewRef\n+      }}\n       waterfallProps={waterfallProps}\n-      listRef={listRef}\n-      onMomentumScrollEnd={onMomentumScrollEnd}\n       $safePaddingBottom={$safePaddingBottom}\n-      nativeGesture={nativeGesture}\n       isRootPage={isRootPage}\n       currentTab={currentTab}\n     />\n"}, {"new_path": "src/bizComponents/userScreen/components/myRoleList/index.tsx", "old_path": "src/bizComponents/userScreen/components/myRoleList/index.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -0,0 +1,289 @@\n+import { useLockFn } from 'ahooks';\n+import { router } from 'expo-router';\n+import React, { memo, useEffect, useState } from 'react';\n+import { TouchableOpacity, View } from 'react-native';\n+import { StyleProp, ViewStyle } from 'react-native';\n+import LinearGradient from 'react-native-linear-gradient';\n+import MyRole from '@/src/bizComponents/feedScreen/rolePanels/myRole';\n+import { FlowCommonProps } from '@/src/bizComponents/userScreen/types';\n+import { Image, Text, hideLoading, showLoading } from '@/src/components';\n+import { EmptyPlaceHolder } from '@/src/components/Empty';\n+import { useAuthState } from '@/src/hooks';\n+import { useCreateRoleFeedStore } from '@/src/store/feed/role-feed';\n+import { FixedRoleFeed } from '@/src/store/feed/role-feed';\n+import { rowStyle, typography } from '@/src/theme';\n+import { $flexCenter } from '@/src/theme/variable';\n+import { createStyle } from '@/src/utils';\n+import { reportClick } from '@/src/utils/report';\n+import { UserPageTab } from '../../constants';\n+import { RoleFilterType } from '@/proto-registry/src/web/raccoon/crole/crole_pb';\n+import { useShallow } from 'zustand/react/shallow';\n+\n+const CREATE_ROLE_ICON = require('@Assets/role/role_feed_add.png');\n+\n+interface MyRoleFlowListProps extends FlowCommonProps {\n+  updateUnlockTop?: (status: boolean) => void;\n+  nativeGesture?: unknown;\n+  scrollY?: unknown;\n+}\n+\n+// FIXME(fuxiao): 现在修改了我的角色列表数据获取方式，需要重新自己看下怎么实现\n+export const MyRoleFlowList = memo((props: MyRoleFlowListProps) => {\n+  const {\n+    $safePaddingBottom,\n+    isRefreshData,\n+    currentTab,\n+    queryRefresh,\n+    queryPageTab,\n+    queryTimestamp\n+  } = props;\n+\n+  const [isFocus, setIsFocus] = useState(false);\n+  const [isRender, setIsRender] = useState(false);\n+  const { loginIntercept } = useAuthState();\n+\n+  // 获取角色数据状态\n+  const { roleListDataMap, fetchMyRoleList, clearRoleData } =\n+    useCreateRoleFeedStore(\n+      useShallow(state => ({\n+        roleListDataMap: state.roleListDataMap,\n+        fetchMyRoleList: state.fetchMyRoleList,\n+        clearRoleData: state.clearRoleData\n+      }))\n+    );\n+\n+  // 加载我的角色数据\n+  const loadMyRoleData = useLockFn(async () => {\n+    try {\n+      // 创建的角色\n+      await fetchMyRoleList({\n+        isInit: true,\n+        id: FixedRoleFeed.myCreate,\n+        reqParams: { filterType: RoleFilterType.RoleFilterTypeCreate }\n+      });\n+\n+      // 收藏的角色\n+      await fetchMyRoleList({\n+        isInit: true,\n+        id: FixedRoleFeed.mySave,\n+        reqParams: { filterType: RoleFilterType.RoleFilterTypeSave }\n+      });\n+    } catch (e) {\n+      console.log('[MyRoleFlowList][loadMyRoleData]', JSON.stringify(e));\n+    }\n+  });\n+\n+  // 组件挂载时设置焦点状态\n+  useEffect(() => {\n+    setIsFocus(true);\n+    setIsRender(true);\n+\n+    // 组件挂载时如果当前tab是我的角色，主动获取数据\n+    if (currentTab === UserPageTab.MY_ROLE) {\n+      // 清空现有数据，确保加载新数据\n+      clearRoleData(FixedRoleFeed.myCreate);\n+      clearRoleData(FixedRoleFeed.mySave);\n+\n+      // 延迟加载，等待清空操作完成\n+      setTimeout(() => {\n+        loadMyRoleData();\n+      }, 100);\n+    }\n+\n+    return () => {\n+      setIsFocus(false);\n+      setIsRender(false);\n+    };\n+  }, []);\n+\n+  // 监听切换到我的角色tab\n+  useEffect(() => {\n+    if (currentTab === UserPageTab.MY_ROLE) {\n+      // 清空现有数据，确保加载新数据\n+      clearRoleData(FixedRoleFeed.myCreate);\n+      clearRoleData(FixedRoleFeed.mySave);\n+\n+      // 延迟加载，等待清空操作完成\n+      setTimeout(() => {\n+        loadMyRoleData();\n+      }, 100);\n+    }\n+  }, [currentTab]);\n+\n+  // 处理Tab切换刷新\n+  useEffect(() => {\n+    if (currentTab === UserPageTab.MY_ROLE && isRefreshData) {\n+      setIsFocus(false);\n+      showLoading();\n+      setTimeout(() => {\n+        setIsFocus(true);\n+        hideLoading();\n+\n+        // 清空现有数据\n+        clearRoleData(FixedRoleFeed.myCreate);\n+        clearRoleData(FixedRoleFeed.mySave);\n+\n+        // 加载新数据\n+        loadMyRoleData();\n+      }, 300);\n+    }\n+  }, [isRefreshData, currentTab]);\n+\n+  // 处理查询刷新\n+  useEffect(() => {\n+    if (queryRefresh && queryPageTab === UserPageTab.MY_ROLE) {\n+      setIsFocus(false);\n+      setTimeout(() => {\n+        setIsFocus(true);\n+\n+        // 清空现有数据\n+        clearRoleData(FixedRoleFeed.myCreate);\n+        clearRoleData(FixedRoleFeed.mySave);\n+\n+        // 加载新数据\n+        loadMyRoleData();\n+      }, 300);\n+    }\n+  }, [queryRefresh, queryTimestamp, queryPageTab]);\n+\n+  // 检查是否有空状态\n+  const hasNoRoles = Object.values(roleListDataMap).every(\n+    data => !data?.list?.length\n+  );\n+\n+  // 跳转到创建角色页面\n+  const handleCreateRole = () => {\n+    reportClick('create_character_button', {\n+      status: 1\n+    });\n+    loginIntercept(() => {\n+      router.push('/role-create/');\n+    });\n+  };\n+\n+  // 滚动容器样式\n+  const scrollContainerStyle: StyleProp<ViewStyle> = {\n+    paddingBottom: $safePaddingBottom + 120\n+  };\n+\n+  // 如果数据为空且已经加载完成，显示空状态\n+  if (hasNoRoles && isFocus && isRender) {\n+    return (\n+      <View style={{ flex: 1, backgroundColor: '#16161A' }}>\n+        <View\n+          style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}\n+        >\n+          <EmptyPlaceHolder type=\"crefRole\" style={{ height: 500 }}>\n+            <View style={{ ...$flexCenter }}>\n+              <Text style={$emptyStyles.hintText}>\n+                小狸没找到ta哦，来试试创建ta吧！\n+              </Text>\n+            </View>\n+          </EmptyPlaceHolder>\n+        </View>\n+        <View style={$createRoleBtnStyles.container}>\n+          <LinearGradient\n+            style={$createRoleBtnStyles.gradient}\n+            colors={['rgba(22, 22, 26, 0)', 'rgba(22, 22, 26, 0.8)']}\n+          />\n+          <TouchableOpacity\n+            style={[\n+              $createRoleBtnStyles.btn,\n+              rowStyle,\n+              { bottom: 30 + Number($safePaddingBottom) }\n+            ]}\n+            onPress={handleCreateRole}\n+          >\n+            <Image\n+              style={$createRoleBtnStyles.icon}\n+              source={CREATE_ROLE_ICON}\n+            />\n+            <Text style={[$createRoleBtnStyles.btnText, { marginLeft: 2 }]}>\n+              自创角色\n+            </Text>\n+          </TouchableOpacity>\n+        </View>\n+      </View>\n+    );\n+  }\n+\n+  return (\n+    <View style={{ flex: 1, backgroundColor: '#16161A' }}>\n+      <MyRole\n+        isActive={true}\n+        isRender={isRender}\n+        isFocus={isFocus}\n+        scrollContainerStyle={scrollContainerStyle}\n+      />\n+      <View style={$createRoleBtnStyles.container}>\n+        <LinearGradient\n+          style={$createRoleBtnStyles.gradient}\n+          colors={['rgba(22, 22, 26, 0)', 'rgba(22, 22, 26, 0.8)']}\n+        />\n+        <TouchableOpacity\n+          style={[\n+            $createRoleBtnStyles.btn,\n+            rowStyle,\n+            { bottom: 30 + Number($safePaddingBottom) }\n+          ]}\n+          onPress={handleCreateRole}\n+        >\n+          <Image style={$createRoleBtnStyles.icon} source={CREATE_ROLE_ICON} />\n+          <Text style={[$createRoleBtnStyles.btnText, { marginLeft: 2 }]}>\n+            自创角色\n+          </Text>\n+        </TouchableOpacity>\n+      </View>\n+    </View>\n+  );\n+});\n+\n+const $emptyStyles = createStyle({\n+  hintText: {\n+    fontSize: 14,\n+    color: '#FFD6CD',\n+    opacity: 0.4,\n+    fontFamily: typography.fonts.pingfangSC.normal,\n+    fontWeight: '500',\n+    textAlign: 'center'\n+  }\n+});\n+\n+const $createRoleBtnStyles = createStyle({\n+  container: {\n+    position: 'absolute',\n+    zIndex: 1,\n+    width: '100%',\n+    height: 108,\n+    bottom: 60,\n+    alignItems: 'center'\n+  },\n+  gradient: {\n+    width: '100%',\n+    height: '100%',\n+    position: 'absolute',\n+    zIndex: -1\n+  },\n+  btn: {\n+    position: 'absolute',\n+    width: 130,\n+    height: 44,\n+    backgroundColor: '#FF6A3B',\n+    borderRadius: 50,\n+    justifyContent: 'center'\n+  },\n+  icon: {\n+    width: 19,\n+    height: 19,\n+    objectFit: 'contain'\n+  },\n+  btnText: {\n+    fontSize: 14,\n+    lineHeight: 20,\n+    color: 'rgba(255,255,255,1)',\n+    fontFamily: typography.fonts.pingfangSC.normal,\n+    fontWeight: '600'\n+  }\n+});\n+\n+MyRoleFlowList.displayName = 'MyRoleFlowList';\n"}, {"new_path": "src/bizComponents/userScreen/components/secretFlowList/index.tsx", "old_path": "src/bizComponents/userScreen/components/secretFlowList/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -1,6 +1,6 @@\n import { useLockFn } from 'ahooks';\n-import { memo, useEffect, useMemo } from 'react';\n-import { Text, View } from 'react-native';\n+import { memo, useEffect, useMemo, useRef } from 'react';\n+import { ScrollView } from 'react-native';\n import { feedClient } from '@/src/api';\n import {\n   getSecretPhotoFeedLayoutProvider,\n@@ -9,19 +9,21 @@ import {\n import { CellCardScene } from '@/src/bizComponents/feedcard/types';\n import { hideLoading, showLoading } from '@/src/components';\n import { RequestScene } from '@/src/components/infiniteList/typing';\n-import { IWaterFallProps } from '@/src/components/waterfall/type';\n+import {\n+  IWaterFallProps,\n+  WaterFallCardData\n+} from '@/src/components/waterfall/type';\n import {\n   FetchMethodPayloadType,\n   useRequestFeed\n } from '@/src/components/waterfall/useRequsetFeed';\n-import { useWaterfallGesture } from '@/src/components/waterfall/useWaterfallGesture';\n import { useChangeRoute } from '@/src/hooks/useChangeRoute';\n import { useAsyncMessage } from '@/src/store/asyncMessage';\n import { useLiveStore } from '@/src/store/live';\n-import { usePersonalCenterStore } from '@/src/store/personalCenter';\n import { GameType } from '@/src/types';\n import { CommonEventBus } from '@/src/utils/event';\n-import { FlowCommonProps, PageTab } from '../../types';\n+import { UserPageTab } from '../../constants';\n+import { FlowCommonProps } from '../../types';\n import { onRefreshError } from '../../utils';\n import { BaseWaterFlowList } from '../baseFlowList';\n import {\n@@ -30,8 +32,6 @@ import {\n } from '@/proto-registry/src/web/raccoon/common/asynccard_pb';\n import { Video } from '@/proto-registry/src/web/raccoon/common/media_pb';\n \n-interface Props extends FlowCommonProps {}\n-\n /**\n  * 视频待发布页列表数据\n  * 相关的 format 操作可以在这里处理\n@@ -75,13 +75,42 @@ async function fetchSecretFeedMethod(payload: FetchMethodPayloadType) {\n     });\n }\n \n-export const SecretsFlowList = memo((props: Props) => {\n+// 适配器函数：转换为 WaterFall2 所需的类型\n+const adaptedGetLayoutProvider = (\n+  dataRef: React.MutableRefObject<WaterFallCardData[]>\n+) => {\n+  // 将 WaterFallCardData[] 类型安全地转换为 AsyncCardInfo[]\n+  const adaptedDataRef = {\n+    get current() {\n+      return dataRef.current as unknown as AsyncCardInfo[];\n+    }\n+  } as React.MutableRefObject<AsyncCardInfo[]>;\n+\n+  return getSecretPhotoFeedLayoutProvider(adaptedDataRef);\n+};\n+\n+// 适配器函数：转换为 WaterFall2 所需的类型\n+const adaptedRenderItem = (\n+  type: string | number,\n+  data: WaterFallCardData,\n+  index: number,\n+  extendedState?: object,\n+  layoutInfo?: { x: number; y: number }\n+) => {\n+  // 将 WaterFallCardData 类型安全地转换为 AsyncCardInfo\n+  return renderSecretPhotoItem(\n+    type,\n+    data as unknown as AsyncCardInfo,\n+    index,\n+    extendedState,\n+    layoutInfo\n+  );\n+};\n+\n+export const SecretsFlowList = memo((props: FlowCommonProps) => {\n   const {\n     id,\n-    updateUnlockTop,\n     $safePaddingBottom,\n-    scrollY,\n-    nativeGesture,\n     queryRefresh,\n     queryPageTab,\n     queryTimestamp,\n@@ -90,6 +119,7 @@ export const SecretsFlowList = memo((props: Props) => {\n     currentTab\n   } = props;\n \n+  const scrollViewRef = useRef<ScrollView | null>(null);\n   const { go2Create } = useChangeRoute();\n \n   const {\n@@ -126,43 +156,36 @@ export const SecretsFlowList = memo((props: Props) => {\n   }, [id]);\n \n   useEffect(() => {\n-    if (queryRefresh && queryPageTab === PageTab.SECRET) {\n+    if (queryRefresh && queryPageTab === UserPageTab.SECRET) {\n       lockFetchList(RequestScene.INIT);\n     }\n   }, [queryRefresh, queryTimestamp]);\n \n   useEffect(() => {\n-    if (currentTab === PageTab.SECRET && isRefreshData) {\n+    if (currentTab === UserPageTab.SECRET && isRefreshData) {\n       showLoading();\n       lockFetchList(RequestScene.REFRESHING).finally(() => {\n-        scrollViewProps.ref.current?.scrollTo(0, 0, false);\n+        scrollViewRef.current?.scrollTo(0, 0, false);\n         hideLoading();\n       });\n     }\n     // 这里只监听 tab 点击更新逻辑\n   }, [isRefreshData, currentTab]);\n \n-  const {\n-    onScroll,\n-    scrollViewProps,\n-    listRef: worksFeedRef,\n-    onMomentumScrollEnd\n-  } = useWaterfallGesture({\n-    active: currentTab === PageTab.LIKE,\n-    updateUnlockTop,\n-    scrollY\n-  });\n-\n   const validSecretSourceData = useMemo(() => {\n     return (\n-      secretSourceData?.filter(i => i.status !== AsyncCardStatus.PUBLISH) || []\n+      secretSourceData?.filter(i => {\n+        // 类型断言为 AsyncCardInfo\n+        const asyncCard = i as unknown as AsyncCardInfo;\n+        return asyncCard.status !== AsyncCardStatus.PUBLISH;\n+      }) || []\n     );\n   }, [secretSourceData]);\n \n   const waterfallProps: Partial<IWaterFallProps> = useMemo(\n     () => ({\n-      renderItem: renderSecretPhotoItem,\n-      getLayoutProvider: getSecretPhotoFeedLayoutProvider,\n+      renderItem: adaptedRenderItem,\n+      getLayoutProvider: adaptedGetLayoutProvider,\n       extendedState: {\n         onRefresh: () => {\n           lockFetchList(RequestScene.REFRESHING);\n@@ -182,31 +205,33 @@ export const SecretsFlowList = memo((props: Props) => {\n       },\n       reportParams: {\n         type: 'secret_video'\n-      },\n-      getReportParams: (data: AsyncCardInfo[], index: number) => {\n-        return {\n-          contentid: data[index]?.cardId\n-        };\n       }\n+      // FIXME(fuxiao): 这里滚动会不断调用，导致不断打印\n+      // getReportParams: (data: WaterFallCardData[], index: number) => {\n+      //   // 由于数据结构实际上是 AsyncCardInfo 而不是标准的 WaterFallCardData\n+      //   const item = data[index] as unknown as AsyncCardInfo;\n+      //   console.log('[DEBUG] item', item);\n+      //   return {\n+      //     contentid: item?.cardId\n+      //   };\n+      // }\n     }),\n     [lockFetchList, go2Create]\n   );\n \n   return (\n     <BaseWaterFlowList\n-      pageTabKey={PageTab.LIKE}\n+      pageTabKey={UserPageTab.SECRET}\n       data={{\n         ...secretReturnData,\n         sourceData: validSecretSourceData\n       }}\n       fetchList={lockFetchList}\n-      onScroll={onScroll}\n-      scrollViewProps={scrollViewProps}\n+      scrollViewProps={{\n+        ref: scrollViewRef\n+      }}\n       waterfallProps={waterfallProps}\n-      listRef={worksFeedRef}\n-      onMomentumScrollEnd={onMomentumScrollEnd}\n       $safePaddingBottom={$safePaddingBottom}\n-      nativeGesture={nativeGesture}\n       isRootPage={isRootPage}\n       currentTab={currentTab}\n     />\n"}, {"new_path": "src/bizComponents/userScreen/components/workFlowList/index.tsx", "old_path": "src/bizComponents/userScreen/components/workFlowList/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -1,5 +1,6 @@\n import { useLockFn } from 'ahooks';\n-import { memo, useEffect, useMemo } from 'react';\n+import { memo, useEffect, useMemo, useRef } from 'react';\n+import { ScrollView } from 'react-native';\n import { feedClient } from '@/src/api';\n import { CellCardScene } from '@/src/bizComponents/feedcard/types';\n import { hideLoading, showLoading } from '@/src/components';\n@@ -8,25 +9,19 @@ import {\n   FetchMethodPayloadType,\n   useRequestFeed\n } from '@/src/components/waterfall/useRequsetFeed';\n-import { useWaterfallGesture } from '@/src/components/waterfall/useWaterfallGesture';\n import { usePersistFn } from '@/src/hooks';\n-import { usePersonalCenterStore } from '@/src/store/personalCenter';\n import { useVideoFlow } from '@/src/store/video-flow';\n import { CardType, PlainType, RichCardInfo } from '@/src/types';\n import { userPerformanceCollector } from '@/src/utils/report/userPageCollector';\n-import { FlowCommonProps, PageTab } from '../../types';\n+import { UserPageTab } from '../../constants';\n+import { FlowCommonProps } from '../../types';\n import { onRefreshError } from '../../utils';\n import { BaseWaterFlowList } from '../baseFlowList';\n \n-interface Props extends FlowCommonProps {}\n-\n-export const WorksFlowList = memo((props: Props) => {\n+export const WorksFlowList = memo((props: FlowCommonProps) => {\n   const {\n     id,\n-    updateUnlockTop,\n     $safePaddingBottom,\n-    scrollY,\n-    nativeGesture,\n     queryRefresh,\n     queryTimestamp,\n     queryPageTab,\n@@ -35,6 +30,8 @@ export const WorksFlowList = memo((props: Props) => {\n     currentTab\n   } = props;\n \n+  const waterfallRef = useRef<ScrollView | null>(null);\n+\n   async function fetchUserFeedkMethod(payload: FetchMethodPayloadType) {\n     return feedClient.userCreatedCards({\n       uid: id ?? ''!,\n@@ -62,23 +59,16 @@ export const WorksFlowList = memo((props: Props) => {\n   }, [id]);\n \n   useEffect(() => {\n-    if (queryRefresh && queryPageTab === PageTab.WORKS) {\n+    if (queryRefresh && queryPageTab === UserPageTab.WORKS) {\n       lockFetchList(RequestScene.INIT);\n     }\n   }, [queryRefresh, queryTimestamp]);\n \n-  const { onScroll, scrollViewProps, listRef, onMomentumScrollEnd } =\n-    useWaterfallGesture({\n-      active: currentTab === PageTab.WORKS,\n-      updateUnlockTop,\n-      scrollY\n-    });\n-\n   useEffect(() => {\n-    if (currentTab === PageTab.WORKS && isRefreshData) {\n+    if (currentTab === UserPageTab.WORKS && isRefreshData) {\n       showLoading();\n       lockFetchList(RequestScene.REFRESHING).finally(() => {\n-        scrollViewProps.ref.current?.scrollTo(0, 0, false);\n+        waterfallRef.current?.scrollTo(0, 0, false);\n         hideLoading();\n       });\n     }\n@@ -107,16 +97,14 @@ export const WorksFlowList = memo((props: Props) => {\n \n   return (\n     <BaseWaterFlowList\n-      pageTabKey={PageTab.WORKS}\n+      pageTabKey={UserPageTab.WORKS}\n       data={data}\n       fetchList={lockFetchList}\n-      onScroll={onScroll}\n-      scrollViewProps={scrollViewProps}\n+      scrollViewProps={{\n+        ref: waterfallRef\n+      }}\n       waterfallProps={waterfallProps}\n-      listRef={listRef}\n-      onMomentumScrollEnd={onMomentumScrollEnd}\n       $safePaddingBottom={$safePaddingBottom}\n-      nativeGesture={nativeGesture}\n       isRootPage={isRootPage}\n       currentTab={currentTab}\n     />\n"}, {"new_path": "src/bizComponents/userScreen/components/GoodsButton.tsx", "old_path": "src/bizComponents/userScreen/components/GoodsButton.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -0,0 +1,47 @@\n+import { Pressable, View } from 'react-native';\n+import { Image } from '@/src/components/image';\n+import { StyleSheet, dp2px } from '../../../utils';\n+\n+const GOODS_BUTTON = require('@Assets/image/goods-shef/goods_button.png');\n+const MY_GOODS_BUTTON = require('@Assets/image/goods-shef/my_goods_button.png');\n+export function GoodsButton({\n+  isMine,\n+  checkEnterGoods,\n+  hasGoods,\n+  safeTop\n+}: {\n+  isMine: boolean;\n+  checkEnterGoods: () => void;\n+  hasGoods: boolean;\n+  safeTop: number;\n+}) {\n+  return (\n+    <Pressable\n+      style={[\n+        styles.$goodsBtnContainer,\n+        { top: hasGoods ? dp2px(158) + safeTop : dp2px(50) + safeTop }\n+      ]}\n+      hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}\n+      onPress={checkEnterGoods}\n+    >\n+      <Image\n+        source={isMine ? MY_GOODS_BUTTON : GOODS_BUTTON}\n+        style={styles.$goodsBtnImage}\n+      />\n+    </Pressable>\n+  );\n+}\n+\n+const styles = StyleSheet.create({\n+  $goodsBtnContainer: {\n+    position: 'absolute',\n+    right: 0,\n+    width: 88.7,\n+    height: 30,\n+    zIndex: 201\n+  },\n+  $goodsBtnImage: {\n+    width: '100%',\n+    height: '100%'\n+  }\n+});\n"}, {"new_path": "src/bizComponents/userScreen/components/GoodsTopTip.tsx", "old_path": "src/bizComponents/userScreen/components/GoodsTopTip.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -0,0 +1,384 @@\n+import * as Haptics from 'expo-haptics';\n+import AnimatedLottieView from 'lottie-react-native';\n+import React from 'react';\n+import { Pressable, View } from 'react-native';\n+import Animated, {\n+  Extrapolate,\n+  SharedValue,\n+  interpolate,\n+  runOnJS,\n+  useAnimatedReaction,\n+  useAnimatedStyle,\n+  useSharedValue,\n+  withTiming\n+} from 'react-native-reanimated';\n+import { Icon, Text } from '@/src/components';\n+import { darkTheme, typography } from '@/src/theme';\n+import { $USE_FONT } from '@/src/theme/variable';\n+import { dp2px, isIos } from '@/src/utils';\n+import { StyleSheet } from '@Utils/StyleSheet';\n+\n+const INTRO_CUSTOM_PULL = require('@Assets/lottie/goods/intro_custom_pull.json');\n+const INTRO_PULL = require('@Assets/lottie/goods/intro_pull.json');\n+\n+// 提升关键动画参数到顶部便于后期修改\n+export const ANIMATION_PARAMS = {\n+  SCROLL_START: 60, // 点赞 tip 开始淡出的滚动距离\n+  SCROLL_END: 130, // 点赞 tip 完全隐藏的滚动距离\n+  PULL_BUFFER: 40, // 下拉缓冲空间\n+  PULL_REFRESH_THRESHOLD: 100, // 下拉刷新阈值\n+  PULL_REFRESH_HOLD: 140, // 刷新提示保持完全显示的距离\n+  PULL_REFRESH_FADE_END: 200, // 刷新提示完全消失的距离\n+  PULL_GOODS_THRESHOLD: 250, // 打开痛墙阈值\n+  PULL_GOODS_FADE_START: 180, // 痛墙提示开始淡入的距离\n+  ANIMATION_DURATION: 200, // 动画持续时间（毫秒）\n+  ANIMATION_SCALE_START: 0.65 // 缩放比例\n+};\n+\n+interface GoodsTopTipProps {\n+  goodsLikes?: number;\n+  isMine: boolean;\n+  hasGoods?: boolean;\n+  isGoodsTipAllowed?: boolean;\n+  checkEnterGoods: () => void;\n+  safeTop: number;\n+  scrollPosition?: SharedValue<number>;\n+  pullDownProgress?: SharedValue<number>;\n+}\n+\n+export function GoodsTopTip({\n+  goodsLikes,\n+  isMine,\n+  hasGoods,\n+  isGoodsTipAllowed = false,\n+  checkEnterGoods,\n+  safeTop,\n+  scrollPosition,\n+  pullDownProgress\n+}: GoodsTopTipProps) {\n+  const shouldShowGoodsGuide =\n+    isGoodsTipAllowed && (isMine || (!isMine && hasGoods));\n+\n+  const shouldShowLikesTip = goodsLikes && goodsLikes > 0 && !isGoodsTipAllowed;\n+\n+  // 跟踪是否正在下拉中\n+  const [isPulling, setIsPulling] = React.useState(false);\n+  // 使用sharedValue跟踪是否已触发震动\n+  const hasTriggeredHaptic = useSharedValue(false);\n+  // 使用sharedValue记录上一次的下拉进度\n+  const previousPullProgress = useSharedValue(0);\n+\n+  // 定义触发震动的函数\n+  const triggerHapticFeedback = React.useCallback(() => {\n+    if (isIos) {\n+      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);\n+    }\n+  }, []);\n+\n+  useAnimatedReaction(\n+    () => pullDownProgress,\n+    progress => {\n+      if (!progress) return;\n+\n+      // 检测是否开始下拉\n+      if (progress.value > 0 && !isPulling) {\n+        runOnJS(setIsPulling)(true);\n+      } else if (progress.value === 0 && isPulling) {\n+        runOnJS(setIsPulling)(false);\n+        // 重置震动状态\n+        hasTriggeredHaptic.value = false;\n+        previousPullProgress.value = 0;\n+      }\n+\n+      // 如果用户从痛墙区域回到刷新区域，重置震动状态\n+      if (\n+        progress.value < ANIMATION_PARAMS.PULL_GOODS_THRESHOLD &&\n+        previousPullProgress.value >= ANIMATION_PARAMS.PULL_GOODS_THRESHOLD\n+      ) {\n+        hasTriggeredHaptic.value = false;\n+      }\n+\n+      // 检测是否达到痛墙阈值，触发震动\n+      if (\n+        isIos &&\n+        progress.value >= ANIMATION_PARAMS.PULL_GOODS_THRESHOLD &&\n+        !hasTriggeredHaptic.value\n+      ) {\n+        runOnJS(triggerHapticFeedback)();\n+        hasTriggeredHaptic.value = true;\n+      }\n+\n+      // 更新上一次的下拉进度\n+      previousPullProgress.value = progress.value;\n+    }\n+  );\n+\n+  // 原始提示（痛墙赞/痛墙提示）动画样式\n+  const animatedContainerStyle = useAnimatedStyle(() => {\n+    let opacity = 1;\n+    let pointerEvents: 'auto' | 'none' = 'auto';\n+\n+    // 根据情况决定透明度\n+    // 1. 如果正在下拉，立即隐藏提示\n+    if (isPulling) {\n+      opacity = withTiming(0, {\n+        duration: ANIMATION_PARAMS.ANIMATION_DURATION\n+      });\n+      pointerEvents = 'none';\n+    }\n+    // 2. 如果正在滚动，根据滚动距离渐变透明度\n+    else if (scrollPosition && scrollPosition.value !== 0) {\n+      const scrollDistance = Math.abs(scrollPosition.value);\n+      const targetOpacity = interpolate(\n+        scrollDistance,\n+        [ANIMATION_PARAMS.SCROLL_START, ANIMATION_PARAMS.SCROLL_END],\n+        [1, 0],\n+        Extrapolate.CLAMP\n+      );\n+\n+      opacity = targetOpacity;\n+\n+      // 当完全隐藏时禁用点击事件\n+      pointerEvents =\n+        scrollDistance >= ANIMATION_PARAMS.SCROLL_END ? 'none' : 'auto';\n+    }\n+    // 3. 其他情况（未下拉且在顶部），平滑显示提示\n+    else {\n+      opacity = withTiming(1, {\n+        duration: ANIMATION_PARAMS.ANIMATION_DURATION\n+      });\n+      pointerEvents = 'auto';\n+    }\n+\n+    return {\n+      opacity,\n+      pointerEvents\n+    };\n+  });\n+\n+  // 刷新提示动画样式 - \"松手立即刷新\"\n+  const refreshTipStyle = useAnimatedStyle(() => {\n+    if (!pullDownProgress) return { opacity: 0 };\n+\n+    // 下拉到一定距离后才开始显示，达到阈值时完全不透明，\n+    // 保持显示直到PULL_REFRESH_HOLD，然后在PULL_REFRESH_FADE_END开始淡出\n+    const opacity = interpolate(\n+      pullDownProgress.value,\n+      [\n+        ANIMATION_PARAMS.PULL_BUFFER * 1.5, // 开始显示的阈值\n+        ANIMATION_PARAMS.PULL_REFRESH_THRESHOLD, // 完全不透明\n+        ANIMATION_PARAMS.PULL_REFRESH_HOLD, // 保持完全显示直到这个距离\n+        ANIMATION_PARAMS.PULL_REFRESH_FADE_END // 完全淡出\n+      ],\n+      [0, 1, 1, 0],\n+      Extrapolate.CLAMP\n+    );\n+\n+    // 添加缩放效果，从小到大，不超过原始尺寸\n+    const scale = interpolate(\n+      pullDownProgress.value,\n+      [\n+        ANIMATION_PARAMS.PULL_BUFFER * 1.5, // 开始显示时较小\n+        ANIMATION_PARAMS.PULL_REFRESH_THRESHOLD // 达到阈值时原始大小\n+      ],\n+      [ANIMATION_PARAMS.ANIMATION_SCALE_START, 1],\n+      Extrapolate.CLAMP\n+    );\n+\n+    return {\n+      opacity,\n+      transform: [{ scale }],\n+      display: opacity > 0 ? 'flex' : 'none'\n+    };\n+  });\n+\n+  // 痛墙提示动画样式 - \"松手进入痛墙\"\n+  const goodsTipStyle = useAnimatedStyle(() => {\n+    if (!pullDownProgress) return { opacity: 0 };\n+\n+    // 在PULL_GOODS_FADE_START到PULL_GOODS_THRESHOLD之间淡入\n+    const opacity = interpolate(\n+      pullDownProgress.value,\n+      [\n+        ANIMATION_PARAMS.PULL_GOODS_FADE_START,\n+        ANIMATION_PARAMS.PULL_GOODS_THRESHOLD\n+      ],\n+      [0, 1],\n+      Extrapolate.CLAMP\n+    );\n+\n+    // 添加缩放效果，从小到大，不超过原始尺寸\n+    const scale = interpolate(\n+      pullDownProgress.value,\n+      [\n+        ANIMATION_PARAMS.PULL_GOODS_FADE_START, // 开始显示时较小\n+        ANIMATION_PARAMS.PULL_GOODS_THRESHOLD // 达到阈值时原始大小\n+      ],\n+      [ANIMATION_PARAMS.ANIMATION_SCALE_START, 1],\n+      Extrapolate.CLAMP\n+    );\n+\n+    return {\n+      opacity,\n+      transform: [{ scale }],\n+      display: opacity > 0 ? 'flex' : 'none'\n+    };\n+  });\n+\n+  // 不应该显示任何提示时，仍需要返回下拉提示的容器\n+  const shouldShowAnyTip = shouldShowGoodsGuide || shouldShowLikesTip;\n+\n+  return (\n+    <>\n+      {shouldShowAnyTip ? (\n+        <Animated.View\n+          style={[\n+            styles.$outerContainer,\n+            { top: safeTop + dp2px(10) },\n+            animatedContainerStyle\n+          ]}\n+        >\n+          {shouldShowLikesTip ? (\n+            <View style={styles.$likesContainer}>\n+              <Icon icon=\"self_goods_like\" size={16} style={styles.$likeIcon} />\n+              <Text style={styles.$likesCountText}>{goodsLikes}人</Text>\n+              <Text style={styles.$likesText}>\n+                {isMine ? '赞了你的痛墙' : '赞了TA的痛墙'}\n+              </Text>\n+            </View>\n+          ) : (\n+            <Pressable onPress={checkEnterGoods} hitSlop={10}>\n+              <View\n+                style={[\n+                  styles.$likesContainer,\n+                  { paddingTop: 3, paddingLeft: 14, paddingRight: 4 }\n+                ]}\n+              >\n+                {isMine ? (\n+                  <AnimatedLottieView\n+                    source={INTRO_PULL}\n+                    loop\n+                    autoPlay\n+                    style={styles.$ownerLottie}\n+                  />\n+                ) : (\n+                  <AnimatedLottieView\n+                    source={INTRO_CUSTOM_PULL}\n+                    loop\n+                    autoPlay\n+                    style={styles.$visitorLottie}\n+                  />\n+                )}\n+              </View>\n+            </Pressable>\n+          )}\n+        </Animated.View>\n+      ) : null}\n+\n+      {/* 下拉提示容器 - 包含三个不同状态的提示，位置在原始提示下方 */}\n+      {pullDownProgress ? (\n+        <Animated.View\n+          style={[\n+            styles.$pullDownContainer,\n+            {\n+              top: safeTop + dp2px(48)\n+            }\n+          ]}\n+        >\n+          {/* 刷新提示 - \"松手立即刷新\" */}\n+          <Animated.View style={[refreshTipStyle, styles.$pullTipWrapper]}>\n+            <View style={styles.$pullTipContainer}>\n+              <Text style={styles.$pullTipText}>松手刷新</Text>\n+            </View>\n+          </Animated.View>\n+\n+          {/* 进入痛墙提示 - \"下拉进入痛墙\" */}\n+          <Animated.View style={[goodsTipStyle, styles.$pullTipWrapper]}>\n+            <View style={styles.$pullTipContainer}>\n+              <Text style={styles.$pullTipText}>下拉进入痛墙</Text>\n+            </View>\n+          </Animated.View>\n+        </Animated.View>\n+      ) : null}\n+    </>\n+  );\n+}\n+\n+const styles = StyleSheet.create({\n+  $outerContainer: {\n+    position: 'absolute',\n+    left: 0,\n+    right: 0,\n+    alignItems: 'center',\n+    zIndex: 100\n+  },\n+  $likesContainer: {\n+    flexDirection: 'row',\n+    alignItems: 'center',\n+    height: 30,\n+    backgroundColor: StyleSheet.darkTheme.background.transTip,\n+    paddingLeft: 16,\n+    paddingRight: 16,\n+    borderRadius: 15\n+  },\n+  $likeIcon: {\n+    marginRight: 4\n+  },\n+  $likesCountText: {\n+    ...$USE_FONT(\n+      darkTheme.text.primary,\n+      typography.fonts.pingfangSC.normal,\n+      12,\n+      'normal',\n+      isIos ? '600' : 'bold',\n+      undefined\n+    ),\n+    marginRight: 4\n+  },\n+  $likesText: {\n+    ...$USE_FONT(\n+      darkTheme.text.primary,\n+      typography.fonts.pingfangSC.normal,\n+      12,\n+      'normal',\n+      isIos ? '600' : 'bold',\n+      undefined\n+    )\n+  },\n+  $ownerLottie: {\n+    width: 111,\n+    height: 21\n+  },\n+  $visitorLottie: {\n+    width: 139,\n+    height: 21\n+  },\n+  $pullDownContainer: {\n+    position: 'absolute',\n+    left: 0,\n+    right: 0,\n+    alignItems: 'center',\n+    zIndex: 101\n+  },\n+  $pullTipWrapper: {\n+    position: 'absolute',\n+    left: 0,\n+    right: 0,\n+    alignItems: 'center'\n+  },\n+  $pullTipContainer: {\n+    height: 30,\n+    backgroundColor: 'rgba(0, 0, 0, 0.5)',\n+    paddingLeft: 12,\n+    paddingRight: 12,\n+    borderRadius: 15,\n+    justifyContent: 'center'\n+  },\n+  $pullTipText: {\n+    color: '#ffffff',\n+    fontSize: 12,\n+    textAlign: 'center',\n+    fontFamily: typography.fonts.feed,\n+    lineHeight: 30\n+  }\n+});\n"}, {"new_path": "src/bizComponents/userScreen/components/GoodsWallBg.tsx", "old_path": "src/bizComponents/userScreen/components/GoodsWallBg.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -0,0 +1,109 @@\n+import React, { useMemo } from 'react';\n+import { View } from 'react-native';\n+import Animated, { FadeIn } from 'react-native-reanimated';\n+import { Image } from '../../../components';\n+import { useScreenSize } from '../../../hooks';\n+import { StyleSheet, dp2px } from '../../../utils';\n+import { PartialMessage } from '@bufbuild/protobuf';\n+import { MediaType } from '@step.ai/proto-gen/raccoon/common/media_pb';\n+import { GetPlaceRsp } from '@step.ai/proto-gen/raccoon/goods/goods_pb';\n+\n+const GOODS_BG = require('@Assets/image/goods-shef/goods_bg.webp');\n+\n+interface GoodsWallBgProps {\n+  visible?: boolean;\n+  goodsWallRes?: PartialMessage<GetPlaceRsp>;\n+  safeTop: number;\n+}\n+\n+export function GoodsWallBg({\n+  visible = true,\n+  goodsWallRes,\n+  safeTop\n+}: GoodsWallBgProps) {\n+  const { width: screenWidth, height: screenHeight } = useScreenSize('window');\n+\n+  const { width: wallBgWidth, height: wallBgHeight } =\n+    (goodsWallRes?.backgroundImage?.type === MediaType.IMAGE &&\n+      goodsWallRes?.backgroundImage?.meta?.case === 'image' &&\n+      goodsWallRes?.backgroundImage?.meta?.value) ||\n+    {};\n+\n+  // 计算图片显示的逻辑\n+  const bgImageStyle = useMemo(() => {\n+    if (!wallBgWidth || !wallBgHeight) {\n+      return {};\n+    }\n+\n+    // 使用原始尺寸，但只显示左侧 4/5\n+    const aspectRatio = wallBgWidth / wallBgHeight;\n+\n+    // 计算合适的高度，使图片宽度等于屏幕宽度\n+    const adjustedHeight = screenWidth / aspectRatio;\n+\n+    // 图片显示为屏幕宽度，但向左偏移 1/5 的宽度（这样只显示右边的 4/5）\n+    return {\n+      width: screenWidth,\n+      height: adjustedHeight,\n+      // 向左偏移 1/5 的屏幕宽度，使得我们只看到右边的 4/5\n+      transform: [{ translateX: dp2px(44) }, { scale: 1.225 }]\n+    };\n+  }, [wallBgWidth, wallBgHeight, screenWidth]);\n+\n+  const hasWallBg = !!goodsWallRes?.backgroundImage?.url && !!wallBgWidth;\n+\n+  return (\n+    <View style={styles.$container}>\n+      {visible && (\n+        <>\n+          <Animated.View entering={FadeIn.duration(150)}>\n+            <Image\n+              source={GOODS_BG}\n+              contentFit=\"contain\"\n+              contentPosition=\"top\"\n+              // 不使用 native 会导致图片定位问题\n+              // native\n+              style={[styles.$goodsBg, { height: screenHeight }]}\n+            />\n+          </Animated.View>\n+\n+          {hasWallBg && (\n+            <Image\n+              source={{ uri: goodsWallRes?.backgroundImage?.url }}\n+              tosSize=\"size1\"\n+              contentFit=\"cover\"\n+              style={[\n+                styles.$wallBgImage,\n+                {\n+                  top: safeTop * 0.75,\n+                  ...bgImageStyle\n+                }\n+              ]}\n+            />\n+          )}\n+        </>\n+      )}\n+    </View>\n+  );\n+}\n+\n+const styles = StyleSheet.create({\n+  $container: {\n+    width: '100%',\n+    height: '100%',\n+    position: 'absolute',\n+    pointerEvents: 'none',\n+    zIndex: -1,\n+    backgroundColor: StyleSheet.darkTheme.background.page\n+  },\n+  $goodsBg: {\n+    width: '100%',\n+    position: 'absolute',\n+    zIndex: -1\n+  },\n+  $wallBgImage: {\n+    position: 'absolute',\n+    left: 0,\n+    zIndex: 0 // 确保壁纸背景在 goods bg 上面显示\n+  }\n+});\n"}, {"new_path": "src/bizComponents/userScreen/components/MineButtons.tsx", "old_path": "src/bizComponents/userScreen/components/MineButtons.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -0,0 +1,99 @@\n+import { useMemoizedFn } from 'ahooks';\n+import { router } from 'expo-router';\n+import React from 'react';\n+import { TouchableOpacity, View } from 'react-native';\n+import { Image, Text } from '@/src/components';\n+import { typography } from '@/src/theme';\n+import { $USE_FONT } from '@/src/theme/variable';\n+import { isIos } from '@/src/utils';\n+import { reportClick } from '@/src/utils/report';\n+import { StyleSheet } from '@Utils/StyleSheet';\n+import { handleEditProfile } from '../utils';\n+\n+interface MineButtonsProps {\n+  onEditPress?: () => void;\n+  onSettingPress?: () => void;\n+}\n+\n+const SETTING_NEW = require('@Assets/icon/icon-setting-new.png');\n+export const MineButtons: React.FC<MineButtonsProps> = ({\n+  onEditPress,\n+  onSettingPress\n+}) => {\n+  const handleEditPress = useMemoizedFn(() => {\n+    handleEditProfile(onEditPress);\n+  });\n+\n+  const handleSettingPress = useMemoizedFn(() => {\n+    if (onSettingPress) {\n+      onSettingPress();\n+    } else {\n+      reportClick('button', {\n+        user_button: 0,\n+        identity_status: '0'\n+      });\n+      router.push('/setting/');\n+    }\n+  });\n+\n+  return (\n+    <View style={styles.$container}>\n+      <TouchableOpacity\n+        activeOpacity={0.4}\n+        style={styles.$editBtn}\n+        onPress={handleEditPress}\n+        hitSlop={{ top: 10, bottom: 10, left: 10, right: 5 }}\n+      >\n+        <Text style={styles.$editBtnText}>编辑资料</Text>\n+      </TouchableOpacity>\n+\n+      <TouchableOpacity\n+        style={styles.$settingBtn}\n+        onPress={handleSettingPress}\n+        activeOpacity={0.4}\n+        hitSlop={{ top: 10, bottom: 10, left: 5, right: 10 }}\n+      >\n+        <Image source={SETTING_NEW} style={styles.$settingBtnImage} />\n+      </TouchableOpacity>\n+    </View>\n+  );\n+};\n+\n+const styles = StyleSheet.create({\n+  $container: {\n+    flexDirection: 'row',\n+    alignItems: 'center',\n+    gap: 12,\n+    marginLeft: 'auto'\n+  },\n+  $editBtn: {\n+    height: 30,\n+    paddingLeft: 14,\n+    paddingRight: 14,\n+    borderRadius: 100,\n+    backgroundColor: 'rgba(255, 255, 255, 0.08)',\n+    justifyContent: 'center',\n+    alignItems: 'center'\n+  },\n+  $editBtnText: {\n+    ...$USE_FONT(\n+      StyleSheet.darkTheme.text.primary,\n+      typography.fonts.pingfangSC.normal,\n+      12,\n+      'normal',\n+      '500',\n+      isIos ? 15.5 : 14\n+    ),\n+    textAlign: 'center'\n+  },\n+  $settingBtn: {\n+    width: 32,\n+    height: 32,\n+    alignItems: 'center',\n+    justifyContent: 'center'\n+  },\n+  $settingBtnImage: {\n+    width: '100%',\n+    height: '100%'\n+  }\n+});\n"}, {"new_path": "src/bizComponents/userScreen/components/MineCards.tsx", "old_path": "src/bizComponents/userScreen/components/MineCards.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -0,0 +1,168 @@\n+import { router } from 'expo-router';\n+import React from 'react';\n+import { Platform, Pressable, View } from 'react-native';\n+import { LinearGradient } from 'react-native-linear-gradient';\n+import Svg, { Path } from 'react-native-svg';\n+import { Text } from '@/src/components';\n+import { useCreditStore } from '@/src/store/credit';\n+import { StyleSheet, dp2px } from '@/src/utils';\n+import { reportClick, reportDiy } from '@/src/utils/report';\n+import { Image } from '@Components/image';\n+import { useShallow } from 'zustand/react/shallow';\n+\n+// 资源常量\n+const BATTERY_ICON = require('@Assets/image/goods-shef/icon_battery.png');\n+const BATTERY_GLOW = require('@Assets/image/goods-shef/battery_glow_bottom.png');\n+const INVITE_BUTTON_BG = require('@Assets/image/goods-shef/invite_button_bg.png');\n+\n+// 电池图标组件\n+const BatteryIcon = () => (\n+  <Svg width={dp2px(12)} height={dp2px(12)} viewBox=\"0 0 12 12\" fill=\"none\">\n+    <Path\n+      fillRule=\"evenodd\"\n+      clipRule=\"evenodd\"\n+      d=\"M6.63851 5.4165 7.35284 1 2 6.62891h3.06711l-.71433 4.41649 5.35284-5.6289H6.63851Z\"\n+      fill=\"#2EE84B\"\n+    />\n+  </Svg>\n+);\n+\n+export const MineCards = () => {\n+  const { totalCredits } = useCreditStore(\n+    useShallow(state => ({\n+      totalCredits: state.totalCredits\n+    }))\n+  );\n+\n+  return (\n+    <View style={styles.$container}>\n+      {/* 狸电池卡片 */}\n+      <Pressable\n+        style={styles.$cardWrapper}\n+        onPress={() => {\n+          reportDiy('credit', 'entrance_button-click');\n+          router.push('/credit/');\n+        }}\n+      >\n+        <LinearGradient\n+          colors={['rgba(255, 255, 255, 0.08)', 'rgba(255, 255, 255, 0.04)']}\n+          start={{ x: 0, y: 0 }}\n+          end={{ x: 1, y: 1 }}\n+          style={styles.$card}\n+        >\n+          <View style={styles.$textContainer}>\n+            <Text style={styles.$title}>狸电池</Text>\n+            <View style={styles.$batteryCountContainer}>\n+              <BatteryIcon />\n+              <Text style={styles.$batteryCount}>{totalCredits}</Text>\n+            </View>\n+          </View>\n+          <View style={styles.$iconContainer}>\n+            <Image source={BATTERY_GLOW} native style={styles.$glowImage} />\n+            <Image source={BATTERY_ICON} native style={styles.$icon} />\n+          </View>\n+        </LinearGradient>\n+      </Pressable>\n+\n+      {/* 邀请好礼卡片 */}\n+      <Pressable\n+        style={styles.$cardWrapper}\n+        onPress={() => {\n+          reportClick('invite_icon');\n+          router.push('/lottery-fission/');\n+        }}\n+      >\n+        <Image source={INVITE_BUTTON_BG} style={styles.$inviteImage} />\n+      </Pressable>\n+    </View>\n+  );\n+};\n+\n+const styles = StyleSheet.create({\n+  $container: {\n+    flexDirection: 'row',\n+    gap: 11,\n+    marginTop: 20,\n+    backgroundColor: StyleSheet.darkTheme.background.page,\n+    width: '100%'\n+  },\n+  $cardWrapper: {\n+    flex: 1,\n+    height: 68,\n+    borderRadius: 12,\n+    overflow: 'hidden'\n+  },\n+  $card: {\n+    flex: 1,\n+    flexDirection: 'row',\n+    alignItems: 'center',\n+    justifyContent: 'space-between',\n+    paddingLeft: 16,\n+    paddingRight: 16,\n+    borderRadius: 12,\n+    borderWidth: 0.3,\n+    borderColor: 'rgba(255, 255, 255, 0.1)'\n+  },\n+  $textContainer: {\n+    flexDirection: 'column',\n+    marginTop: 2\n+  },\n+  $title: {\n+    fontSize: dp2px(14),\n+    ...Platform.select({\n+      ios: {\n+        fontWeight: '600'\n+      },\n+      android: {\n+        fontWeight: 'bold'\n+      }\n+    }),\n+    color: StyleSheet.darkTheme.text.primary,\n+    height: 20,\n+    ...Platform.select({\n+      ios: {\n+        marginBottom: 3\n+      },\n+      android: {\n+        marginBottom: 2\n+      }\n+    })\n+  },\n+  $batteryCountContainer: {\n+    flexDirection: 'row',\n+    alignItems: 'center',\n+    gap: 4\n+  },\n+  $batteryCount: {\n+    fontSize: dp2px(12),\n+    color: StyleSheet.darkTheme.text.tertiary,\n+    fontFamily: StyleSheet.typography.fonts.Rany.Medium,\n+    ...Platform.select({\n+      android: {\n+        marginTop: 1\n+      }\n+    })\n+  },\n+  $iconContainer: {\n+    position: 'relative',\n+    width: 52,\n+    height: 52\n+  },\n+  $glowImage: {\n+    position: 'absolute',\n+    width: 166,\n+    height: 68,\n+    right: -20,\n+    top: -7,\n+    zIndex: 1\n+  },\n+  $icon: {\n+    width: 52,\n+    height: 52,\n+    zIndex: 3\n+  },\n+  $inviteImage: {\n+    width: '100%',\n+    height: 68\n+  }\n+});\n"}, {"new_path": "src/bizComponents/userScreen/components/UserHeader.tsx", "old_path": "src/bizComponents/userScreen/components/UserHeader.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -0,0 +1,222 @@\n+import { router } from 'expo-router';\n+import React, { useMemo } from 'react';\n+import { View } from 'react-native';\n+import Animated, {\n+  SharedValue,\n+  useAnimatedStyle,\n+  withTiming\n+} from 'react-native-reanimated';\n+import { Header, Icon, Text } from '@/src/components';\n+import { Avatar } from '@/src/components/avatar';\n+import { Follow } from '@/src/components/follow';\n+import { usePersistFn } from '@/src/hooks';\n+import { useUserInfoStore } from '@/src/store/userInfo';\n+import { CommonColor } from '@/src/theme/colors/common';\n+import { UserProfile, UserSocialStat } from '@/src/types';\n+import { StyleSheet, dp2px, isIos } from '@/src/utils';\n+import { stirngRemoveEnter } from '@/src/utils/opt/replace';\n+import { reportClick } from '@/src/utils/report';\n+import { useShallow } from 'zustand/react/shallow';\n+import { MineButtons } from './MineButtons';\n+\n+interface UserHeaderProps {\n+  profile?: UserProfile;\n+  stat?: UserSocialStat;\n+  hasSlideUp?: SharedValue<boolean>;\n+  isMine?: boolean;\n+  isRootPage?: boolean;\n+  onBack?: () => void;\n+  safeTop: number;\n+}\n+\n+export const UserHeaderRight = ({\n+  profile,\n+  stat,\n+  isMine\n+}: {\n+  profile?: UserProfile;\n+  stat: UserSocialStat;\n+  isMine?: boolean;\n+}) => {\n+  const { updateStat } = useUserInfoStore(\n+    useShallow(state => ({\n+      updateStat: state.updateStat\n+    }))\n+  );\n+  const onUpdatefollow = usePersistFn((followed: boolean) => {\n+    reportClick('follow_button', { userId: profile?.uid || '', followed });\n+    updateStat(profile?.uid || '', {\n+      followed\n+    });\n+  });\n+\n+  if (isMine) {\n+    return <MineButtons />;\n+  }\n+  return (\n+    <Follow\n+      buttonStyle={styles.$followBtn}\n+      followed={!!stat.followed}\n+      beingFollowed={!!stat.beingFollowed}\n+      uid={profile?.uid || ''}\n+      onUnfollow={() => onUpdatefollow(false)}\n+      onFollow={() => onUpdatefollow(true)}\n+    />\n+  );\n+};\n+\n+export const UserHeader = ({\n+  profile,\n+  stat,\n+  hasSlideUp,\n+  isMine = false,\n+  isRootPage = false,\n+  onBack,\n+  safeTop\n+}: UserHeaderProps) => {\n+  const handleBack = usePersistFn(() => {\n+    if (isRootPage) {\n+      return;\n+    }\n+    console.log('handleBack');\n+    reportClick('button', {\n+      user_button: 1,\n+      identity_status: isMine ? '0' : '1'\n+    });\n+    if (onBack) {\n+      onBack();\n+    } else {\n+      router.back();\n+    }\n+  });\n+\n+  const displayName = useMemo(() => {\n+    return stirngRemoveEnter(profile?.name || '-');\n+  }, [profile?.name]);\n+\n+  // 只使用 hasSlideUp 控制透明度\n+  const $headerStyle = useAnimatedStyle(() => {\n+    'worklet';\n+    return {\n+      pointerEvents: hasSlideUp?.value ? 'auto' : 'none',\n+      opacity: withTiming(hasSlideUp?.value ? 1 : 0)\n+    };\n+  }, [hasSlideUp]);\n+\n+  // 添加独立的返回按钮样式\n+  const $backButtonStyle = useAnimatedStyle(() => {\n+    'worklet';\n+    return {\n+      opacity: withTiming(hasSlideUp?.value ? 0 : 1),\n+      top: safeTop + 6\n+    };\n+  }, [hasSlideUp, isRootPage, safeTop]);\n+\n+  return (\n+    <>\n+      <Animated.View\n+        style={[\n+          styles.$headerContainer,\n+          {\n+            paddingTop: safeTop,\n+            height: dp2px(44) + safeTop\n+          },\n+          $headerStyle\n+        ]}\n+      >\n+        <Header\n+          themeColors={{ textColor: CommonColor.white }}\n+          headerStyle={styles.$header}\n+          backButton={\n+            !isRootPage ? (\n+              <Icon\n+                onPress={handleBack}\n+                icon=\"back\"\n+                size={dp2px(16)}\n+                color=\"#fff\"\n+              />\n+            ) : null\n+          }\n+          headerLeft={() => (\n+            <View style={styles.$headerBrand}>\n+              <Avatar\n+                profile={profile}\n+                size={dp2px(24)}\n+                showTag={false}\n+                showPendant={false}\n+              />\n+              <Text style={styles.$headerName} numberOfLines={1}>\n+                {displayName}\n+              </Text>\n+            </View>\n+          )}\n+          headerRight={() =>\n+            stat ? (\n+              <UserHeaderRight profile={profile} stat={stat} isMine={isMine} />\n+            ) : null\n+          }\n+        />\n+      </Animated.View>\n+\n+      {/* 添加独立的返回按钮 */}\n+      {!isRootPage && (\n+        <Animated.View style={[styles.$backButton, $backButtonStyle]}>\n+          <Icon\n+            onPress={handleBack}\n+            icon=\"back\"\n+            size={22}\n+            color=\"#fff\"\n+            hitSlop={20}\n+          />\n+        </Animated.View>\n+      )}\n+    </>\n+  );\n+};\n+\n+const styles = StyleSheet.create({\n+  $headerContainer: {\n+    position: 'absolute',\n+    left: 0,\n+    right: 0,\n+    top: 3,\n+    zIndex: 2,\n+    backgroundColor: StyleSheet.darkTheme.background.page\n+  },\n+  $header: {\n+    borderBottomWidth: 0,\n+    paddingLeft: 16,\n+    paddingRight: 16\n+  },\n+  $followBtn: {\n+    width: 76,\n+    height: 30\n+  },\n+  $headerBrand: {\n+    display: 'flex',\n+    justifyContent: 'flex-start',\n+    alignItems: 'center',\n+    flexDirection: 'row',\n+    width: 200\n+  },\n+  // FIXME(fuxiao): 处理很长的名字\n+  $headerName: {\n+    fontSize: 16,\n+    fontWeight: isIos ? '600' : 'bold',\n+    marginLeft: 6,\n+    color: StyleSheet.darkTheme.text.primary\n+  },\n+  // 新增返回按钮的静态样式\n+  $backButton: {\n+    position: 'absolute',\n+    left: 16,\n+    zIndex: 300,\n+    paddingRight: 2,\n+    width: 32,\n+    height: 32,\n+    borderRadius: 16,\n+    backgroundColor: StyleSheet.darkTheme.background.transTip,\n+    justifyContent: 'center',\n+    alignItems: 'center'\n+  }\n+});\n"}, {"new_path": "src/bizComponents/userScreen/components/UserInfo.tsx", "old_path": "src/bizComponents/userScreen/components/UserInfo.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -0,0 +1,200 @@\n+import { useMemoizedFn } from 'ahooks';\n+import { router } from 'expo-router';\n+import { useEffect } from 'react';\n+import { TouchableOpacity, View } from 'react-native';\n+import { Text } from '@/src/components';\n+import { Avatar } from '@/src/components/avatar';\n+import { SwitchName, useControlStore } from '@/src/store/control';\n+import { useEditPendantStore } from '@/src/store/edit-pendant';\n+import { darkTheme, typography } from '@/src/theme';\n+import { $USE_FONT, $Z_INDEXES } from '@/src/theme/variable';\n+import { StyleSheet, dp2px, isIos } from '@/src/utils';\n+import { stirngRemoveEnter } from '@/src/utils/opt/replace';\n+import { reportClick } from '@/src/utils/report';\n+import { Image } from '@Components/image';\n+import { userPerformanceCollector } from '../../../utils/report/userPageCollector';\n+import { handleEditProfile } from '../utils';\n+import { UserProfile } from '@/proto-registry/src/web/raccoon/uinfo/uinfo_pb';\n+import { useIsFocused } from '@react-navigation/native';\n+import { useShallow } from 'zustand/react/shallow';\n+\n+const PENDANT_TIP_BG = require('@Assets/user/pendant-tip-bg.png');\n+\n+export function UserInfo({\n+  currentUser,\n+  isMine,\n+  showFeedBack\n+}: {\n+  currentUser?: UserProfile;\n+  isMine?: boolean;\n+  showFeedBack?: () => void;\n+}) {\n+  // FIXME(fuxiao): 新挂件逻辑与 UI 确认\n+  const { newPendant, fetchNewPendant } = useEditPendantStore(\n+    useShallow(state => ({\n+      newPendant: state.newPendant,\n+      fetchNewPendant: state.fetchNewPendant\n+    }))\n+  );\n+  const isFocus = useIsFocused();\n+\n+  useEffect(() => {\n+    if (isFocus && isMine) {\n+      fetchNewPendant();\n+    }\n+  }, [fetchNewPendant, isFocus, isMine]);\n+\n+  const currentUserName = stirngRemoveEnter(currentUser?.name);\n+\n+  const handleEditAvatar = useMemoizedFn(() => {\n+    reportClick('button', {\n+      user_button: 2,\n+      identity_status: isMine ? '0' : '1'\n+    });\n+    if (isMine) {\n+      router.push('/avatar-edit/');\n+    } else {\n+      if (\n+        useControlStore.getState().checkIsOpen(SwitchName.ENABLE_USER_REPORT)\n+      ) {\n+        showFeedBack?.();\n+      }\n+    }\n+  });\n+\n+  const handleEditProfileClick = useMemoizedFn(() => {\n+    if (isMine) {\n+      handleEditProfile();\n+    }\n+  });\n+\n+  return (\n+    <View style={styles.$container}>\n+      <View style={styles.$avatarContainer}>\n+        <Avatar\n+          profile={currentUser}\n+          size={90}\n+          showTag={true}\n+          showPendant={true}\n+          onPress={handleEditAvatar}\n+          borderWidth={1}\n+          borderColor={StyleSheet.darkTheme.border.avatar}\n+          outerBorder={true}\n+          onLoad={() => {\n+            if (currentUser?.uid) {\n+              userPerformanceCollector.markPerformanceTimestamp(\n+                'user_info_available_timestamp',\n+                currentUser?.uid\n+              );\n+            }\n+          }}\n+        />\n+\n+        {newPendant && (\n+          <View style={styles.$pendantTipContainer}>\n+            <Image style={styles.$pendantTip} source={PENDANT_TIP_BG} />\n+            <View style={styles.$pendantContent}>\n+              <Image\n+                source={newPendant?.pendantUrl || ''}\n+                style={styles.$pendantImage}\n+              />\n+              <Text style={styles.$newPendantText}>{'新挂件'}</Text>\n+            </View>\n+            <View style={styles.$dotTip} />\n+          </View>\n+        )}\n+      </View>\n+\n+      <TouchableOpacity\n+        activeOpacity={0.7}\n+        onPress={handleEditProfileClick}\n+        disabled={!isMine}\n+        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}\n+        style={styles.$infoContainer}\n+      >\n+        <Text\n+          style={$USE_FONT(\n+            isMine && currentUser?.isNameUpdated === false\n+              ? darkTheme.text.primary\n+              : darkTheme.text.primary,\n+            typography.fonts.baba.bold,\n+            dp2px(18),\n+            'normal',\n+            isIos ? '600' : 'bold',\n+            25\n+          )}\n+          ellipsizeMode=\"tail\"\n+          numberOfLines={1}\n+        >\n+          {currentUserName}\n+        </Text>\n+      </TouchableOpacity>\n+    </View>\n+  );\n+}\n+\n+const styles = StyleSheet.create({\n+  $container: {\n+    flexDirection: 'row',\n+    alignItems: 'center',\n+    height: 90,\n+    marginBottom: 20,\n+    zIndex: $Z_INDEXES.z200 + 1,\n+    gap: 12\n+  },\n+  $avatarContainer: {\n+    position: 'relative'\n+  },\n+  $infoContainer: {\n+    flex: 1,\n+    justifyContent: 'center'\n+  },\n+  $pendantTipContainer: {\n+    position: 'absolute',\n+    top: -20,\n+    right: -30\n+  },\n+  $pendantTip: {\n+    width: 91,\n+    height: 45,\n+    position: 'absolute'\n+  },\n+  $pendantContent: {\n+    position: 'absolute',\n+    flexDirection: 'row',\n+    justifyContent: 'center',\n+    alignItems: 'center',\n+    gap: 2,\n+    paddingLeft: 6,\n+    top: 4,\n+    left: 4\n+  },\n+  $pendantImage: {\n+    width: 20,\n+    height: 20\n+  },\n+  $dotTip: {\n+    width: 8,\n+    height: 8,\n+    backgroundColor: '#fff',\n+    borderRadius: 4,\n+    left: 4,\n+    top: 30,\n+    position: 'absolute'\n+  },\n+  $newPendantText: {\n+    fontSize: 11,\n+    fontWeight: '600'\n+  },\n+  $lottieContainer: {\n+    marginTop: 5\n+  },\n+  $lottieAnimation: {\n+    width: 111,\n+    height: 21\n+  },\n+  $lottieCustomAnimation: {\n+    width: 139,\n+    height: 21\n+  }\n+});\n"}, {"new_path": "src/bizComponents/userScreen/components/UserPanel.tsx", "old_path": "src/bizComponents/userScreen/components/UserPanel.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -0,0 +1,334 @@\n+import { useMemoizedFn } from 'ahooks';\n+import React, { ReactElement, RefObject, useMemo } from 'react';\n+import { Pressable, TextStyle, View } from 'react-native';\n+import PagerView from 'react-native-pager-view';\n+import Animated, {\n+  SharedValue,\n+  useAnimatedStyle,\n+  useDerivedValue,\n+  useSharedValue,\n+  withTiming\n+} from 'react-native-reanimated';\n+import { useSafeAreaInsets } from 'react-native-safe-area-context';\n+import { UserHeaderRight } from '@/src/bizComponents/userScreen/components/UserHeader';\n+import { Follow } from '@/src/components/follow';\n+import { SimpleTabs } from '@/src/components/tabs/simple-tabs';\n+import { darkTheme } from '@/src/theme';\n+import { UserSocialStat } from '@/src/types';\n+import { CommonEventBus } from '@/src/utils/event';\n+import { reportClick } from '@/src/utils/report';\n+import { Icon, Image, Text } from '../../../components';\n+import { DynamicWidthTabs } from '../../../components/tabs/dynamic-tabs';\n+import { StyleSheet, dp2px, fixedPx, isIos } from '../../../utils';\n+import {\n+  UserPageTab,\n+  authorPageTabMap,\n+  authorTabs,\n+  visitorPageTabMap,\n+  visitorTabs\n+} from '../constants';\n+import { calculatePanelHeight } from '../utils';\n+import { UserProfile } from '@/proto-registry/src/web/raccoon/uinfo/uinfo_pb';\n+import { MineButtons } from './MineButtons';\n+import { MineCards } from './MineCards';\n+import { UserInfo } from './UserInfo';\n+import { UserStats } from './UserStats';\n+\n+const GOODS_MASK = require('@Assets/image/goods-shef/mask.png');\n+const MASK_HEIGHT = dp2px(120);\n+const BACKGROUND_OFFSET = -0.3; // 背景底部偏移量，防止安卓有一小段空隙\n+\n+// 定义标签项类型\n+export interface TabItem {\n+  key: UserPageTab;\n+  title: string;\n+  width: number;\n+  renderItem?: (props: {\n+    isActive: boolean;\n+    textStyle: TextStyle[];\n+  }) => ReactElement;\n+}\n+\n+interface UserPanelProps {\n+  currentUser: UserProfile;\n+  stat?: UserSocialStat;\n+  isMine: boolean;\n+  showFeedBack: () => void;\n+  hasGoods: boolean;\n+  currentTab: UserPageTab;\n+  $animatedIndictor: SharedValue<number>;\n+  pagerRef: RefObject<PagerView>;\n+  onAlbumPress: () => void;\n+  safeTop: number;\n+  hasSlideUp?: SharedValue<boolean>;\n+  checkEnterGoods?: () => void;\n+  tabConfig?: TabItem[]; // 使用明确的类型\n+}\n+\n+export function UserPanel({\n+  currentUser,\n+  stat,\n+  isMine,\n+  showFeedBack,\n+  currentTab,\n+  $animatedIndictor,\n+  pagerRef,\n+  onAlbumPress,\n+  hasGoods,\n+  safeTop,\n+  hasSlideUp,\n+  checkEnterGoods,\n+  tabConfig\n+}: UserPanelProps) {\n+  // 获取所有可能的高度配置\n+  const heightConfigs = useMemo(() => calculatePanelHeight(), []);\n+\n+  // 计算固定值，避免在UI线程中计算\n+  const maskHeightWithOffset = useMemo(\n+    () => MASK_HEIGHT + BACKGROUND_OFFSET,\n+    []\n+  );\n+  const backgroundTopOffset = useMemo(\n+    () => safeTop + maskHeightWithOffset,\n+    [safeTop]\n+  );\n+\n+  // 为动画创建共享值\n+  const $topHeight = useSharedValue(\n+    isMine\n+      ? heightConfigs.mineNoGoods.topHeight\n+      : heightConfigs.visitorNoGoods.topHeight\n+  );\n+  const $maskTop = useSharedValue(\n+    isMine\n+      ? heightConfigs.mineNoGoods.maskTop\n+      : heightConfigs.visitorNoGoods.maskTop\n+  );\n+\n+  // 使用 useDerivedValue 替代 useEffect 来更新共享值\n+  useDerivedValue(() => {\n+    'worklet';\n+    const config = isMine\n+      ? hasGoods\n+        ? heightConfigs.mineWithGoods\n+        : heightConfigs.mineNoGoods\n+      : hasGoods\n+        ? heightConfigs.visitorWithGoods\n+        : heightConfigs.visitorNoGoods;\n+\n+    $topHeight.value = withTiming(config.topHeight, { duration: 300 });\n+    $maskTop.value = withTiming(config.maskTop, { duration: 300 });\n+  }, [isMine, hasGoods]);\n+\n+  const onPressTab = useMemoizedFn((_, type) => {\n+    if (type === UserPageTab.SECRET) {\n+      reportClick('secret_button', { type: 'video' });\n+    } else if (type === UserPageTab.MY_ROLE) {\n+      reportClick('role_button');\n+    } else {\n+      reportClick('button', {\n+        user_button: type === UserPageTab.LIKE ? 9 : 8,\n+        identity_status: isMine ? '0' : '1'\n+      });\n+    }\n+\n+    const tabMap = isMine ? authorPageTabMap : visitorPageTabMap;\n+    const tab = tabMap[type as UserPageTab];\n+    pagerRef.current?.setPage(tab || 0);\n+  });\n+\n+  const $buttonsStyle = useAnimatedStyle(() => {\n+    return {\n+      opacity: hasSlideUp?.value ? 0 : 1\n+    };\n+  }, [hasSlideUp]);\n+\n+  // 用于容器高度的动画样式\n+  const $containerStyle = useAnimatedStyle(() => {\n+    'worklet';\n+    return {\n+      height: $topHeight.value + safeTop\n+    };\n+  });\n+\n+  // 用于背景底部的动画样式\n+  const $backgroundStyle = useAnimatedStyle(() => {\n+    'worklet';\n+    return {\n+      top: $maskTop.value + backgroundTopOffset\n+    };\n+  });\n+\n+  // 用于遮罩的动画样式\n+  const $maskStyle = useAnimatedStyle(() => {\n+    'worklet';\n+    return {\n+      position: 'absolute',\n+      left: 0,\n+      right: 0,\n+      top: $maskTop.value + safeTop,\n+      zIndex: 0\n+    };\n+  });\n+\n+  // 背景点击区域的高度\n+  const clickAreaHeight = useMemo(\n+    () => (hasGoods ? dp2px(179) : dp2px(90)),\n+    [hasGoods]\n+  );\n+\n+  return (\n+    <>\n+      <Animated.View style={[styles.$backgroundBottom, $backgroundStyle]} />\n+      <Animated.View style={[styles.$container, $containerStyle]}>\n+        {/* 添加背景墙点击区域 - 改为普通Pressable */}\n+        <Pressable\n+          style={[\n+            styles.$backgroundClickArea,\n+            { top: safeTop, height: clickAreaHeight }\n+          ]}\n+          onPress={checkEnterGoods}\n+        />\n+\n+        {/* 将 Image 放入 Animated.View 中 */}\n+        <Animated.View style={$maskStyle}>\n+          <Image\n+            source={GOODS_MASK}\n+            // 使用 native 会导致背景渐变效果失效\n+            contentFit=\"fill\"\n+            contentPosition=\"top\"\n+            style={styles.$goodsMask}\n+            onLoadEnd={() => {\n+              CommonEventBus.emit('goodsMaskLoaded', { loaded: true });\n+            }}\n+          />\n+        </Animated.View>\n+\n+        <UserInfo\n+          currentUser={currentUser}\n+          isMine={isMine}\n+          showFeedBack={showFeedBack}\n+        />\n+        <View style={styles.$statsWrap}>\n+          <UserStats\n+            stat={stat}\n+            isMine={isMine}\n+            currentUserId={currentUser?.uid}\n+            currentUserName={currentUser?.name}\n+          />\n+          {isMine && <MineButtons />}\n+          <Animated.View style={$buttonsStyle}>\n+            {!isMine && stat && (\n+              <UserHeaderRight\n+                profile={currentUser}\n+                stat={stat}\n+                isMine={isMine}\n+              />\n+            )}\n+          </Animated.View>\n+        </View>\n+\n+        {/* MineCards - 仅在个人主页显示 */}\n+        {isMine && <MineCards />}\n+\n+        {/* Tabs与图集按钮 */}\n+        <View style={styles.$tabContainer}>\n+          <DynamicWidthTabs\n+            current={currentTab}\n+            items={tabConfig || (isMine ? authorTabs : visitorTabs)}\n+            animatedTabIndex={$animatedIndictor}\n+            tabBarStyle={styles.$tabBar}\n+            tabGap={0}\n+            itemStyle={styles.$tabItemStyle}\n+            itemTextStyle={styles.$tabItemTextStyle}\n+            indicatorBottomOffset={-6}\n+            onPressTab={onPressTab}\n+          />\n+          {isMine && (\n+            <Pressable style={styles.$albumButton} onPress={onAlbumPress}>\n+              <Text style={styles.$albumButtonText}>图集</Text>\n+              <Icon\n+                icon=\"right_outline2\"\n+                size={16}\n+                color={darkTheme.text.disabled}\n+              />\n+            </Pressable>\n+          )}\n+        </View>\n+      </Animated.View>\n+    </>\n+  );\n+}\n+\n+const styles = StyleSheet.create({\n+  $container: {\n+    width: '100%',\n+    position: 'relative',\n+    justifyContent: 'flex-end',\n+    paddingLeft: 16,\n+    paddingRight: 16\n+  },\n+  $backgroundBottom: {\n+    position: 'absolute',\n+    left: 0,\n+    right: 0,\n+    bottom: 0,\n+    backgroundColor: darkTheme.background.page,\n+    zIndex: -1\n+  },\n+  $goodsMask: {\n+    left: 0,\n+    right: 0,\n+    width: '100%',\n+    height: fixedPx(MASK_HEIGHT),\n+    position: 'absolute',\n+    zIndex: 0\n+  },\n+  $statsWrap: {\n+    flexDirection: 'row',\n+    alignItems: 'center',\n+    justifyContent: 'space-between',\n+    zIndex: 1\n+  },\n+  $tabContainer: {\n+    position: 'relative',\n+    flexDirection: 'row',\n+    alignItems: 'center',\n+    width: '100%',\n+    zIndex: 1,\n+    marginTop: 8\n+  },\n+  $tabBar: {\n+    marginLeft: fixedPx(-16),\n+    height: 44\n+  },\n+  $tabItemStyle: {\n+    justifyContent: 'center',\n+    alignItems: 'center',\n+    height: 18\n+  },\n+  $tabItemTextStyle: {\n+    fontSize: 14,\n+    textAlign: 'center',\n+    fontWeight: '500'\n+  },\n+  $albumButton: {\n+    position: 'absolute',\n+    right: 0,\n+    height: 44,\n+    justifyContent: 'center',\n+    alignItems: 'center',\n+    flexDirection: 'row'\n+  },\n+  $albumButtonText: {\n+    marginRight: 1,\n+    fontSize: 14,\n+    color: darkTheme.text.disabled\n+  },\n+  $backgroundClickArea: {\n+    position: 'absolute',\n+    left: 0,\n+    right: 0,\n+    zIndex: 200\n+  }\n+});\n"}, {"new_path": "src/bizComponents/userScreen/components/UserStats.tsx", "old_path": "src/bizComponents/userScreen/components/UserStats.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -0,0 +1,150 @@\n+import { useMemoizedFn } from 'ahooks';\n+import { router } from 'expo-router';\n+import { Pressable, TextStyle, View, ViewStyle } from 'react-native';\n+import { Text } from '@/src/components';\n+import { typography } from '@/src/theme';\n+import { UserSocialStat } from '@/src/types';\n+import { formatUserNumber } from '@/src/utils/opt/transNum';\n+import { reportClick } from '@/src/utils/report';\n+import { showImageConfirm } from '@Components/popup/confirmModalGlobal/Confirm';\n+import { StyleSheet } from '@Utils/StyleSheet';\n+import { Theme } from '../../../theme/colors/type';\n+import dp2px from '../../../utils/dp2px';\n+\n+const LIKE_COVER = require('@Assets/user/userLike.png');\n+\n+const HIT_SLOP = { top: 10, bottom: 10, left: 15, right: 15 };\n+\n+export function UserStats({\n+  stat,\n+  isMine,\n+  currentUserId,\n+  currentUserName\n+}: {\n+  stat?: UserSocialStat;\n+  isMine?: boolean;\n+  currentUserId?: string;\n+  currentUserName?: string;\n+}) {\n+  const onPressFollowAndFans = useMemoizedFn((defaultTab: string) => {\n+    reportClick('button', {\n+      user_button: defaultTab === 'follow' ? 4 : 5,\n+      identity_status: isMine ? '0' : '1'\n+    });\n+    router.push({\n+      pathname: '/follow-fan/',\n+      params: {\n+        defaultTab,\n+        uid: currentUserId || ''\n+      }\n+    });\n+  });\n+\n+  const showLikeModal = useMemoizedFn(() => {\n+    reportClick('button', {\n+      user_button: 6,\n+      identity_status: isMine ? '0' : '1'\n+    });\n+    showImageConfirm({\n+      image: LIKE_COVER,\n+      title: currentUserName,\n+      content: `共获得${stat?.beingLikeds}个赞`,\n+      confirmText: '知道了',\n+      cancelText: '#hiddenCancel#',\n+      onConfirm: ({ close }) => {\n+        close();\n+      },\n+      theme: Theme.DARK\n+    });\n+  });\n+\n+  const showSameModal = useMemoizedFn(() => {\n+    reportClick('button', {\n+      user_button: 7,\n+      identity_status: isMine ? '0' : '1'\n+    });\n+\n+    router.push({\n+      pathname: `/fame/${currentUserId}`,\n+      params: {\n+        identity_status: isMine ? 0 : 1\n+      }\n+    });\n+  });\n+\n+  return (\n+    <View style={styles.$numsWrap}>\n+      <Pressable\n+        onPress={() => onPressFollowAndFans('follow')}\n+        hitSlop={HIT_SLOP}\n+        style={styles.$numItem}\n+      >\n+        <Text style={styles.$num}>\n+          {formatUserNumber(stat?.followings?.toLocaleString() || 0)}\n+        </Text>\n+        <Text style={styles.$label}>关注</Text>\n+      </Pressable>\n+      <Pressable\n+        onPress={() => onPressFollowAndFans('fans')}\n+        hitSlop={HIT_SLOP}\n+        style={styles.$numItem}\n+      >\n+        <Text style={styles.$num}>\n+          {formatUserNumber(stat?.fans?.toLocaleString() || 0)}\n+        </Text>\n+        <Text style={styles.$label}>粉丝</Text>\n+      </Pressable>\n+      <Pressable\n+        onPress={showLikeModal}\n+        hitSlop={HIT_SLOP}\n+        style={styles.$numItem}\n+      >\n+        <Text style={styles.$num}>\n+          {formatUserNumber(stat?.beingLikeds?.toLocaleString() || 0)}\n+        </Text>\n+        <Text style={styles.$label}>获赞</Text>\n+      </Pressable>\n+      <Pressable\n+        onPress={showSameModal}\n+        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}\n+        style={styles.$numItem}\n+      >\n+        <Text style={styles.$num}>\n+          {formatUserNumber(stat?.beingCopieds?.toLocaleString() || 0)}\n+        </Text>\n+        <Text style={styles.$label}>声望</Text>\n+      </Pressable>\n+    </View>\n+  );\n+}\n+\n+const styles = StyleSheet.create({\n+  $numsWrap: {\n+    maxWidth: dp2px(196),\n+    flex: 1,\n+    flexDirection: 'row',\n+    justifyContent: 'space-between'\n+  },\n+  $numItem: {\n+    alignItems: 'center',\n+    flexDirection: 'column',\n+    paddingBottom: 2\n+  },\n+  $num: {\n+    fontSize: dp2px(18),\n+    height: 20,\n+    textAlign: 'center',\n+    fontFamily: typography.fonts.Barlow.SemiBold,\n+    color: StyleSheet.darkTheme.text.primary,\n+    letterSpacing: -0.02\n+  },\n+  $label: {\n+    color: StyleSheet.darkTheme.text.disabled,\n+    marginTop: 4,\n+    fontSize: dp2px(12),\n+    lineHeight: dp2px(17),\n+    height: 17,\n+    fontWeight: '500',\n+    textAlign: 'center'\n+  }\n+});\n"}, {"new_path": "src/bizComponents/userScreen/hooks/useHandleSwipeBack.ts", "old_path": "src/bizComponents/userScreen/hooks/useHandleSwipeBack.ts", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -0,0 +1,32 @@\n+import { useMemoizedFn } from 'ahooks';\n+import { useEffect } from 'react';\n+import { CommonEventBus } from '@/src/utils/event';\n+import { useRetentionPopup } from './useRetentionPopup';\n+\n+export const useHandleSwipeBack = (\n+  checkRetentionAndReport: ReturnType<\n+    typeof useRetentionPopup\n+  >['checkRetentionAndReport']\n+) => {\n+  // 使用 useMemoizedFn 包装事件处理函数，确保函数引用稳定\n+  const handleUserScreenBack = useMemoizedFn(\n+    (data: { resolve: () => void; ownerUid: string }) => {\n+      const { resolve } = data;\n+      const shouldShowPopup = checkRetentionAndReport(resolve);\n+      if (!shouldShowPopup) {\n+        // 如果不需要显示弹窗，正常返回\n+        resolve();\n+      }\n+      // 否则阻止返回，不调用 resolve\n+    }\n+  );\n+\n+  // 注册事件处理函数\n+  useEffect(() => {\n+    CommonEventBus.on('handleUserScreenBack', handleUserScreenBack);\n+\n+    return () => {\n+      CommonEventBus.off('handleUserScreenBack', handleUserScreenBack);\n+    };\n+  }, [handleUserScreenBack]);\n+};\n"}, {"new_path": "src/bizComponents/userScreen/hooks/useRetentionPopup.ts", "old_path": "src/bizComponents/userScreen/hooks/useRetentionPopup.ts", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": ""}, {"new_path": "src/bizComponents/userScreen/infoProfile/index.tsx", "old_path": "src/bizComponents/userScreen/infoProfile/index.tsx", "deleted_file": true, "new_file": false, "renamed_file": false, "diff": "@@ -1,499 +0,0 @@\n-import { useMemoizedFn } from 'ahooks';\n-import dayjs from 'dayjs';\n-import { router } from 'expo-router';\n-import AnimatedLottieView, {\n-  AnimatedLottieViewProps\n-} from 'lottie-react-native';\n-import {\n-  Component,\n-  LegacyRef,\n-  memo,\n-  useEffect,\n-  useMemo,\n-  useRef,\n-  useState\n-} from 'react';\n-import {\n-  Text,\n-  TextStyle,\n-  TouchableOpacity,\n-  View,\n-  ViewStyle\n-} from 'react-native';\n-import Animated, {\n-  AnimateProps,\n-  useAnimatedStyle,\n-  useSharedValue\n-} from 'react-native-reanimated';\n-import { Icon } from '@/src/components';\n-import { Avatar } from '@/src/components/avatar';\n-import { CREDIT_LIMIT } from '@/src/components/credit-cas';\n-import { Follow } from '@/src/components/follow';\n-import { getSkinConfig } from '@/src/components/skin/getSkinConfig';\n-import { SwitchName, useControlStore } from '@/src/store/control';\n-import { useCreditStore } from '@/src/store/credit';\n-import { useResourceStore } from '@/src/store/resource';\n-import { useStorageStore } from '@/src/store/storage';\n-import { useUserInfoStore } from '@/src/store/userInfo';\n-import { darkTheme, typography } from '@/src/theme';\n-import { $USE_FONT, $Z_INDEXES, $flexCenter } from '@/src/theme/variable';\n-import { dp2px, isIos } from '@/src/utils';\n-import { stirngRemoveEnter } from '@/src/utils/opt/replace';\n-import { reportClick, reportDiy, reportExpo } from '@/src/utils/report';\n-import { Image, ImageStyle } from '@Components/image';\n-import { StyleSheet } from '@Utils/StyleSheet';\n-import { UserProfile } from '@/proto-registry/src/web/raccoon/uinfo/uinfo_pb';\n-import { useIsFocused } from '@react-navigation/native';\n-import TransparentVideo from '@step.ai/react-native-transparent-video';\n-import { useShallow } from 'zustand/react/shallow';\n-\n-const RED_LIGHT = require('@Assets/lottie/redlight.json');\n-const GREEN_LIGHT = require('@Assets/lottie/greenlight.json');\n-\n-interface TInfoProfileProps {\n-  isRootPage: boolean;\n-  isMine: boolean;\n-  creditOpacity: number;\n-  id: string;\n-  showAvatar?: boolean;\n-  currentUser?: UserProfile;\n-  showFeedBack?: () => void;\n-  onBack?: () => void;\n-  showBackButton?: boolean;\n-}\n-\n-const InfoProfile = memo(\n-  ({\n-    isRootPage,\n-    isMine,\n-    creditOpacity,\n-    id,\n-    showAvatar,\n-    currentUser,\n-    showFeedBack,\n-    onBack,\n-    showBackButton = true\n-  }: TInfoProfileProps) => {\n-    const skinConfig = getSkinConfig();\n-\n-    const creditShareOpacity = useSharedValue(0);\n-    const videoRef = useRef<TransparentVideo | null>(null);\n-\n-    const creditStyle = useAnimatedStyle(() => ({\n-      opacity: creditShareOpacity.value\n-    }));\n-\n-    const { totalCredits } = useCreditStore(\n-      useShallow(state => ({\n-        totalCredits: state.totalCredits\n-      }))\n-    );\n-\n-    useEffect(() => {\n-      const getCredits = async () => {\n-        await useCreditStore.getState().syncCredits();\n-        creditShareOpacity.value = 1;\n-      };\n-      isMine && getCredits();\n-    }, [isMine]);\n-\n-    useEffect(() => {\n-      if (creditOpacity) {\n-        creditShareOpacity.value = 1;\n-      } else {\n-        creditShareOpacity.value = 0;\n-      }\n-    }, [creditOpacity]);\n-    const lightRef = useRef<AnimatedLottieView>(null);\n-\n-    const isMinus = totalCredits < CREDIT_LIMIT ? true : false;\n-    const lottieSource = isMinus ? RED_LIGHT : GREEN_LIGHT;\n-\n-    useEffect(() => {\n-      lightRef.current?.play();\n-      const timer = setInterval(() => {\n-        lightRef.current?.play();\n-      }, 10 * 1000);\n-      return () => {\n-        clearInterval(timer);\n-      };\n-    }, []);\n-\n-    const { profile, stat, updateStat } = useUserInfoStore(\n-      useShallow(state => {\n-        const userInfo = id ? state.getUserInfo(id) : undefined;\n-        return {\n-          profile: userInfo?.profile,\n-          stat: userInfo?.stat,\n-          updateStat: state.updateStat\n-        };\n-      })\n-    );\n-\n-    const onUpdatefollow = (followed: boolean) => {\n-      reportClick('follow_button', { userId: id, followed });\n-      updateStat(id, {\n-        followed\n-      });\n-    };\n-\n-    const handleEditAvatar = useMemoizedFn(() => {\n-      reportClick('button', {\n-        user_button: 2,\n-        identity_status: isMine ? '0' : '1'\n-      });\n-      if (isMine) {\n-        router.push('/avatar-edit/');\n-      } else {\n-        if (\n-          useControlStore.getState().checkIsOpen(SwitchName.ENABLE_USER_REPORT)\n-        ) {\n-          showFeedBack?.();\n-        }\n-      }\n-    });\n-\n-    const currentUserName = stirngRemoveEnter(currentUser?.name);\n-    const lotteryVideo = useMemo(\n-      () => ({\n-        uri: useResourceStore\n-          .getState()\n-          .getResource(\n-            'https://mediafile.lipuhome.com/aigc/VIDEO/20250303/f3b57db8c24c52ba2994444a7907d01d.mp4'\n-          )\n-      }),\n-      []\n-    );\n-\n-    const isFocus = useIsFocused();\n-\n-    const [showDailyTip, setShowDailyTip] = useState(false);\n-\n-    useEffect(() => {\n-      const isDailyFissionGuide =\n-        useStorageStore.getState().isDailyFissionGuide;\n-      if (\n-        !isDailyFissionGuide ||\n-        (isDailyFissionGuide &&\n-          isDailyFissionGuide - dayjs().startOf('day').valueOf() < 0)\n-      ) {\n-        useStorageStore.getState().__setStorage({\n-          isDailyFissionGuide: Date.now()\n-        });\n-        setShowDailyTip(true);\n-      } else {\n-        setShowDailyTip(false);\n-      }\n-    }, [isFocus]);\n-\n-    return (\n-      <View\n-        style={{\n-          flexDirection: 'row',\n-          width: '100%',\n-          justifyContent: 'space-between',\n-          alignItems: 'center',\n-          minHeight: 58,\n-          position: 'relative',\n-          paddingHorizontal: 16\n-        }}\n-      >\n-        {!isRootPage ? (\n-          <View style={{ width: 24, height: 24 }}>\n-            {showBackButton && (\n-              <TouchableOpacity\n-                onPressIn={() => {\n-                  reportClick('button', {\n-                    user_button: 1,\n-                    identity_status: isMine ? '0' : '1'\n-                  });\n-                  onBack ? onBack() : router.back();\n-                }}\n-              >\n-                <Icon icon={skinConfig ? 'back_pw' : 'back'} size={24} />\n-              </TouchableOpacity>\n-            )}\n-          </View>\n-        ) : (\n-          <View />\n-        )}\n-        <View\n-          style={{\n-            flexDirection: 'row',\n-            position: 'relative',\n-            justifyContent: 'space-between',\n-            flex: 1,\n-            marginTop: 4\n-          }}\n-        >\n-          {showAvatar ? (\n-            <Animated.View\n-              style={{\n-                flexDirection: 'row',\n-                alignItems: 'center',\n-                flex: 1\n-              }}\n-            >\n-              <View style={{ width: 32, height: 'auto', marginRight: 10 }}>\n-                <Avatar\n-                  profile={currentUser}\n-                  size={32}\n-                  showTag={true}\n-                  showPendant={creditOpacity < 1}\n-                  onPress={handleEditAvatar}\n-                  borderWidth={2}\n-                  borderColor={'#262629'}\n-                />\n-              </View>\n-              <Text\n-                ellipsizeMode=\"tail\"\n-                numberOfLines={1}\n-                allowFontScaling={false}\n-                style={{\n-                  width: 180,\n-                  ...$USE_FONT(\n-                    isMine && currentUser?.isNameUpdated === false\n-                      ? darkTheme.text.disabled\n-                      : darkTheme.text.primary,\n-                    typography.fonts.baba.medium,\n-                    dp2px(14),\n-                    'normal',\n-                    isIos ? '500' : 'bold',\n-                    dp2px(19.6)\n-                  )\n-                }}\n-              >\n-                {currentUserName}\n-              </Text>\n-            </Animated.View>\n-          ) : isMine ? (\n-            <>\n-              <Animated.View style={[creditStyle]}>\n-                <TouchableOpacity\n-                  onPressIn={() => {\n-                    reportDiy('credit', 'entrance_button-click');\n-                    router.push('/credit/');\n-                  }}\n-                  style={{\n-                    marginEnd: 16,\n-                    width: 32,\n-                    height: 32,\n-                    transform: [\n-                      {\n-                        translateY: 0\n-                      }\n-                    ]\n-                  }}\n-                >\n-                  <Icon\n-                    icon={!isMinus ? 'credit_minus' : 'credit_plus'}\n-                    size={24}\n-                    style={{ opacity: 0 }}\n-                  />\n-                  <View style={$creditLottie}>\n-                    <AnimatedLottieView\n-                      source={lottieSource}\n-                      ref={\n-                        lightRef as LegacyRef<\n-                          Component<AnimateProps<AnimatedLottieViewProps>>\n-                        >\n-                      }\n-                      loop={false}\n-                    />\n-                  </View>\n-                </TouchableOpacity>\n-              </Animated.View>\n-              <TouchableOpacity\n-                style={st.lotteryEntry}\n-                onPress={() => {\n-                  reportClick('invite_icon');\n-                  router.push('/lottery-fission/');\n-                }}\n-              >\n-                {/* 目前没有 unload api */}\n-                {isFocus ? (\n-                  <TransparentVideo\n-                    ref={videoRef}\n-                    isAutoPlay\n-                    loop\n-                    style={st.lotteryEntryVideo}\n-                    source={lotteryVideo}\n-                  />\n-                ) : null}\n-                {showDailyTip ? (\n-                  <View style={[$dailyTip]}>\n-                    <Image\n-                      style={{\n-                        width: 120,\n-                        height: 36\n-                      }}\n-                      source={\n-                        'https://resource.lipuhome.com/resource/img/prod/20250307/82994ac4159c85c133be6c132b71d8d0.png'\n-                      }\n-                    />\n-                    <Text\n-                      numberOfLines={1}\n-                      style={{\n-                        ...$USE_FONT(\n-                          'StyleSheet.currentColors.titleGray',\n-                          typography.fonts.pingfangSC.normal,\n-                          dp2px(12),\n-                          'normal',\n-                          isIos ? '500' : 'bold',\n-                          dp2px(18)\n-                        ),\n-                        position: 'absolute',\n-                        top: 12\n-                      }}\n-                    >\n-                      邀好友得狸电池\n-                    </Text>\n-                  </View>\n-                ) : null}\n-              </TouchableOpacity>\n-            </>\n-          ) : null}\n-          {isMine ? (\n-            <View\n-              style={{\n-                flexDirection: 'row',\n-                alignItems: 'center',\n-                flex: 1,\n-                justifyContent: 'flex-end'\n-              }}\n-            >\n-              <TouchableOpacity\n-                activeOpacity={0.3}\n-                style={[\n-                  $creditBtn,\n-                  {\n-                    backgroundColor: '#00000099', // 暗色模式无法覆盖\n-                    marginRight: isIos ? 16 : 4\n-                  }\n-                ]}\n-                onPress={() => {\n-                  router.push('/profile/edit');\n-                  setTimeout(() => {\n-                    reportClick('edit_profile');\n-                  });\n-                }}\n-              >\n-                <Text\n-                  style={[\n-                    $creditBtnText,\n-                    {\n-                      color: darkTheme.text.primary\n-                    }\n-                  ]}\n-                >\n-                  编辑资料\n-                </Text>\n-              </TouchableOpacity>\n-\n-              <TouchableOpacity\n-                style={{\n-                  width: 24,\n-                  height: 24,\n-                  alignItems: 'center',\n-                  justifyContent: 'center'\n-                }}\n-                onPress={() => {\n-                  reportClick('button', {\n-                    user_button: 0,\n-                    identity_status: isMine ? '0' : '1'\n-                  });\n-                  router.push('/setting/');\n-                }}\n-              >\n-                <Icon\n-                  color={darkTheme.text.solid}\n-                  icon={skinConfig ? 'setting_pw' : 'setting'}\n-                  size={24}\n-                />\n-              </TouchableOpacity>\n-            </View>\n-          ) : null}\n-          {profile ? (\n-            <>\n-              <View />\n-              <Follow\n-                followed={!!stat?.followed}\n-                beingFollowed={!!stat?.beingFollowed}\n-                uid={profile?.uid}\n-                onUnfollow={() => onUpdatefollow(false)}\n-                onFollow={() => onUpdatefollow(true)}\n-              />\n-            </>\n-          ) : null}\n-        </View>\n-      </View>\n-    );\n-  }\n-);\n-export default InfoProfile;\n-\n-const st = StyleSheet.create({\n-  lotteryEntryVideo: {\n-    width: 34,\n-    height: 34\n-  },\n-  lotteryEntry: {\n-    width: 34,\n-    height: 34,\n-    marginTop: -9,\n-    marginLeft: -12\n-  }\n-});\n-\n-const $creditLottie: ViewStyle = {\n-  width: 70,\n-  height: 70,\n-  position: 'absolute',\n-  pointerEvents: 'none',\n-  right: 0,\n-  bottom: 8,\n-  zIndex: $Z_INDEXES.zm1,\n-  transform: [\n-    {\n-      translateX: dp2px(18)\n-    },\n-    {\n-      translateY: dp2px(23)\n-    }\n-  ]\n-};\n-\n-const $creditBtn: ViewStyle = {\n-  width: 72,\n-  height: 26,\n-  borderRadius: 100,\n-  borderColor: 'rgba(0, 0, 0, 0.12)',\n-  borderWidth: 0.5,\n-  // paddingHorizontal: 12,\n-  // paddingVertical: 4,\n-  marginRight: 16,\n-  alignItems: 'center',\n-  justifyContent: 'center',\n-  top: -1\n-};\n-\n-const $creditBtnText: TextStyle = {\n-  ...$USE_FONT(\n-    StyleSheet.currentColors.titleGray,\n-    typography.fonts.pingfangSC.normal,\n-    dp2px(12),\n-    'normal',\n-    '600',\n-    dp2px(18)\n-  )\n-};\n-\n-const $dailyTip: ImageStyle = {\n-  position: 'absolute',\n-  width: 120,\n-  height: 36,\n-  bottom: -48,\n-  left: -41,\n-  borderRadius: 8,\n-  ...$flexCenter\n-};\n"}, {"new_path": "src/bizComponents/userScreen/infoSection/index.tsx", "old_path": "src/bizComponents/userScreen/infoSection/index.tsx", "deleted_file": true, "new_file": false, "renamed_file": false, "diff": "@@ -1,552 +0,0 @@\n-import { useMemoizedFn } from 'ahooks';\n-import { router } from 'expo-router';\n-import Animated<PERSON>ottieView from 'lottie-react-native';\n-import { useEffect, useMemo, useRef, useState } from 'react';\n-import {\n-  ImageStyle,\n-  Pressable,\n-  Text,\n-  TextStyle,\n-  TouchableOpacity,\n-  View,\n-  ViewStyle\n-} from 'react-native';\n-import { ShadowedView } from 'react-native-fast-shadow';\n-import Animated, {\n-  AnimatedStyle,\n-  AnimatedStyleProp,\n-  SharedValue,\n-  useAnimatedStyle,\n-  useSharedValue,\n-  withSpring\n-} from 'react-native-reanimated';\n-import { useSafeAreaInsets } from 'react-native-safe-area-context';\n-import { Icon } from '@/src/components';\n-import { Avatar } from '@/src/components/avatar';\n-import { SkinnedImage } from '@/src/components/skin/SkinnedImage';\n-import { CONFIG_KYES } from '@/src/components/skin/getSkinConfig';\n-import { useScreenSize } from '@/src/hooks';\n-import { useBehaviorStore } from '@/src/store/behavior';\n-import { SwitchName, useControlStore } from '@/src/store/control';\n-import { useEditPendantStore } from '@/src/store/edit-pendant';\n-import { centerStyle, darkTheme, rowStyle, typography } from '@/src/theme';\n-import { Theme } from '@/src/theme/colors/type';\n-import { $USE_FONT, $Z_INDEXES, $flexHCenter } from '@/src/theme/variable';\n-import { UserSocialStat } from '@/src/types';\n-import { dp2px, isIos } from '@/src/utils';\n-import { formatTosUrl } from '@/src/utils/getTosUrl';\n-import { stirngRemoveEnter } from '@/src/utils/opt/replace';\n-import { formatUserNumber } from '@/src/utils/opt/transNum';\n-import { reportClick } from '@/src/utils/report';\n-import { Image } from '@Components/image';\n-import { showImageConfirm } from '@Components/popup/confirmModalGlobal/Confirm';\n-import { StyleSheet } from '@Utils/StyleSheet';\n-import INFO_MASK from '@Assets/image/goods-shef/info-mask.png';\n-import INTRO_CUSTOM_PULL from '@Assets/lottie/goods/intro_custom_pull.json';\n-import INTRO_PULL from '@Assets/lottie/goods/intro_pull.json';\n-import { userPerformanceCollector } from '../../../utils/report/userPageCollector';\n-import { UserProfile } from '@/proto-registry/src/web/raccoon/uinfo/uinfo_pb';\n-import { useIsFocused } from '@react-navigation/native';\n-import { useShallow } from 'zustand/react/shallow';\n-\n-const LIKE_COVER = require('@Assets/user/userLike.png');\n-const PENDANT_TIP_BG = require('@Assets/user/pendant-tip-bg.png');\n-\n-export function InfoSection({\n-  currentUser,\n-  stat,\n-  isMine,\n-  showFeedBack,\n-  isRootPage,\n-  animateStyle,\n-  isAllowed = false,\n-  isSlideToTop,\n-  goodsLikes,\n-  hasGoods,\n-  checkEnterGoods\n-}: {\n-  currentUser?: UserProfile;\n-  stat?: UserSocialStat;\n-  isMine?: boolean;\n-  showFeedBack?: () => void;\n-  isRootPage?: boolean;\n-  animateStyle: ViewStyle;\n-  isAllowed: boolean;\n-  isSlideToTop: boolean;\n-  goodsLikes?: number;\n-  hasGoods?: boolean;\n-  checkEnterGoods: () => void;\n-}) {\n-  const { newPendant, fetchNewPendant } = useEditPendantStore(\n-    useShallow(state => ({\n-      newPendant: state.newPendant,\n-      fetchNewPendant: state.fetchNewPendant\n-    }))\n-  );\n-  const isFocus = useIsFocused();\n-\n-  useEffect(() => {\n-    if (isFocus && isMine) {\n-      fetchNewPendant();\n-    }\n-  }, [isFocus, isMine]);\n-\n-  const currentUserName = stirngRemoveEnter(currentUser?.name);\n-\n-  const handleEditAvatar = useMemoizedFn(() => {\n-    reportClick('button', {\n-      user_button: 2,\n-      identity_status: isMine ? '0' : '1'\n-    });\n-    if (isMine) {\n-      router.push('/avatar-edit/');\n-    } else {\n-      if (\n-        useControlStore.getState().checkIsOpen(SwitchName.ENABLE_USER_REPORT)\n-      ) {\n-        showFeedBack?.();\n-      }\n-    }\n-  });\n-\n-  const onPressFollowAndFans = useMemoizedFn((defaultTab: string) => {\n-    reportClick('button', {\n-      user_button: defaultTab === 'follow' ? 4 : 5,\n-      identity_status: isMine ? '0' : '1'\n-    });\n-    router.push({\n-      pathname: '/follow-fan/',\n-      params: {\n-        defaultTab,\n-        uid: currentUser?.uid || ''\n-      }\n-    });\n-  });\n-\n-  const showLikeModal = useMemoizedFn(() => {\n-    reportClick('button', {\n-      user_button: 6,\n-      identity_status: isMine ? '0' : '1'\n-    });\n-    showImageConfirm({\n-      image: LIKE_COVER,\n-      title: currentUser?.name || '',\n-      content: `共获得${stat?.beingLikeds}个赞`,\n-      confirmText: '知道了',\n-      cancelText: '#hiddenCancel#', // 暗色不影响其他 confirm\n-      onConfirm: ({ close }) => {\n-        close();\n-      },\n-      theme: Theme.DARK\n-    });\n-  });\n-\n-  const showSameModal = useMemoizedFn(() => {\n-    reportClick('button', {\n-      user_button: 7,\n-      identity_status: isMine ? '0' : '1'\n-    });\n-\n-    router.push({\n-      pathname: `/fame/${currentUser?.uid}`,\n-      params: {\n-        identity_status: isMine ? 0 : 1\n-      }\n-    });\n-  });\n-\n-  console.log('[infoSection] currentUser', currentUser);\n-\n-  const HasBeLiked = useMemo(() => goodsLikes, [goodsLikes]);\n-\n-  const safeTop = useSafeAreaInsets().top;\n-\n-  return (\n-    <View style={{ alignItems: 'center' }}>\n-      <View\n-        style={{\n-          position: 'absolute',\n-          width: '100%',\n-          height: '100%',\n-          pointerEvents: 'none',\n-          zIndex: -1,\n-          transform: [\n-            {\n-              translateY: 58\n-            }\n-          ]\n-        }}\n-      >\n-        <Image\n-          source={INFO_MASK}\n-          contentFit=\"cover\"\n-          style={{ flex: 1, zIndex: -1 }}\n-        />\n-      </View>\n-\n-      {HasBeLiked && !isAllowed ? (\n-        <Animated.View\n-          style={[\n-            animateStyle,\n-            {\n-              position: 'absolute',\n-              height: 26,\n-              top: 0,\n-              transform: [\n-                {\n-                  translateY: safeTop < 44 ? 0 : -12\n-                },\n-                {\n-                  translateX: -6\n-                }\n-              ],\n-              zIndex: 100\n-            }\n-          ]}\n-        >\n-          <ShadowedView>\n-            <View style={$flexHCenter}>\n-              <Icon\n-                icon=\"self_goods_like\"\n-                size={14}\n-                style={{\n-                  marginRight: 4\n-                }}\n-              />\n-              <Text\n-                style={{\n-                  ...$USE_FONT(\n-                    darkTheme.text.primary,\n-                    typography.fonts.pingfangSC.normal,\n-                    12,\n-                    'normal',\n-                    isIos ? '600' : 'bold',\n-                    undefined\n-                  ),\n-                  marginRight: 4\n-                }}\n-              >\n-                {goodsLikes}人\n-              </Text>\n-              <Text\n-                style={{\n-                  ...$USE_FONT(\n-                    darkTheme.text.primary,\n-                    typography.fonts.pingfangSC.normal,\n-                    12,\n-                    'normal',\n-                    isIos ? '600' : 'bold',\n-                    undefined\n-                  )\n-                }}\n-              >\n-                {isMine ? '赞了你的痛墙' : '赞了TA的痛墙'}\n-              </Text>\n-            </View>\n-          </ShadowedView>\n-        </Animated.View>\n-      ) : null}\n-\n-      <Animated.View style={[$userInfo]}>\n-        {isAllowed && (isMine || (!isMine && hasGoods)) ? (\n-          <Animated.View\n-            style={[\n-              animateStyle,\n-              {\n-                position: 'absolute',\n-                height: 26,\n-                transform: [\n-                  {\n-                    translateY: -126\n-                  },\n-                  {\n-                    translateX: -6\n-                  }\n-                ],\n-                zIndex: 200\n-              }\n-            ]}\n-          >\n-            <Pressable\n-              onPress={() => {\n-                checkEnterGoods();\n-              }}\n-            >\n-              <ShadowedView>\n-                <View style={$flexHCenter}>\n-                  {isMine ? (\n-                    <AnimatedLottieView\n-                      source={INTRO_PULL}\n-                      loop\n-                      autoPlay\n-                      style={{\n-                        width: 111,\n-                        height: 21,\n-                        transform: [\n-                          {\n-                            translateX: 4\n-                          },\n-                          {\n-                            translateY: -1\n-                          },\n-                          {\n-                            scale: 1\n-                          }\n-                        ]\n-                      }}\n-                    />\n-                  ) : (\n-                    <AnimatedLottieView\n-                      source={INTRO_CUSTOM_PULL}\n-                      loop\n-                      autoPlay\n-                      style={{\n-                        width: 139,\n-                        height: 21,\n-                        transform: [\n-                          {\n-                            translateX: 6\n-                          },\n-                          {\n-                            translateY: -1\n-                          },\n-                          {\n-                            scale: 1\n-                          }\n-                        ]\n-                      }}\n-                    />\n-                  )}\n-                </View>\n-              </ShadowedView>\n-            </Pressable>\n-          </Animated.View>\n-        ) : null}\n-\n-        <Animated.View style={[$avatar]}>\n-          <Avatar\n-            profile={currentUser}\n-            size={100}\n-            showTag={true}\n-            showPendant={true}\n-            onPress={handleEditAvatar}\n-            borderWidth={2}\n-            borderColor={'#262629'}\n-            onLoad={() => {\n-              if (currentUser?.uid) {\n-                userPerformanceCollector.markPerformanceTimestamp(\n-                  'user_info_available_timestamp',\n-                  currentUser?.uid\n-                );\n-              }\n-            }}\n-          />\n-        </Animated.View>\n-        {newPendant ? (\n-          <Animated.View style={[$pendantTipContainer]}>\n-            <Image style={[$pendantTip]} source={PENDANT_TIP_BG} />\n-            <View\n-              style={[\n-                $pendantTip,\n-                rowStyle,\n-                { justifyContent: 'center', gap: 2, top: -52, paddingLeft: 6 }\n-              ]}\n-            >\n-              <Image\n-                source={newPendant?.pendantUrl || ''}\n-                style={$pendantImage}\n-              />\n-              <Text style={[$newPendantStyle]}>{'新挂件'}</Text>\n-            </View>\n-            <View style={[$dotTip]} />\n-          </Animated.View>\n-        ) : null}\n-        <Animated.View style={[$nicknameConainer]}>\n-          <Animated.Text\n-            style={[\n-              $nickname as AnimatedStyleProp<TextStyle>,\n-              {\n-                width: 300,\n-                color:\n-                  isMine && currentUser?.isNameUpdated === false\n-                    ? darkTheme.text.disabled\n-                    : darkTheme.text.primary,\n-                textAlign: 'center'\n-              }\n-            ]}\n-            ellipsizeMode=\"tail\"\n-            numberOfLines={1}\n-            allowFontScaling={false}\n-          >\n-            {currentUserName}\n-          </Animated.Text>\n-        </Animated.View>\n-      </Animated.View>\n-\n-      <Animated.View style={[$numsWrap]} pointerEvents=\"box-none\">\n-        <Pressable onPress={() => onPressFollowAndFans('follow')}>\n-          <View style={$numItem}>\n-            <Text style={$num}>\n-              {formatUserNumber(stat?.followings?.toLocaleString() || 0)}\n-            </Text>\n-            <Text style={$label}>关注</Text>\n-          </View>\n-        </Pressable>\n-        <Pressable onPress={() => onPressFollowAndFans('fans')}>\n-          <View style={$numItem}>\n-            <Text style={$num}>\n-              {formatUserNumber(stat?.fans?.toLocaleString() || 0)}\n-            </Text>\n-            <Text style={$label}>粉丝</Text>\n-          </View>\n-        </Pressable>\n-        <Pressable onPress={showLikeModal} style={$numItem}>\n-          <Text style={$num}>\n-            {formatUserNumber(stat?.beingLikeds?.toLocaleString() || 0)}\n-          </Text>\n-          <Text style={$label}>获赞</Text>\n-        </Pressable>\n-        <Pressable onPress={showSameModal} style={$numItem}>\n-          <Text style={$num}>\n-            {formatUserNumber(stat?.beingCopieds?.toLocaleString() || 0)}\n-          </Text>\n-          <Text style={$label}>声望</Text>\n-        </Pressable>\n-      </Animated.View>\n-    </View>\n-  );\n-}\n-\n-const $userInfo: ViewStyle = {\n-  display: 'flex',\n-  flexDirection: 'column',\n-  justifyContent: 'center',\n-  alignItems: 'center',\n-  marginTop: 58,\n-  zIndex: $Z_INDEXES.z200\n-};\n-\n-const $avatar: ViewStyle = {\n-  width: 100,\n-  height: 100,\n-  position: 'relative'\n-};\n-\n-const $pendantTipContainer: ViewStyle = {\n-  position: 'absolute',\n-  width: 0,\n-  height: 0\n-};\n-\n-const $pendantTip: ImageStyle = {\n-  width: 91,\n-  height: 45,\n-  left: 22,\n-  top: -48,\n-  position: 'absolute'\n-};\n-\n-const $dotTip: ViewStyle = {\n-  width: 8,\n-  height: 8,\n-  backgroundColor: '#fff',\n-  borderRadius: 4,\n-  left: 26,\n-  top: -18,\n-  position: 'absolute'\n-};\n-\n-const $pendantImage: ImageStyle = {\n-  width: 20,\n-  height: 20\n-};\n-\n-const $newPendantStyle: TextStyle = {\n-  fontSize: 11,\n-  fontWeight: '600'\n-};\n-\n-const $avatarTag: ImageStyle = {\n-  width: 24,\n-  height: 24,\n-  right: 6,\n-  position: 'absolute',\n-  bottom: 0\n-};\n-\n-const $avatarInner: ViewStyle = {\n-  width: 104,\n-  height: 104,\n-  top: -2,\n-  left: -2,\n-  position: 'absolute',\n-  borderColor: 'rgba(255, 255, 255, 0.6)',\n-  borderWidth: 2,\n-  borderRadius: 52,\n-  zIndex: $Z_INDEXES.z0\n-};\n-\n-const $nicknameConainer: ViewStyle = {\n-  marginTop: 12,\n-  flexDirection: 'row',\n-  alignItems: 'center',\n-  justifyContent: 'center',\n-  maxWidth: '60%',\n-  transformOrigin: 'top'\n-  // backgroundColor: 'pink'\n-};\n-\n-const $nickname: TextStyle = {\n-  // overflow: 'hidden',\n-  // flexShrink: 0,\n-  color: '#000',\n-  position: 'relative',\n-  fontFamily: typography.fonts.baba.heavy,\n-  fontSize: 20,\n-  lineHeight: 28\n-};\n-\n-const $nicknameEdit: ViewStyle = {\n-  flex: 0,\n-  marginLeft: 4\n-};\n-\n-const $numsWrap: ViewStyle = {\n-  ...StyleSheet.rowStyle,\n-  width: '100%',\n-  paddingHorizontal: 16,\n-  marginTop: 16,\n-  marginBottom: 17,\n-  justifyContent: 'space-around'\n-};\n-\n-const $numItem: ViewStyle = {\n-  display: 'flex',\n-  alignItems: 'center',\n-  flexDirection: 'column',\n-  minWidth: 32,\n-  minHeight: 44\n-  //   flexBasis: 100\n-};\n-\n-const $num: TextStyle = {\n-  fontSize: 17,\n-  lineHeight: 24,\n-  textAlign: 'center',\n-  fontFamily: typography.fonts.baba.heavy,\n-  color: darkTheme.text.primary\n-};\n-\n-const $label: TextStyle = {\n-  color: darkTheme.text.tertiary,\n-  fontSize: 12,\n-  lineHeight: 17,\n-  fontWeight: '500'\n-};\n-\n-const $editIconPress: ViewStyle = {\n-  width: 20,\n-  height: 20,\n-  position: 'absolute',\n-  ...centerStyle\n-};\n"}, {"new_path": "src/bizComponents/userScreen/retentionPopup/index.tsx", "old_path": "src/bizComponents/userScreen/retentionPopup/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -1,6 +1,6 @@\n import React, { useCallback } from 'react';\n import { useEffect } from 'react';\n-import { Pressable, Text, View } from 'react-native';\n+import { Pressable, View } from 'react-native';\n import { queryClient } from '@/src/api/query';\n import { Follow } from '@/src/components/follow';\n import { Image } from '@/src/components/image';\n@@ -11,6 +11,7 @@ import { darkTheme, typography } from '@/src/theme';\n import { StyleSheet, dp2px } from '@/src/utils';\n import { followGuide } from '@/src/utils/FollowGuideManager';\n import { reportClick } from '@/src/utils/report';\n+import { Text } from '@Components/text';\n import { FirstTriggerType } from '@/proto-registry/src/web/raccoon/common/types_pb';\n import { useShallow } from 'zustand/react/shallow';\n import { AvatarBackgroundHalo, AvatarForegroundHalo } from './AvatarHalo';\n"}, {"new_path": "src/bizComponents/userScreen/constants.ts", "old_path": "src/bizComponents/userScreen/constants.ts", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -0,0 +1,47 @@\n+import { TabItem } from './components/UserPanel';\n+\n+export enum UserPageTab {\n+  WORKS = 'works',\n+  SECRET = 'live_photo',\n+  LIKE = 'like',\n+  MY_ROLE = 'my_role'\n+}\n+\n+export const authorTabs: TabItem[] = [\n+  {\n+    key: UserPageTab.WORKS,\n+    title: '作品',\n+    width: 48\n+  },\n+  {\n+    key: UserPageTab.SECRET,\n+    title: '待发布',\n+    width: 63\n+  },\n+  {\n+    key: UserPageTab.LIKE,\n+    title: '赞过',\n+    width: 48\n+  },\n+  {\n+    key: UserPageTab.MY_ROLE,\n+    title: '我的角色',\n+    width: 78\n+  }\n+];\n+\n+export const visitorTabs = authorTabs.filter(\n+  tab => tab.key !== UserPageTab.SECRET && tab.key !== UserPageTab.MY_ROLE\n+);\n+\n+export const authorPageTabMap = {\n+  [UserPageTab.WORKS]: 0,\n+  [UserPageTab.SECRET]: 1,\n+  [UserPageTab.LIKE]: 2,\n+  [UserPageTab.MY_ROLE]: 3\n+};\n+\n+export const visitorPageTabMap = {\n+  [UserPageTab.WORKS]: 0,\n+  [UserPageTab.LIKE]: 1\n+};\n"}, {"new_path": "src/bizComponents/userScreen/index.tsx", "old_path": "src/bizComponents/userScreen/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "--- \n+++ \n@@ -9,32 +9,27 @@\n   useRef,\n   useState\n } from 'react';\n-import { InteractionManager, Pressable, View } from 'react-native';\n-import { GestureDetector } from 'react-native-gesture-handler';\n+import { InteractionManager, StyleProp, TextStyle, View } from 'react-native';\n+import { Gesture, GestureDetector } from 'react-native-gesture-handler';\n import PagerView from 'react-native-pager-view';\n import Animated, {\n-  cancelAnimation,\n   runOnJS,\n   useAnimatedStyle,\n   useSharedValue,\n   withDelay,\n   withRepeat,\n-  withSpring,\n-  withTiming\n+  withSpring\n } from 'react-native-reanimated';\n import { useSafeAreaInsets } from 'react-native-safe-area-context';\n import { getGoodsList, visitHomePage } from '@/src/api/goods';\n-import ProfileGuideModal from '@/src/bizComponents/profile/GuideModal';\n-import { FullScreen, Icon, Text } from '@/src/components';\n-import { EmptyPlaceHolder } from '@/src/components/Empty';\n+import { usePullDownGestureHandlersFactory } from '@/src/bizComponents/nestedScrollView/hooks/usePullDownGestureHandlers';\n+import { GoodsWallBg } from '@/src/bizComponents/userScreen/components/GoodsWallBg';\n+import { Screen } from '@/src/components';\n import { AlbumSheet } from '@/src/components/album';\n import { AlbumFromType } from '@/src/components/album/const';\n import { PagePerformance } from '@/src/components/common/pagePerformance';\n-import { showLogin } from '@/src/components/login';\n-import { Tabs } from '@/src/components/tabs';\n import { LOGIN_SCENE } from '@/src/constants';\n import { useAuthState, usePersistFn, useScreenSize } from '@/src/hooks';\n-import { useRetentionPopup } from '@/src/hooks/useRetentionPopup';\n import { useSafeBottomArea } from '@/src/hooks/useSafeAreaInsetsStyle';\n import { useAuthStore } from '@/src/store/authInfo';\n import { useGoodsShefStore } from '@/src/store/goods_shef';\n@@ -42,14 +37,14 @@\n import { usePublishStore } from '@/src/store/publish';\n import { useStorageStore } from '@/src/store/storage';\n import { useUserInfoStore } from '@/src/store/userInfo';\n-import { darkTheme } from '@/src/theme';\n+import { darkTheme, typography } from '@/src/theme';\n import {\n   FirstTriggerType,\n   Pagination,\n   TabItemType,\n   UserSocialStat\n } from '@/src/types';\n-import { dp2px } from '@/src/utils';\n+import { dp2px, isIos } from '@/src/utils';\n import { CommonEventBus } from '@/src/utils/event';\n import { safeParseJson } from '@/src/utils/opt/safeParseJson';\n import { reportClick, reportExpo } from '@/src/utils/report';\n@@ -57,70 +52,33 @@\n import { Image } from '@Components/image';\n import { MaskArea } from '@Components/maskArea';\n import { StyleSheet } from '@Utils/StyleSheet';\n-import CUSTOMER_EMPTY from '@Assets/image/goods-shef/customer-empty.png';\n-import GOODS_MASK from '@Assets/image/goods-shef/goods-mask.png';\n-import DEFAULT_MASK_BG from '@Assets/image/goods-shef/goods_bg.png';\n-import MAIN_EMPTY from '@Assets/image/goods-shef/main-bg.png';\n import { GoodsHomeState } from '../goodsHome/types';\n+import { NestedScrollView } from '../nestedScrollView';\n+import ProfileGuideModal from '../profile/GuideModal';\n+import { GoodsButton } from './components/GoodsButton';\n+import { ANIMATION_PARAMS, GoodsTopTip } from './components/GoodsTopTip';\n+import { UserHeader } from './components/UserHeader';\n+import { UserPanel } from './components/UserPanel';\n import { LikesFlowList } from './components/likeFlowList';\n+import { MyRoleFlowList } from './components/myRoleList';\n import { SecretsFlowList } from './components/secretFlowList';\n import { WorksFlowList } from './components/workFlowList';\n+import { useHandleSwipeBack } from './hooks/useHandleSwipeBack';\n+import { useRetentionPopup } from './hooks/useRetentionPopup';\n import { GetPlaceRsp } from '@/proto-registry/src/web/raccoon/goods/goods_pb';\n import { UserProfile } from '@/proto-registry/src/web/raccoon/uinfo/uinfo_pb';\n import { PartialMessage } from '@bufbuild/protobuf';\n import { useIsFocused } from '@react-navigation/native';\n import { useShallow } from 'zustand/react/shallow';\n+import {\n+  UserPageTab,\n+  authorPageTabMap,\n+  authorTabs,\n+  visitorPageTabMap,\n+  visitorTabs\n+} from './constants';\n import { FeedbackSheet } from './feedback';\n-import InfoProfile from './infoProfile';\n-import { InfoSection } from './infoSection';\n-import { PageTab } from './types';\n-import { useAnimation } from './useAnimation';\n-\n-export { PageTab } from './types';\n-\n-export enum PageParticleTab {\n-  LIVE_PHOTO = 'live_photo',\n-  MAGIC_VIDEO = 'magic_video'\n-}\n-\n-const authorTabs = [\n-  {\n-    key: PageTab.WORKS,\n-    title: '作品',\n-    label: '作品'\n-  },\n-  {\n-    key: PageTab.SECRET,\n-    title: '待发布',\n-    label: '待发布'\n-  },\n-  {\n-    key: PageTab.LIKE,\n-    title: '赞过',\n-    label: '赞过'\n-  },\n-  {\n-    key: PageTab.ALBUM,\n-    label: '图集',\n-    title: '图集'\n-  }\n-];\n-\n-const visitorTabs = authorTabs.filter(\n-  tab => tab.key !== PageTab.SECRET && tab.key !== PageTab.ALBUM\n-);\n-\n-const authorPageTabMap = {\n-  [PageTab.WORKS]: 0,\n-  [PageTab.SECRET]: 1,\n-  [PageTab.LIKE]: 2,\n-  [PageTab.ALBUM]: 3\n-};\n-\n-const visitorPageTabMap = {\n-  [PageTab.WORKS]: 0,\n-  [PageTab.LIKE]: 1\n-};\n+import { fetchSecretCount } from './utils';\n \n function User({\n   isRootPage = false,\n@@ -139,7 +97,7 @@\n   fromSwipePreview?: boolean;\n   preloadProfile?: PartialMessage<UserProfile>;\n }) {\n-  const [currentTab, setCurrentTab] = useState(PageTab.WORKS);\n+  const [currentTab, setCurrentTab] = useState<UserPageTab>(UserPageTab.WORKS);\n   const pagerRef = useRef<PagerView>(null);\n   const [pagerViewReady, setPagerViewReady] = useState<boolean>(false);\n   const { width: screenWidth, height: screenHeight } = useScreenSize('window');\n@@ -212,21 +170,77 @@\n     return profile;\n   }, [isMine, user, profile]);\n \n-  const shouldShowProfileGuide =\n-    userId &&\n-    isMine &&\n-    (!queryPageTab || tabEntry) &&\n-    !user?.gender &&\n-    !user?.birthday;\n-\n   const pageTabMap = useMemo(\n     () => (isMine ? authorPageTabMap : visitorPageTabMap),\n     [isMine]\n   );\n-  const pageTabConfig = useMemo(\n-    () => (isMine ? authorTabs : visitorTabs),\n-    [isMine]\n-  );\n+  const [secretsCount, setSecretsCount] = useState<number | null>(null);\n+\n+  const countStr = useMemo(() => {\n+    if (secretsCount) {\n+      return secretsCount > 99 ? '99+' : secretsCount.toString();\n+    }\n+    return '';\n+  }, [secretsCount]);\n+\n+  const countTextStyle: TextStyle = useMemo(() => {\n+    return {\n+      width: countStr.length * 8.5,\n+      lineHeight: 15,\n+      fontFamily: typography.fonts.Barlow.SemiBold,\n+      textOverflow: 'clip',\n+      fontWeight: '600'\n+    };\n+  }, [countStr.length]);\n+\n+  // 为待发布标签创建一个可以立即反映数量变化的渲染函数\n+  const renderSecretTabContent = useMemoizedFn(\n+    ({\n+      textStyle\n+    }: {\n+      isActive: boolean;\n+      textStyle: StyleProp<TextStyle>[];\n+    }) => {\n+      return (\n+        <>\n+          <Animated.Text\n+            style={[...textStyle, { width: 46 }]}\n+            numberOfLines={1}\n+          >\n+            待发布\n+          </Animated.Text>\n+          <Animated.Text\n+            style={[...textStyle, countTextStyle]}\n+            numberOfLines={1}\n+          >\n+            {countStr}\n+          </Animated.Text>\n+        </>\n+      );\n+    }\n+  );\n+  const pageTabConfig = useMemo(() => {\n+    // 修改作者标签配置，为待发布标签添加数量\n+    if (isMine) {\n+      const updatedConfig = [...authorTabs];\n+      // 找到待发布标签并添加数量\n+      const secretsTabIndex = updatedConfig.findIndex(\n+        tab => tab.key === UserPageTab.SECRET\n+      );\n+      if (!secretsCount || secretsTabIndex === -1) {\n+        return updatedConfig;\n+      }\n+\n+      updatedConfig[secretsTabIndex] = {\n+        ...updatedConfig[secretsTabIndex],\n+        renderItem: renderSecretTabContent,\n+        width: 63 + 8.5 * countStr.length\n+      };\n+\n+      return updatedConfig;\n+    }\n+    return visitorTabs;\n+  }, [isMine, secretsCount, renderSecretTabContent, countStr.length]);\n \n   const [profileGuideModalVisible, setProfileGuideModalVisible] = useState<\n     boolean | null\n@@ -248,6 +262,13 @@\n       CommonEventBus.off('tabBarPressedWhenFocus', handler);\n     };\n   }, []);\n+\n+  const shouldShowProfileGuide =\n+    userId &&\n+    isMine &&\n+    (!queryPageTab || tabEntry) &&\n+    !user?.gender &&\n+    !user?.birthday;\n \n   /** 资料引导 */\n   useEffect(() => {\n@@ -270,59 +291,40 @@\n     });\n   }, [shouldShowProfileGuide, userId, isFocused]);\n \n-  const topHeight = useMemo(() => {\n-    return dp2px(274) + safeTop * 2;\n-  }, [safeTop]);\n-  const translateYMax = topHeight - safeTop - (safeTop < 44 ? 0 : 44);\n-  const [creditOpacity, setCreditOpacity] = useState(1);\n-\n-  /** 当用户上滑到边界区域，并立刻下拉时 */\n-  const [returnTop, setReturnTop] = useState(true);\n-  const updateUnlockTop = (status: boolean) => {\n-    setReturnTop(status);\n-    if (!status) {\n-      $tabStickyOpacity.value = withTiming(1);\n-      $goodsShareY.value = withTiming(-translateYMax);\n-    }\n-  };\n-\n-  /** 统一设置回到头部 重置 */\n-  const resetTopBehavior = () => {\n-    $goodsShareY.value = withTiming(0);\n-    $tabStickyOpacity.value = withTiming(0);\n-  };\n-\n-  useEffect(() => {\n-    if (returnTop) {\n-      resetTopBehavior();\n-    }\n-  }, [returnTop]);\n-\n   /** profile 的一些引导 */\n   const [isFeedbackShow, setIsFeedbackShow] = useState(false);\n   const [isSeqToastShow, setIsSeqToastShow] = useState(false);\n \n   /** 谷子部分 */\n-  const [goodsLikes, setGoodsLikes] = useState(0);\n-  const DEFAULT_EMPTY = isMine ? MAIN_EMPTY : CUSTOMER_EMPTY;\n-  const [goodsBgWall, setGoodsBgWall] = useState('');\n   const [hasGoods, setHasGoods] = useState(false);\n-\n-  useEffect(() => {\n-    setGoodsBgWall(goodsWallRes?.backgroundImage?.url || DEFAULT_EMPTY);\n-    setGoodsLikes(goodsWallRes?.likes || 0);\n-  }, [goodsWallRes]);\n-\n-  const checkEnterGoods = useCallback(() => {\n+  const [isMaskLoaded, setIsMaskLoaded] = useState(false);\n+\n+  // 监听GOODS_MASK加载完成事件\n+  useEffect(() => {\n+    const handleMaskLoaded = (data: { loaded: boolean }) => {\n+      // 使用InteractionManager确保其他UI渲染完成后再显示背景\n+      if (data.loaded) {\n+        InteractionManager.runAfterInteractions(() => {\n+          setIsMaskLoaded(true);\n+        });\n+      }\n+    };\n+\n+    CommonEventBus.on('goodsMaskLoaded', handleMaskLoaded);\n+\n+    return () => {\n+      CommonEventBus.off('goodsMaskLoaded', handleMaskLoaded);\n+    };\n+  }, []);\n+\n+  const checkEnterGoods = useMemoizedFn(() => {\n     loginIntercept(\n       () => {\n         enterGoods();\n       },\n       { scene: LOGIN_SCENE.GOODS }\n     );\n-  }, []);\n-\n-  const maxScrollLimit = 215 - safeTop;\n+  });\n \n   // tab 触发刷新\n   const [isRefreshData, setIsRefreshData] = useState<[] | undefined>(undefined);\n@@ -335,28 +337,6 @@\n       leading: true\n     }\n   );\n-\n-  const {\n-    holdGesture,\n-    $goodsShareY,\n-    nativeGesture,\n-    refreshStyle,\n-    enterGoodsStyle,\n-    refreshWrapStyle,\n-    worksScrollY,\n-    livePhotoSY,\n-    likeScrollY\n-  } = useAnimation({\n-    currentTab,\n-    maxScrollLimit,\n-    checkEnterGoods,\n-    onRefreshData,\n-    setReturnTop\n-  });\n-\n-  const $goodsTransBgStyle = useAnimatedStyle(() => ({\n-    transform: [{ translateY: $goodsShareY.value as number }]\n-  }));\n \n   const GOODS_SHEF_TRIGGER = isMine\n     ? FirstTriggerType.ENTER_OWN_FANDOM_WALL\n@@ -382,7 +362,7 @@\n         });\n       }\n     } else {\n-      if (hasGoods && profile?.uid) {\n+      if (profile?.uid) {\n         router.push({\n           pathname:\n             `/goods/home?state=${GoodsHomeState.Guest}&uid=${profile?.uid}` as RelativePathString\n@@ -397,6 +377,7 @@\n     setIsSeqToastShow(res);\n   }, []);\n \n+  // FIXME(fuxiao): 需要再次降低优先级\n   const syncGoodsList = useCallback(async (id: string) => {\n     const res = await getGoodsList({\n       pagination: {\n@@ -425,7 +406,9 @@\n         }\n \n         // 引导提示（低优先级）\n-        ((hasGoods && !isMine) || isMine) && syncGoodsShefToast();\n+        if ((hasGoods && !isMine) || isMine) {\n+          syncGoodsShefToast();\n+        }\n       });\n     }\n   }, [\n@@ -439,8 +422,23 @@\n     hasGoods\n   ]);\n \n-  useEffect(() => {\n-    isFocused && reportExpo('invite_icon', {}, true);\n+  // 添加获取待发布数量的逻辑，确保在其他接口完成后再执行\n+  useEffect(() => {\n+    if (id && isMine && isFocused && goodsWallRes && secretsCount === null) {\n+      // 先执行高优先级和次高优先级的请求\n+      InteractionManager.runAfterInteractions(async () => {\n+        // 所有优先级请求完成后，再获取待发布数量\n+        const count = await fetchSecretCount();\n+        setSecretsCount(count);\n+        console.log('[LogPrefix][User][secretsCount] 待发布数量:', count);\n+      });\n+    }\n+  }, [id, isMine, isFocused, goodsWallRes, secretsCount]);\n+\n+  useEffect(() => {\n+    if (isFocused) {\n+      reportExpo('invite_icon', {}, true);\n+    }\n   }, [isFocused]);\n \n   /** 新手引导的 痛墙 bounce */\n@@ -456,64 +454,17 @@\n     isLoadBouncing.current = false;\n   };\n \n-  useEffect(() => {\n-    if (\n-      isFocused &&\n-      useStorageStore.getState().userGoodsHasbeBounced < 5 &&\n-      (isMine || (!isMine && hasGoods)) &&\n-      !lockBounce &&\n-      profileGuideModalVisible !== true &&\n-      isStickyAbs\n-    ) {\n-      const nextCount = useStorageStore.getState().userGoodsHasbeBounced + 1;\n-      isLoadBouncing.current = true;\n-      $goodsShareY.value = withDelay(\n-        300,\n-        withRepeat(\n-          withSpring((maxScrollLimit / 6) * 5 - 10, {\n-            damping: 80\n-          }),\n-          4,\n-          true,\n-          () => {\n-            runOnJS(__setStorage)({\n-              userGoodsHasbeBounced: nextCount\n-            });\n-            runOnJS(resetLoadBouncing)();\n-            $goodsShareY.value = 0;\n-          }\n-        )\n-      );\n-    }\n-  }, [isFocused, isMine, hasGoods, lockBounce, profileGuideModalVisible]);\n-\n   /**是否可回退 */\n-  const [touchOffsetX, setTouchOffsetX] = useState(screenWidth);\n-  const [isBack, setIsBack] = useState(false);\n-  useEffect(() => {\n-    if (isBack) {\n-      router.back();\n-    }\n-  }, [isBack]);\n \n   const pageScroll = useCallback(\n     (e: { nativeEvent: { position: number; offset: number } }) => {\n       const pos = e.nativeEvent.position + e.nativeEvent.offset;\n \n-      // 只在非 Preview 模式下启用返回手势\n-      if (\n-        !fromSwipePreview &&\n-        e.nativeEvent.position < 0 &&\n-        touchOffsetX < 100\n-      ) {\n-        setIsBack(true);\n-      }\n-\n-      if (pos >= 0 && pos < 2) {\n-        $animatedIndictor.value = pos;\n+      if (pos >= 0 && pos < (isMine ? 4 : 2)) {\n+        $animatedTabIndictor.value = pos;\n       }\n     },\n-    [touchOffsetX, fromSwipePreview]\n+    [isMine]\n   );\n \n   useEffect(() => {\n@@ -528,31 +479,14 @@\n   useEffect(() => {\n     if (pagerViewReady && queryPageTab && queryPageTab !== currentTab) {\n       if (pagerRef.current) {\n-        const tab = pageTabMap[queryPageTab as PageTab];\n+        const tab = pageTabMap[queryPageTab as UserPageTab];\n         pagerRef.current?.setPage(tab || 0);\n       }\n     }\n   }, [queryPageTab, queryTimestamp, pagerViewReady]);\n \n-  /** 部分场景重置所有动画 */\n-  const cancelAllAnimate = () => {\n-    cancelAnimation($goodsShareY);\n-  };\n-\n-  const $animatedIndictor = useSharedValue(0);\n-  const [isStickyAbs, setIsStickyAbs] = useState(true);\n-\n-  const [tabPosY, setTabPosY] = useState(0);\n-  const $goodsMaskShareOpacity = useSharedValue(1);\n-  const $goodsMaskStyle = useAnimatedStyle(() => ({\n-    opacity: $goodsMaskShareOpacity.value\n-  }));\n-\n-  const $tabStickyOpacity = useSharedValue(0);\n-  const $tabStickyAnimateStyle = useAnimatedStyle(() => ({\n-    opacity: $tabStickyOpacity.value,\n-    display: $tabStickyOpacity.value === 0 ? 'none' : 'flex'\n-  }));\n+  const $animatedTabIndictor = useSharedValue(0);\n+  const $animatedScrollPosition = useSharedValue(0);\n \n   const { checkRetentionAndReport } = useRetentionPopup({\n     isMine,\n@@ -573,36 +507,14 @@\n       );\n       return () => {\n         if (isLoadBouncing.current) {\n-          cancelAllAnimate();\n+          // cancelAllAnimate();\n         }\n       };\n     }, [id])\n   );\n \n   // 处理返回事件，用于 SwipeScreen 的 beforeBackPressed\n-  useEffect(() => {\n-    const handleUserScreenBack = (data: {\n-      resolve: () => void;\n-      ownerUid: string;\n-    }) => {\n-      const { resolve } = data;\n-      const shouldShowPopup = checkRetentionAndReport(resolve);\n-      if (!shouldShowPopup) {\n-        // 如果不需要显示弹窗，正常返回\n-        resolve();\n-      }\n-      // 否则阻止返回，不调用 resolve\n-    };\n-\n-    // 注册事件处理函数\n-    const handler = (event: { resolve: () => void; ownerUid: string }) =>\n-      handleUserScreenBack(event);\n-    CommonEventBus.on('handleUserScreenBack', handler);\n-\n-    return () => {\n-      CommonEventBus.off('handleUserScreenBack', handler);\n-    };\n-  }, [checkRetentionAndReport]);\n+  useHandleSwipeBack(checkRetentionAndReport);\n \n   // 处理返回按钮点击\n   const handleBack = useCallback(() => {\n@@ -613,16 +525,28 @@\n     // 否则阻止返回，让弹窗处理\n   }, [checkRetentionAndReport]);\n \n+  // 处理图集按钮点击\n+  const handleAlbumPress = useCallback(() => {\n+    reportClick('album_button', {\n+      identity_status: isMine ? '0' : '1'\n+    });\n+\n+    usePublishStore.getState().getAlbumPhotos(true);\n+    usePublishStore.getState().getHistoryPhotos(true);\n+    setShowAlbum(true);\n+    usePerformanceStore.getState().recordStart('make_photo_photo_set_render', {\n+      performance_type: 'render',\n+      performance_key: AlbumFromType.USER\n+    });\n+  }, [isMine]);\n+\n+  const PagerViewGesture = Gesture.Native().shouldCancelWhenOutside(false);\n+\n   const renderPageView = useMemoizedFn(() => {\n-    if (!currentUser?.uid) {\n-      return <View />;\n-    }\n-\n+    // NOTE(fuxiao): 如果这里判断 id 返回 View 或者 null，会导致 Android 无法滑动\n     const commonParams = {\n       id: id || '',\n-      updateUnlockTop: updateUnlockTop,\n       $safePaddingBottom: $safePaddingBottom,\n-      nativeGesture: nativeGesture,\n       queryRefresh: queryRefresh,\n       queryPageTab: queryPageTab,\n       queryTimestamp: queryTimestamp,\n@@ -634,14 +558,14 @@\n     // 这里有点坑，在pageview 里面直接控制会报错，所以在这里处理\n     const children = isMine\n       ? [\n-          <WorksFlowList key={0} scrollY={worksScrollY} {...commonParams} />,\n-          <SecretsFlowList key={1} scrollY={livePhotoSY} {...commonParams} />,\n-          <LikesFlowList key={2} scrollY={likeScrollY} {...commonParams} />,\n-          <View key={3} />\n+          <WorksFlowList key={0} {...commonParams} />,\n+          <SecretsFlowList key={1} {...commonParams} />,\n+          <LikesFlowList key={2} {...commonParams} />,\n+          <MyRoleFlowList key={3} {...commonParams} />\n         ]\n       : [\n-          <WorksFlowList key={0} scrollY={worksScrollY} {...commonParams} />,\n-          <LikesFlowList key={2} scrollY={likeScrollY} {...commonParams} />\n+          <WorksFlowList key={0} {...commonParams} />,\n+          <LikesFlowList key={2} {...commonParams} />\n         ];\n \n     return (\n@@ -652,26 +576,19 @@\n           backgroundColor: darkTheme.background.page\n         }}\n         onPageSelected={e => {\n+          const position = e.nativeEvent.position;\n+          // 修复查找对应标签的逻辑\n           const tab = Object.entries(pageTabMap).find(\n-            (key, value) => value === e.nativeEvent.position\n+            ([_, value]) => value === position\n           );\n-          if (tab?.[0] === PageTab.ALBUM) {\n-            setShowAlbum(true);\n-            usePerformanceStore\n-              .getState()\n-              .recordStart('make_photo_photo_set_render', {\n-                performance_type: 'render',\n-                performance_key: AlbumFromType.USER\n-              });\n-          }\n           if (pagerViewReady && tab?.[0]) {\n-            setCurrentTab((tab?.[0] as PageTab) || PageTab.WORKS);\n+            setCurrentTab((tab?.[0] as UserPageTab) || UserPageTab.WORKS);\n             setIsRefreshData(undefined);\n           }\n         }}\n         // overdrag\n         onTouchStart={e => {\n-          setTouchOffsetX(e.nativeEvent.pageX);\n+          // setTouchOffsetX(e.nativeEvent.pageX);\n         }}\n         onPageScroll={pageScroll}\n         onLayout={() => {\n@@ -711,7 +628,7 @@\n \n   // 在作品列表开始加载时标记\n   useEffect(() => {\n-    if (currentTab === PageTab.WORKS && id) {\n+    if (currentTab === UserPageTab.WORKS && id) {\n       userPerformanceCollector.markPerformanceTimestamp(\n         'user_works_init_timestamp',\n         id\n@@ -719,400 +636,215 @@\n     }\n   }, [currentTab, id]);\n \n-  if (!id) {\n-    return (\n-      <EmptyPlaceHolder\n-        buttonText=\"立即登录\"\n-        button={true}\n-        type=\"needlogin\"\n-        onButtonPress={showLogin}\n-        style={{\n-          height: '100%',\n-          paddingBottom: $safePaddingBottom\n-        }}\n-      >\n-        登录账号，查看你关注的精彩内容\n-      </EmptyPlaceHolder>\n-    );\n-  }\n+  const hasSlideUp = useSharedValue(false);\n+\n+  const { getGestureHandlers, pullDownProgress } =\n+    usePullDownGestureHandlersFactory({\n+      onPullDownEnd: useMemoizedFn((progress: number) => {\n+        console.log('[LogPrefix][GoodsTopTip] 下拉结束，进度:', progress);\n+        if (progress >= ANIMATION_PARAMS.PULL_GOODS_THRESHOLD) {\n+          // 打开痛墙\n+          checkEnterGoods();\n+        } else if (progress >= ANIMATION_PARAMS.PULL_REFRESH_THRESHOLD) {\n+          // 触发刷新\n+          onRefreshData();\n+        }\n+      }),\n+      scrollPosition: $animatedScrollPosition\n+    });\n+\n+  // 创建橡皮筋效果的动画样式\n+  const pullDownAnimatedStyle = useAnimatedStyle(() => {\n+    // 检查是否有下拉值时应用变换\n+    if (pullDownProgress.value <= 0 && !isLoadBouncing.current) {\n+      return { transform: [{ translateY: 0 }] };\n+    }\n+\n+    return {\n+      transform: [{ translateY: pullDownProgress.value }]\n+    };\n+  });\n+\n+  // 添加引导动画效果\n+  useEffect(() => {\n+    if (\n+      isMaskLoaded &&\n+      isFocused &&\n+      useStorageStore.getState().userGoodsHasbeBounced < 5 &&\n+      (isMine || (!isMine && hasGoods)) &&\n+      !isLoadBouncing.current &&\n+      lockBounce &&\n+      profileGuideModalVisible !== true\n+    ) {\n+      const nextCount = useStorageStore.getState().userGoodsHasbeBounced + 1;\n+      isLoadBouncing.current = true;\n+\n+      // 使用 pullDownProgress 进行动画\n+      pullDownProgress.value = withDelay(\n+        1000,\n+        withRepeat(\n+          withSpring(ANIMATION_PARAMS.PULL_GOODS_THRESHOLD + 20, {\n+            damping: 80\n+          }),\n+          4,\n+          true,\n+          () => {\n+            runOnJS(__setStorage)({\n+              userGoodsHasbeBounced: nextCount\n+            });\n+            runOnJS(resetLoadBouncing)();\n+            pullDownProgress.value = 0;\n+          }\n+        )\n+      );\n+    }\n+  }, [\n+    isFocused,\n+    isMine,\n+    hasGoods,\n+    profileGuideModalVisible,\n+    lockBounce,\n+    isMaskLoaded,\n+    pullDownProgress,\n+    __setStorage\n+  ]);\n \n   return (\n     <PagePerformance pathname=\"user\">\n-      <FullScreen\n+      <Screen\n+        theme=\"dark\"\n         key={id}\n-        style={{\n-          backgroundColor: isStickyAbs\n-            ? darkTheme.background.popup\n-            : darkTheme.background.page,\n-          position: 'relative'\n-        }}\n+        safeAreaEdges={[]}\n+        headerShown={false}\n+        backgroundView={\n+          <GoodsWallBg\n+            visible={isMaskLoaded}\n+            goodsWallRes={goodsWallRes}\n+            safeTop={safeTop}\n+          />\n+        }\n       >\n-        <Animated.View\n-          style={[\n-            {\n-              position: 'absolute',\n-              zIndex: -50,\n-              width: '100%',\n-              height: 481 + safeTop * 2,\n-              opacity: 0.75,\n-              transform: [\n-                {\n-                  translateY: -safeTop\n-                }\n-              ]\n-            },\n-            $goodsMaskStyle\n-          ]}\n-        >\n-          <Image\n-            source={GOODS_MASK}\n-            style={{\n-              flex: 1\n+        <UserHeader\n+          profile={profile}\n+          stat={stat}\n+          hasSlideUp={hasSlideUp}\n+          isMine={isMine}\n+          isRootPage={isRootPage}\n+          onBack={handleBack}\n+          safeTop={safeTop}\n+        />\n+        <GoodsTopTip\n+          isMine={isMine}\n+          hasGoods={hasGoods}\n+          goodsLikes={goodsWallRes?.likes}\n+          isGoodsTipAllowed={isSeqToastShow}\n+          checkEnterGoods={checkEnterGoods}\n+          safeTop={safeTop}\n+          scrollPosition={$animatedScrollPosition}\n+          pullDownProgress={pullDownProgress}\n+        />\n+        <Animated.View style={[{ flex: 1 }, pullDownAnimatedStyle]}>\n+          <NestedScrollView\n+            animatedPosition={$animatedScrollPosition}\n+            hasSlideUp={hasSlideUp}\n+            topPreserveInset={dp2px(88) + safeTop}\n+            topInset={0}\n+            simultaneousWithExternalGesture={\n+              isIos ? undefined : PagerViewGesture\n+            }\n+            gestureEventsHandlersHook={getGestureHandlers}\n+            failOffsetX={[-20, 20]}\n+            activeOffsetY={[-20, 20]}\n+            headerComponent={\n+              <>\n+                <UserPanel\n+                  currentUser={currentUser as UserProfile}\n+                  stat={stat}\n+                  isMine={isMine}\n+                  showFeedBack={() => setIsFeedbackShow(true)}\n+                  hasGoods={hasGoods}\n+                  currentTab={currentTab}\n+                  $animatedIndictor={$animatedTabIndictor}\n+                  pagerRef={pagerRef}\n+                  onAlbumPress={handleAlbumPress}\n+                  safeTop={safeTop}\n+                  hasSlideUp={hasSlideUp}\n+                  checkEnterGoods={checkEnterGoods}\n+                  tabConfig={pageTabConfig}\n+                />\n+                <GoodsButton\n+                  isMine={isMine}\n+                  checkEnterGoods={checkEnterGoods}\n+                  hasGoods={hasGoods}\n+                  safeTop={safeTop}\n+                />\n+              </>\n+            }\n+          >\n+            {isIos ? (\n+              renderPageView()\n+            ) : (\n+              <GestureDetector gesture={PagerViewGesture}>\n+                {renderPageView()}\n+              </GestureDetector>\n+            )}\n+          </NestedScrollView>\n+          <View\n+            style={[\n+              {\n+                width: '100%',\n+                height: screenHeight,\n+                position: 'relative',\n+                pointerEvents: 'box-none'\n+              }\n+            ]}\n+          >\n+            {currentUser?.uid && (\n+              <View\n+                style={{\n+                  position: 'absolute',\n+                  top: 0,\n+                  left: 0,\n+                  flex: 1\n+                }}\n+              >\n+                <FeedbackSheet\n+                  isVisible={isFeedbackShow}\n+                  userId={currentUser?.uid}\n+                  onClose={() => {\n+                    setIsFeedbackShow(false);\n+                  }}\n+                />\n+              </View>\n+            )}\n+\n+            {profileGuideModalVisible && (\n+              <ProfileGuideModal\n+                visible={profileGuideModalVisible}\n+                onClose={() => setProfileGuideModalVisible(false)}\n+              />\n+            )}\n+          </View>\n+\n+          <MaskArea />\n+\n+          {isIos && <MaskArea width={10} />}\n+          <AlbumSheet\n+            // FIXME(fuxiao): 图集 Modal 上方按钮需要增加一个间距\n+            style={\n+              {\n+                // paddingLeft: 20\n+                // paddingRight: 20\n+              }\n+            }\n+            callWhere={AlbumFromType.USER}\n+            isVisible={showAlbum}\n+            onClose={() => {\n+              setShowAlbum(false);\n             }}\n-            contentFit=\"cover\"\n           />\n         </Animated.View>\n-\n-        <Image\n-          source={DEFAULT_MASK_BG}\n-          contentFit=\"contain\"\n-          style={{\n-            position: 'absolute',\n-            zIndex: -1000,\n-            width: '100%',\n-            height: 481 + safeTop,\n-            transform: [\n-              {\n-                translateY: -safeTop\n-              }\n-            ]\n-          }}\n-        />\n-\n-        <Image\n-          source={goodsBgWall}\n-          contentFit=\"contain\"\n-          tosSize=\"size1\"\n-          style={{\n-            position: 'absolute',\n-            zIndex: -100,\n-            width: '100%',\n-            height: 481 + safeTop * 2,\n-            opacity: 1,\n-            transform: [\n-              {\n-                translateY: -safeTop\n-              }\n-            ]\n-          }}\n-        />\n-\n-        <Animated.View\n-          style={[\n-            {\n-              position: 'absolute',\n-              top: safeTop + 10,\n-              left: 0,\n-              right: 0,\n-              justifyContent: 'center',\n-              flexDirection: 'row',\n-              width: '100%'\n-            },\n-            refreshWrapStyle\n-          ]}\n-        >\n-          <Animated.View style={refreshStyle}>\n-            <Icon icon=\"loading\" style={{ width: 24, height: 24 }} />\n-          </Animated.View>\n-          <Animated.View\n-            style={[enterGoodsStyle, { borderRadius: 15, overflow: 'hidden' }]}\n-          >\n-            <Text\n-              style={{\n-                height: 30,\n-                paddingHorizontal: 8,\n-                backgroundColor: 'rgba(0, 0,0, 0.5)',\n-                color: '#ffffff',\n-                fontSize: 14,\n-                textAlign: 'center',\n-                lineHeight: 30\n-              }}\n-            >\n-              打开狸小窝\n-            </Text>\n-          </Animated.View>\n-        </Animated.View>\n-\n-        <Animated.View\n-          style={{\n-            position: 'absolute',\n-            paddingTop: safeTop - 8,\n-            top: 0,\n-            width: '100%',\n-            zIndex: 1000\n-          }}\n-        >\n-          <InfoProfile\n-            id={id}\n-            isRootPage={isRootPage}\n-            isMine={isMine}\n-            creditOpacity={creditOpacity}\n-            showAvatar={false}\n-            showFeedBack={() => setIsFeedbackShow(true)}\n-            onBack={handleBack}\n-            showBackButton={!fromSwipePreview}\n-          />\n-        </Animated.View>\n-\n-        <Animated.View\n-          style={[\n-            $tabStickyAnimateStyle,\n-            {\n-              position: 'absolute',\n-              paddingTop: safeTop - 8,\n-              top: 0,\n-              width: '100%',\n-              zIndex: 1000,\n-              backgroundColor: darkTheme.background.page\n-            }\n-          ]}\n-        >\n-          <InfoProfile\n-            id={id}\n-            isRootPage={isRootPage}\n-            isMine={isMine}\n-            creditOpacity={creditOpacity}\n-            showAvatar={true}\n-            currentUser={currentUser as UserProfile}\n-            showBackButton={!fromSwipePreview}\n-          />\n-\n-          <Tabs\n-            current={currentTab}\n-            items={pageTabConfig}\n-            tabBarStyle={{\n-              ...tabStyles.$tabStyle,\n-              backgroundColor: darkTheme.background.input\n-            }}\n-            animatedTabIndex={$animatedIndictor}\n-            tabWidth={60}\n-            itemStyle={tabStyles.$tabItemStyle}\n-            itemTextStyle={{\n-              ...tabStyles.$tabItemTextStyle,\n-              color: darkTheme.text.disabled\n-            }}\n-            onPressTab={(_, type) => {\n-              if (type === PageTab.ALBUM) {\n-                console.log('=======type=====', type);\n-                usePublishStore.getState().getAlbumPhotos(true);\n-                usePublishStore.getState().getHistoryPhotos(true);\n-                setShowAlbum(true);\n-                usePerformanceStore\n-                  .getState()\n-                  .recordStart('make_photo_photo_set_render', {\n-                    performance_type: 'render',\n-                    performance_key: AlbumFromType.USER\n-                  });\n-                return;\n-              }\n-              if (type === PageTab.SECRET) {\n-                reportClick('secret_button', { type: 'video' });\n-              } else {\n-                reportClick('button', {\n-                  user_button: type === PageTab.LIKE ? 9 : 8,\n-                  identity_status: isMine ? '0' : '1'\n-                });\n-              }\n-\n-              const tab = pageTabMap[type as PageTab];\n-              pagerRef.current?.setPage(tab || 0);\n-            }}\n-            renderIndicator={({ tabWidth, animatedStyle }) => (\n-              <Animated.View\n-                style={[\n-                  {\n-                    width: tabWidth,\n-                    position: 'absolute',\n-                    bottom: -2,\n-                    alignItems: 'center'\n-                  },\n-                  animatedStyle\n-                ]}\n-              >\n-                <View\n-                  style={{\n-                    width: 24,\n-                    height: 2,\n-                    borderRadius: 5,\n-                    backgroundColor: StyleSheet.currentColors.brand1\n-                  }}\n-                />\n-              </Animated.View>\n-            )}\n-          />\n-        </Animated.View>\n-\n-        <GestureDetector gesture={holdGesture}>\n-          <Animated.View style={[{ flex: 1 }, $goodsTransBgStyle]}>\n-            <View\n-              style={[\n-                {\n-                  width: '100%',\n-                  height: topHeight + (safeTop < 44 ? 44 : 0)\n-                }\n-              ]}\n-            >\n-              <Pressable\n-                style={{ position: 'relative', flex: 1 }}\n-                onPress={checkEnterGoods}\n-              >\n-                <View\n-                  style={[\n-                    {\n-                      flex: 1,\n-                      justifyContent: 'flex-end'\n-                    }\n-                  ]}\n-                >\n-                  <InfoSection\n-                    isRootPage={isRootPage}\n-                    currentUser={currentUser as UserProfile}\n-                    stat={stat}\n-                    isMine={isMine}\n-                    showFeedBack={() => setIsFeedbackShow(true)}\n-                    animateStyle={$goodsMaskStyle}\n-                    isAllowed={isSeqToastShow}\n-                    isSlideToTop={isStickyAbs}\n-                    goodsLikes={goodsLikes}\n-                    hasGoods={hasGoods}\n-                    checkEnterGoods={checkEnterGoods}\n-                  />\n-                </View>\n-              </Pressable>\n-\n-              <Animated.View\n-                onLayout={e => {\n-                  if (!tabPosY) {\n-                    setTabPosY(e.nativeEvent.layout.y);\n-                  }\n-                }}\n-              >\n-                <Tabs\n-                  current={currentTab}\n-                  items={pageTabConfig}\n-                  animatedTabIndex={$animatedIndictor}\n-                  tabBarStyle={{\n-                    ...tabStyles.$tabStyle,\n-                    backgroundColor: darkTheme.background.input\n-                  }}\n-                  tabWidth={60}\n-                  itemStyle={tabStyles.$tabItemStyle}\n-                  itemTextStyle={{\n-                    ...tabStyles.$tabItemTextStyle,\n-                    color: darkTheme.text.disabled\n-                  }}\n-                  onPressTab={(_, type) => {\n-                    if (type === PageTab.ALBUM) {\n-                      console.log('=======type=====', type);\n-                      usePublishStore.getState().getAlbumPhotos(true);\n-                      usePublishStore.getState().getHistoryPhotos(true);\n-                      setShowAlbum(true);\n-                      usePerformanceStore\n-                        .getState()\n-                        .recordStart('make_photo_photo_set_render', {\n-                          performance_type: 'render',\n-                          performance_key: AlbumFromType.USER\n-                        });\n-                      return;\n-                    }\n-                    if (type === PageTab.SECRET) {\n-                      reportClick('secret_button', { type: 'video' });\n-                    } else {\n-                      reportClick('button', {\n-                        user_button: type === PageTab.LIKE ? 9 : 8,\n-                        identity_status: isMine ? '0' : '1'\n-                      });\n-                    }\n-\n-                    const tab = pageTabMap[type as PageTab];\n-                    pagerRef.current?.setPage(tab || 0);\n-                  }}\n-                  renderIndicator={({ tabWidth, animatedStyle }) => (\n-                    <Animated.View\n-                      style={[\n-                        {\n-                          width: tabWidth,\n-                          position: 'absolute',\n-                          bottom: -2,\n-                          alignItems: 'center'\n-                        },\n-                        animatedStyle\n-                      ]}\n-                    >\n-                      <View\n-                        style={{\n-                          width: 24,\n-                          height: 2,\n-                          borderRadius: 5,\n-                          backgroundColor: StyleSheet.currentColors.brand1\n-                        }}\n-                      />\n-                    </Animated.View>\n-                  )}\n-                />\n-              </Animated.View>\n-            </View>\n-\n-            <View\n-              style={[\n-                {\n-                  width: '100%',\n-                  height: screenHeight - (returnTop ? 200 : 0),\n-                  position: 'relative'\n-                }\n-              ]}\n-            >\n-              {renderPageView()}\n-              {currentUser?.uid && (\n-                <View\n-                  style={{\n-                    position: 'absolute',\n-                    top: 0,\n-                    left: 0,\n-                    flex: 1\n-                  }}\n-                >\n-                  <FeedbackSheet\n-                    isVisible={isFeedbackShow}\n-                    userId={currentUser?.uid}\n-                    onClose={() => {\n-                      setIsFeedbackShow(false);\n-                    }}\n-                  />\n-                </View>\n-              )}\n-\n-              {profileGuideModalVisible && (\n-                <ProfileGuideModal\n-                  visible={profileGuideModalVisible}\n-                  onClose={() => setProfileGuideModalVisible(false)}\n-                />\n-              )}\n-            </View>\n-          </Animated.View>\n-        </GestureDetector>\n-\n-        <MaskArea />\n-        <AlbumSheet\n-          callWhere={AlbumFromType.USER}\n-          isVisible={showAlbum}\n-          onClose={() => {\n-            const tab = pageTabMap[PageTab.WORKS];\n-            pagerRef.current?.setPage(tab || 0);\n-            setShowAlbum(false);\n-          }}\n-        />\n-      </FullScreen>\n+      </Screen>\n     </PagePerformance>\n   );\n }\n@@ -1120,34 +852,3 @@\n export const UserScreen = memo(User, (prev, now) => {\n   return prev.timestamp === now.timestamp;\n });\n-\n-const tabStyles = StyleSheet.create({\n-  $tabStyle: {\n-    ...StyleSheet.rowStyle,\n-    borderColor: StyleSheet.hex(StyleSheet.currentColors.black, 0.08),\n-    height: 44,\n-    backgroundColor: 'white',\n-    borderTopLeftRadius: 16,\n-    borderTopRightRadius: 16,\n-    paddingHorizontal: 70\n-  },\n-  $tabItemStyle: {\n-    // flex: 1\n-  },\n-  $tabItemTextStyle: {\n-    fontSize: 14,\n-    lineHeight: 20\n-  },\n-\n-  $tabActiveStyle: {\n-    color: '#222222'\n-  },\n-  $tabActiveBorder: {\n-    ...StyleSheet.rowStyle,\n-    position: 'absolute',\n-    width: '100%',\n-    height: '100%',\n-    bottom: -13,\n-    justifyContent: 'center'\n-  }\n-});\n"}, {"new_path": "src/bizComponents/userScreen/types.ts", "old_path": "src/bizComponents/userScreen/types.ts", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -1,23 +1,12 @@\n-import { GestureType } from 'react-native-gesture-handler';\n-import { SharedValue } from 'react-native-reanimated';\n-\n-export enum PageTab {\n-  WORKS = 'works',\n-  SECRET = 'live_photo',\n-  LIKE = 'like',\n-  ALBUM = 'album'\n-}\n+import { UserPageTab } from './constants';\n \n export interface FlowCommonProps {\n   id: string;\n-  updateUnlockTop: (status: boolean) => void;\n   $safePaddingBottom: number;\n-  scrollY: SharedValue<number>;\n-  nativeGesture: GestureType;\n   queryRefresh?: string;\n   queryPageTab?: string;\n   queryTimestamp?: string;\n   isRefreshData?: [];\n   isRootPage?: boolean;\n-  currentTab: PageTab;\n+  currentTab: UserPageTab;\n }\n"}, {"new_path": "src/bizComponents/userScreen/useAnimation.ts", "old_path": "src/bizComponents/userScreen/useAnimation.ts", "deleted_file": true, "new_file": false, "renamed_file": false, "diff": "@@ -1,132 +0,0 @@\n-import { Platform } from 'react-native';\n-import { Gesture } from 'react-native-gesture-handler';\n-import {\n-  runOnJS,\n-  useAnimatedStyle,\n-  useSharedValue,\n-  withSpring,\n-  withTiming\n-} from 'react-native-reanimated';\n-import { useIsFocused } from '@react-navigation/native';\n-import { PageTab } from './types';\n-\n-export const useAnimation = ({\n-  currentTab,\n-  checkEnterGoods,\n-  maxScrollLimit,\n-  onRefreshData,\n-  setReturnTop\n-}) => {\n-  const $goodsShareY = useSharedValue(0);\n-  const refreshTypeS = useSharedValue(-1);\n-  const worksScrollY = useSharedValue(0);\n-  const livePhotoSY = useSharedValue(0);\n-  const likeScrollY = useSharedValue(0);\n-\n-  const scrollY =\n-    currentTab === PageTab.WORKS\n-      ? worksScrollY\n-      : currentTab === PageTab.SECRET\n-        ? livePhotoSY\n-        : likeScrollY;\n-\n-  const nativeGesture = Gesture.Native();\n-\n-  const refreshStyle = useAnimatedStyle(() => ({\n-    opacity: refreshTypeS.value === 0 ? 1 : 0,\n-    display: refreshTypeS.value === 0 ? 'flex' : 'none'\n-  }));\n-\n-  const enterGoodsStyle = useAnimatedStyle(() => ({\n-    opacity: refreshTypeS.value === 1 ? 1 : 0,\n-    display: refreshTypeS.value === 1 ? 'flex' : 'none'\n-  }));\n-\n-  const refreshWrapStyle = useAnimatedStyle(() => ({\n-    display: refreshTypeS.value === -1 ? 'none' : 'flex'\n-  }));\n-\n-  const iosMoveSv = useSharedValue(0);\n-  const isFocused = useIsFocused();\n-  const holdGesture = Gesture.Pan()\n-    .enabled(isFocused)\n-    .activeOffsetY(30)\n-    .onBegin(e => {\n-      iosMoveSv.value = e.absoluteY;\n-    })\n-    .maxPointers(2)\n-    .onTouchesMove(e => {\n-      //   ios 无法响应 onChange\n-      if (Platform.OS === 'ios') {\n-        if (scrollY.value > 0) {\n-          return;\n-        }\n-        const abY = e.allTouches[0].absoluteY;\n-        if (abY < iosMoveSv.value) {\n-          return;\n-        }\n-\n-        const curV = (abY - iosMoveSv.value) * 3;\n-\n-        $goodsShareY.value = withSpring(curV, {\n-          damping: 100,\n-          stiffness: 100 + Math.floor(Math.abs(curV) / maxScrollLimit)\n-        });\n-        let v = -1;\n-\n-        if (curV >= maxScrollLimit / 5) {\n-          v = 1;\n-        }\n-        refreshTypeS.value = withTiming(v, { duration: 100 });\n-      }\n-    })\n-    .onChange(e => {\n-      if (scrollY.value > 0) {\n-        runOnJS(setReturnTop)(false);\n-        return;\n-      }\n-\n-      $goodsShareY.value = withSpring($goodsShareY.value + e.changeY, {\n-        damping: 100,\n-        stiffness: 100 + Math.floor(Math.abs(e.translationY) / maxScrollLimit)\n-      });\n-      let v = -1;\n-\n-      if (e.translationY >= maxScrollLimit / 5) {\n-        v = 1;\n-      }\n-      refreshTypeS.value = v;\n-    })\n-    .onFinalize(() => {\n-      if (scrollY.value > 0) {\n-        refreshTypeS.value = -1;\n-        return;\n-      }\n-\n-      runOnJS(setReturnTop)(true);\n-      $goodsShareY.value = withSpring(0, {\n-        damping: 100,\n-        stiffness: 100\n-      });\n-      if (refreshTypeS.value === 0) {\n-        runOnJS(onRefreshData)();\n-      } else if (refreshTypeS.value === 1) {\n-        // 超过阈值进入谷子\n-        runOnJS(checkEnterGoods)();\n-      }\n-      refreshTypeS.value = -1;\n-    })\n-    .simultaneousWithExternalGesture(nativeGesture);\n-\n-  return {\n-    holdGesture,\n-    nativeGesture,\n-    $goodsShareY,\n-    refreshStyle,\n-    enterGoodsStyle,\n-    refreshWrapStyle,\n-    worksScrollY,\n-    livePhotoSY,\n-    likeScrollY\n-  };\n-};\n"}, {"new_path": "src/bizComponents/userScreen/utils.ts", "old_path": "src/bizComponents/userScreen/utils.ts", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -1,5 +1,12 @@\n+import { router } from 'expo-router';\n+import { feedClient } from '@/src/api';\n+import { getUserShowAsyncCardCnt } from '@/src/api/asynccard/index';\n import { showToast } from '@/src/components';\n import { RequestScene } from '@/src/components/infiniteList/typing';\n+import { dp2px } from '@/src/utils';\n+import { reportClick } from '@/src/utils/report';\n+import { catchErrorLog, errorReport } from '../../utils/error-log';\n+import { AsyncCardStatus } from '@/proto-registry/src/web/raccoon/common/asynccard_pb';\n \n /** feed流部分 */\n export const onRefreshError = (scene: RequestScene) => {\n@@ -7,3 +14,69 @@ export const onRefreshError = (scene: RequestScene) => {\n     ? showToast('刷新失败啦，请重试')\n     : undefined;\n };\n+\n+/**\n+ * 统一处理编辑资料的点击事件\n+ * @param customHandler 自定义处理函数，如果提供则优先使用\n+ */\n+export const handleEditProfile = (customHandler?: () => void) => {\n+  if (customHandler) {\n+    customHandler();\n+  } else {\n+    router.push('/profile/edit');\n+    setTimeout(() => {\n+      reportClick('edit_profile');\n+    });\n+  }\n+};\n+\n+/**\n+ * 计算用户面板的高度相关参数\n+ * @param isMine 是否是当前用户自己的面板\n+ * @param hasGoods 是否有商品\n+ * @returns 包含 topHeight 和 maskTop 的对象\n+ */\n+export const calculatePanelHeight = () => {\n+  // 所有可能的高度配置\n+  const heights = {\n+    visitorNoGoods: {\n+      topHeight: dp2px(267),\n+      maskTop: dp2px(13)\n+    },\n+    visitorWithGoods: {\n+      topHeight: dp2px(386),\n+      maskTop: dp2px(89)\n+    },\n+    mineNoGoods: {\n+      topHeight: dp2px(355),\n+      maskTop: dp2px(12)\n+    },\n+    mineWithGoods: {\n+      topHeight: dp2px(461),\n+      maskTop: dp2px(76)\n+    }\n+  };\n+\n+  return heights;\n+};\n+\n+/**\n+ * 获取用户待发布内容的数量\n+ * @returns 待发布内容的数量\n+ */\n+export const fetchSecretCount = async (): Promise<number> => {\n+  try {\n+    const res = await getUserShowAsyncCardCnt({});\n+    // 过滤出未发布的内容\n+    const secretsCount = Number(res.cnt);\n+\n+    return secretsCount;\n+  } catch (error) {\n+    console.log(\n+      '[LogPrefix][fetchSecretCount][utils] 获取待发布数量失败',\n+      error\n+    );\n+    catchErrorLog('fetchSecretCount_error', error);\n+    return 0;\n+  }\n+};\n"}, {"new_path": "src/bizComponents/videoMagic/button/generateButton.tsx", "old_path": "src/bizComponents/videoMagic/button/generateButton.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -24,7 +24,7 @@ import { reportClick } from '@/src/utils/report';\n import ToastInner from '../../credit/toast';\n import { TStoryBoardItem } from '../../magic-video/draggieStoryboard';\n import { WINDOW_WIDTH } from '../../nestedScrollView';\n-import { PageTab } from '../../userScreen/types';\n+import { UserPageTab } from '../../userScreen/constants';\n import { PlotType } from '@/proto-registry/src/web/raccoon/common/reimagine_pb';\n import { InvokeType } from '@/proto-registry/src/web/raccoon/common/types_pb';\n import { GameType } from '@/proto-registry/src/web/raccoon/common/types_pb';\n@@ -264,7 +264,7 @@ export default function GenerateButton() {\n \n             afterPublish({\n               tab: TabItemType.PROFILE,\n-              pageTab: PageTab.SECRET,\n+              pageTab: UserPageTab.SECRET,\n               refresh: true,\n               appendId: '200',\n               scene: Go2HomeScene.VIDEO_GENERATING\n"}, {"new_path": "src/components/avatar/index.tsx", "old_path": "src/components/avatar/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -65,6 +65,7 @@ export type AvatarProps = {\n   theme?: Theme;\n   borderWidth?: number;\n   borderColor?: ColorValue;\n+  outerBorder?: boolean; // 添加外边框选项\n   onLoad?: () => void;\n };\n \n@@ -76,7 +77,8 @@ export function Avatar(props: AvatarProps) {\n     theme = Theme.DARK,\n     showPendant = false,\n     borderWidth = 0,\n-    borderColor = 'rgba(255,255,255,0.06)'\n+    borderColor = 'rgba(255,255,255,0.06)',\n+    outerBorder = false\n   } = props;\n \n   const { loginIntercept } = useAuthState();\n@@ -92,8 +94,11 @@ export function Avatar(props: AvatarProps) {\n   const { teenModeGuard } = useTeenModeGuard();\n \n   const innerSize = useMemo(\n-    () => Math.max((props.size || 50) - borderWidth * 2, 0),\n-    [borderWidth, props.size]\n+    () =>\n+      outerBorder\n+        ? props.size || 50\n+        : Math.max((props.size || 50) - borderWidth * 2, 0),\n+    [borderWidth, props.size, outerBorder]\n   );\n \n   return (\n@@ -101,17 +106,28 @@ export function Avatar(props: AvatarProps) {\n       onStartShouldSetResponderCapture={() => false}\n       onPress={handlePress}\n       style={{\n-        width: props.size || 50,\n-        height: props.size || 50,\n+        width: outerBorder\n+          ? (props.size || 50) + borderWidth * 2\n+          : props.size || 50,\n+        height: outerBorder\n+          ? (props.size || 50) + borderWidth * 2\n+          : props.size || 50,\n         // overflow: 'hidden',\n         ...centerStyle,\n-        ...StyleSheet.circleStyle\n+        ...StyleSheet.circleStyle,\n+        ...(outerBorder && borderWidth > 0\n+          ? {\n+              borderWidth,\n+              borderColor,\n+              padding: borderWidth\n+            }\n+          : {})\n       }}\n     >\n       <Image\n         source={\n           formatTosUrl(props.profile?.avatar || props.source || '', {\n-            size: innerSize ? innerSize * 3 : 'size10'\n+            size: innerSize ? Math.ceil(innerSize * 3) : 'size10'\n           }) || (isMine ? DEFAULT_AVATAR_MINE : DEFAULT_AVATAR)\n         }\n         style={{\n@@ -119,8 +135,7 @@ export function Avatar(props: AvatarProps) {\n           height: innerSize,\n           borderRadius: 500,\n           zIndex: 0,\n-          borderWidth,\n-          borderColor\n+          ...(outerBorder ? {} : { borderWidth, borderColor })\n         }}\n         onLoad={props.onLoad}\n       />\n@@ -182,9 +197,9 @@ export function Avatar(props: AvatarProps) {\n \n   function handlePress(e: GestureResponderEvent) {\n     // clickEffect();\n-\n     if (!teenModeGuard()) return;\n     if (props.onPress) {\n+      console.log('### handlePress', props.onPress?.toString());\n       props.onPress(e);\n       return;\n     }\n@@ -265,6 +280,8 @@ function AvatarTag({\n   const size = useMemo(() => {\n     if (avatarSize >= 100) {\n       return 24;\n+    } else if (avatarSize >= 90) {\n+      return 20;\n     } else if (avatarSize >= 50) {\n       return 16;\n     } else {\n@@ -275,6 +292,8 @@ function AvatarTag({\n     switch (size) {\n       case 24:\n         return 6;\n+      case 20:\n+        return 4;\n       case 16:\n         return 0;\n       case 14:\n"}, {"new_path": "src/components/follow/index.tsx", "old_path": "src/components/follow/index.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -235,6 +235,7 @@ export const Follow = (props: IFollowProps) => {\n         onPressIn={onPressIn}\n         onPressOut={onPressOut}\n         onPress={onPress}\n+        hitSlop={10}\n       >\n         {customRender ? (\n           <View>{customRender(internalFollowed)}</View>\n"}, {"new_path": "src/components/icons/icon.tsx", "old_path": "src/components/icons/icon.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -569,7 +569,7 @@ export const iconRegistry = {\n   duntu_icon: require('@Assets/icon/gallery/duntu.png'),\n   warning: require('@Assets/icon/icon-warning.png'),\n   message: require('@Assets/icon/icon-message.png'),\n-  message_comment: require('@Assets/icon/message-comment.png'),\n+  message_comment: require('@Assets/icon/message-comment.png')\n };\n \n const $imageStyle: ImageStyle = {\n"}, {"new_path": "src/components/infiniteList/CustomNestedInnerScrollView.tsx", "old_path": "src/components/infiniteList/CustomNestedInnerScrollView.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -92,7 +92,7 @@ export const CustomNestedInnerScrollView = forwardRef(\n         isActive={isActive}\n         // @ts-ignore\n         ref={ref}\n-        // bounces={false}\n+        bounces={bounces}\n         {...props}\n         onScroll={onScroll1}\n         onRefresh={onRefresh}\n"}, {"new_path": "src/components/infiniteList/typing.ts", "old_path": "src/components/infiniteList/typing.ts", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "@@ -19,6 +19,13 @@ export enum RequestScene {\n   AFTER_PUBLISH = 'after_publish'\n }\n \n+export interface InfiniteScrollViewProps extends ScrollViewProps {\n+  contentStyle?: StyleProp<ViewStyle>;\n+  ref?: React.MutableRefObject<ScrollView | null>;\n+  hideRefresh?: boolean;\n+  lockScroll?: boolean;\n+}\n+\n export interface IInfiniteListProps<T> {\n   // loading 状态\n   loading?: boolean;\n@@ -38,12 +45,7 @@ export interface IInfiniteListProps<T> {\n   // 自定义\n   customListProps?: Partial<RecyclerListViewProps>;\n   // scrollView props 透传\n-  scrollViewProps?: ScrollViewProps & {\n-    contentStyle?: StyleProp<ViewStyle>;\n-    ref?: React.MutableRefObject<ScrollView | null>;\n-    hideRefresh?: boolean;\n-    lockScroll?: boolean;\n-  };\n+  scrollViewProps?: InfiniteScrollViewProps;\n   // 是否开启下拉刷新\n   enablePullRefresh?: boolean;\n   // 自定义 LayoutProvider\n"}, {"new_path": "src/components/skeletion/index.tsx", "old_path": "src/components/skeletion/index.tsx", "deleted_file": true, "new_file": false, "renamed_file": false, "diff": "@@ -47,7 +47,6 @@ export function SkeletonSpan({\n   width,\n   height,\n   radius,\n-  // FIXME(fuxiao): 需要根据主题来设置\n   theme = Theme.DARK,\n   style: $customStyle,\n   children\n@@ -75,7 +74,6 @@ export function SkeletonRow({\n   children,\n   gap = 10,\n   repeat,\n-  // FIXME(fuxiao): 需要根据主题来设置\n   theme = Theme.DARK,\n   style: $customStyle\n }: {\n@@ -112,7 +110,6 @@ export function SkeletonColumn({\n   gap = 10,\n   repeat,\n   style: $customStyle,\n-  // FIXME(fuxiao): 需要根据主题来设置\n   theme = Theme.DARK\n }: {\n   children?: React.ReactNode;\n"}, {"new_path": "src/components/tabs/dynamic-tabs.tsx", "old_path": "src/components/tabs/dynamic-tabs.tsx", "deleted_file": false, "new_file": true, "renamed_file": false, "diff": "@@ -0,0 +1,355 @@\n+import { useMemoizedFn } from 'ahooks';\n+import React, { ReactElement, useEffect } from 'react';\n+import { Pressable, TextStyle, View, ViewStyle } from 'react-native';\n+import Animated, {\n+  AnimatedStyleProp,\n+  SharedValue,\n+  useAnimatedStyle,\n+  useDerivedValue,\n+  useSharedValue,\n+  withTiming\n+} from 'react-native-reanimated';\n+import { Text } from '@/src/components';\n+import { $Z_INDEXES, $flexCenter, $flexRow } from '@/src/theme/variable';\n+import { StyleSheet } from '@/src/utils';\n+\n+export type Tab = {\n+  title: string;\n+  key?: string | number;\n+  width: number; // 必须提供宽度\n+  renderItem?: (props: {\n+    isActive: boolean;\n+    textStyle: TextStyle[];\n+  }) => ReactElement;\n+};\n+\n+export interface DynamicWidthTabsProps<T extends Tab> {\n+  items: T[]; //数据\n+  current: number | string; // 当前的激活态的key或index\n+  animatedTabIndex?: SharedValue<number>; // 与PagerView配合使用，获取滑动手势触发的TabIndicator变化\n+  onPressTab: (tabIndex: number, tabKey?: T['key']) => void; // 点击事件\n+  // 手动渲染Tab节点\n+  renderItem?: (props: {\n+    item: T;\n+    isActive: boolean;\n+    textStyle: TextStyle[];\n+  }) => ReactElement; // 全局renderItem\n+  animatedActive?: boolean;\n+  tabBarStyle?: ViewStyle; // 最外层的bar的样式\n+  itemStyle?: ViewStyle; //item的样式\n+  itemTextStyle?: TextStyle; // 文字样式\n+  tabGap?: number; // tab间的间隙\n+  indicatorBottomOffset?: number; // 指示器与文字的间距\n+  // 手动渲染indicator(必需把animatedStyle赋值给最外层的元素)\n+  renderIndicator?: (props: {\n+    animatedStyle: AnimatedStyleProp<ViewStyle>;\n+  }) => React.ReactElement | undefined;\n+  renderOthers?: () => ReactElement;\n+}\n+\n+// UI线程工作函数 - 计算位置和宽度\n+const calculatePositionsAndWidths = (\n+  widths: readonly number[],\n+  tabGap: number\n+) => {\n+  'worklet';\n+  const positions: number[] = [0];\n+\n+  for (let i = 1; i < widths.length; i++) {\n+    positions[i] = positions[i - 1] + widths[i - 1] + tabGap;\n+  }\n+\n+  return { positions, widths };\n+};\n+\n+// 插值计算 - 在UI线程运行\n+const interpolate = (current: number, start: number, end: number) => {\n+  'worklet';\n+  return start + (end - start) * current;\n+};\n+\n+export function DynamicWidthTabs<T extends Tab>({\n+  items,\n+  onPressTab,\n+  current = 0,\n+  animatedTabIndex,\n+  tabBarStyle = {},\n+  itemTextStyle = {},\n+  itemStyle,\n+  tabGap = 24,\n+  animatedActive,\n+  renderIndicator,\n+  renderOthers,\n+  indicatorBottomOffset = -4,\n+  renderItem\n+}: DynamicWidthTabsProps<T>) {\n+  const validIndex =\n+    typeof current === 'number'\n+      ? current\n+      : items.findIndex(item => item.key === current);\n+\n+  // 保存items到共享值，以便在worklet中访问\n+  const $itemWidths = useSharedValue([...items.map(item => item.width)]);\n+  const $tabGap = useSharedValue(tabGap);\n+  const $validIndex = useSharedValue(validIndex);\n+\n+  // 当props变化时更新共享值\n+  useEffect(() => {\n+    $tabGap.value = tabGap;\n+    $validIndex.value = validIndex;\n+    $itemWidths.value = [...items.map(item => item.width)];\n+  }, [items, tabGap, validIndex]);\n+\n+  // 在UI线程计算位置和宽度 - 避免JS到UI线程的数据传输\n+  const $cachedData = useDerivedValue(() => {\n+    return calculatePositionsAndWidths($itemWidths.value, $tabGap.value);\n+  }, [$itemWidths, $tabGap]);\n+\n+  // 目标位置和宽度 - 在UI线程计算\n+  const $targetPosition = useDerivedValue(() => {\n+    const { positions } = $cachedData.value;\n+    return positions[$validIndex.value] || 0;\n+  }, [$cachedData, $validIndex]);\n+\n+  const $targetWidth = useDerivedValue(() => {\n+    const { widths } = $cachedData.value;\n+    return widths[$validIndex.value];\n+  }, [$cachedData, $validIndex]);\n+\n+  // 动画到目标位置和宽度\n+  const $animatedPosition = useDerivedValue(() => {\n+    if (!animatedTabIndex) {\n+      return withTiming($targetPosition.value, { duration: 300 });\n+    }\n+\n+    const { positions } = $cachedData.value;\n+    const idx = Math.floor(animatedTabIndex.value);\n+    const fraction = animatedTabIndex.value % 1;\n+\n+    // 处理边界情况\n+    if (fraction === 0 || idx < 0 || idx >= positions.length - 1) {\n+      return idx >= 0 && idx < positions.length\n+        ? positions[idx]\n+        : $targetPosition.value;\n+    }\n+\n+    // UI线程插值计算，更高效\n+    const startPos = positions[idx];\n+    const endPos = positions[idx + 1];\n+    return interpolate(fraction, startPos, endPos);\n+  });\n+\n+  const $animatedWidth = useDerivedValue(() => {\n+    if (!animatedTabIndex) {\n+      return withTiming($targetWidth.value, { duration: 300 });\n+    }\n+\n+    const { widths } = $cachedData.value;\n+    const idx = Math.floor(animatedTabIndex.value);\n+    const fraction = animatedTabIndex.value % 1;\n+\n+    // 处理边界情况\n+    if (fraction === 0 || idx < 0 || idx >= widths.length - 1) {\n+      return idx >= 0 && idx < widths.length ? widths[idx] : $targetWidth.value;\n+    }\n+\n+    // UI线程插值计算，更高效\n+    const startWidth = widths[idx];\n+    const endWidth = widths[idx + 1];\n+    return interpolate(fraction, startWidth, endWidth);\n+  });\n+\n+  // 动画样式 - 只需要位置和宽度变化，不需要scale效果\n+  const $indicatorAnimateStyle = useAnimatedStyle(() => {\n+    return {\n+      transform: [{ translateX: $animatedPosition.value }],\n+      width: $animatedWidth.value\n+    };\n+  });\n+\n+  const $activeIndex = useSharedValue(-1);\n+\n+  useEffect(() => {\n+    if ($activeIndex.value !== validIndex) $activeIndex.value = validIndex;\n+  }, [validIndex]);\n+\n+  // 渲染Tab内容\n+  const renderTabContent = useMemoizedFn(\n+    (tab: T, idx: number, $textStyle: TextStyle) => {\n+      const inactiveStyle = animatedActive ? {} : $inactiveTabText;\n+\n+      const textStyle: TextStyle[] = [\n+        $tabText,\n+        { width: tab.width },\n+        validIndex === idx ? $activeTabText : inactiveStyle,\n+        itemTextStyle || {}\n+      ];\n+\n+      const isActive = validIndex === idx;\n+\n+      // 从原始items中获取renderItem，而不是从共享值中获取\n+      const originalTab = items[idx];\n+\n+      // 优先使用单个tab的renderItem，其次使用全局renderItem\n+      if (originalTab.renderItem) {\n+        return originalTab.renderItem({\n+          isActive,\n+          textStyle: [...textStyle, $textStyle]\n+        });\n+      } else if (renderItem) {\n+        return renderItem({\n+          item: tab,\n+          isActive,\n+          textStyle: [...textStyle, $textStyle]\n+        });\n+      }\n+\n+      return (\n+        <Animated.Text numberOfLines={1} style={[...textStyle, $textStyle]}>\n+          {tab.title}\n+        </Animated.Text>\n+      );\n+    }\n+  );\n+\n+  return (\n+    <View style={[$tabBar, tabBarStyle]}>\n+      <View style={{ position: 'relative' }}>\n+        {renderIndicator ? (\n+          renderIndicator({\n+            animatedStyle: $indicatorAnimateStyle\n+          })\n+        ) : (\n+          <Animated.View\n+            style={[\n+              $indicator,\n+              $indicatorAnimateStyle,\n+              { bottom: indicatorBottomOffset }\n+            ]}\n+          >\n+            <View style={$indicatorPointer} />\n+          </Animated.View>\n+        )}\n+        <View style={{ flexDirection: 'row', gap: tabGap }}>\n+          {items.map((tab, idx) => (\n+            <Pressable\n+              key={tab.title}\n+              onPress={() => {\n+                $activeIndex.value = idx;\n+                onPressTab(idx, tab?.key);\n+              }}\n+            >\n+              <TabItem\n+                itemStyle={itemStyle}\n+                tab={tab}\n+                idx={idx}\n+                $activeIndex={$activeIndex}\n+                animatedActive={animatedActive}\n+                renderContent={renderTabContent}\n+              />\n+            </Pressable>\n+          ))}\n+        </View>\n+        {renderOthers?.()}\n+      </View>\n+    </View>\n+  );\n+}\n+\n+interface TabsItemProps<T extends Tab> {\n+  tab: T;\n+  idx: number;\n+  $activeIndex: SharedValue<number>;\n+  itemStyle?: ViewStyle;\n+  animatedActive?: boolean;\n+  renderContent: (tab: T, idx: number, textStyle: TextStyle) => ReactElement;\n+}\n+\n+function TabItem<T extends Tab>({\n+  tab,\n+  idx,\n+  $activeIndex,\n+  itemStyle: itemStyleOverride = {},\n+  animatedActive = false,\n+  renderContent\n+}: TabsItemProps<T>) {\n+  // 在UI线程计算透明度动画\n+  const $animationStyle = useAnimatedStyle(() => {\n+    'worklet';\n+    const isActive = $activeIndex.value === idx;\n+    return {\n+      opacity: withTiming(isActive ? 1 : 0.4, { duration: 200 })\n+    };\n+  });\n+\n+  // 在UI线程计算字体样式，同时添加透明度过渡\n+  const $animationTextStyle = useAnimatedStyle(() => {\n+    'worklet';\n+    const isActive = $activeIndex.value === idx;\n+    return {\n+      fontWeight: isActive ? '600' : '500',\n+      color: withTiming(\n+        isActive\n+          ? StyleSheet.darkColors.white[1000]\n+          : StyleSheet.darkColors.white[400],\n+        { duration: 200 }\n+      ),\n+      opacity: withTiming(isActive ? 1 : 0.8, { duration: 200 })\n+    };\n+  });\n+\n+  return (\n+    <Animated.View\n+      style={[\n+        $tab,\n+        itemStyleOverride,\n+        animatedActive ? $animationStyle : {},\n+        { width: tab.width }\n+      ]}\n+    >\n+      {renderContent(tab, idx, $animationTextStyle)}\n+    </Animated.View>\n+  );\n+}\n+\n+const $tabBar: ViewStyle = {\n+  ...$flexRow,\n+  ...$flexCenter,\n+  paddingHorizontal: 8,\n+  flexGrow: 0,\n+  height: 44\n+};\n+\n+const $tab: ViewStyle = {\n+  ...$flexRow,\n+  ...$flexCenter\n+};\n+\n+const $tabText: TextStyle = {\n+  fontSize: 16,\n+  color: StyleSheet.darkColors.white[1000],\n+  textAlign: 'center'\n+};\n+\n+const $activeTabText: TextStyle = {\n+  color: StyleSheet.darkColors.white[1000],\n+  fontWeight: '600'\n+};\n+\n+const $inactiveTabText: TextStyle = {\n+  color: StyleSheet.darkColors.white[400],\n+  fontWeight: '500'\n+};\n+\n+const $indicator: ViewStyle = {\n+  position: 'absolute',\n+  alignItems: 'center',\n+  zIndex: $Z_INDEXES.zm1\n+};\n+\n+const $indicatorPointer: ViewStyle = {\n+  width: 12,\n+  height: 5,\n+  backgroundColor: StyleSheet.colors.themeGround,\n+  borderRadius: 4\n+};\n"}, {"new_path": "src/components/tabs/index.tsx", "old_path": "src/components/tabs/index.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "--- \n+++ \n@@ -42,6 +42,7 @@\n   itemStyle?: ViewStyle; //item的样式\n   itemTextStyle?: TextStyle; // 文字样式\n   tabGap?: number; // tab间的间隙\n+  indicatorBottomOffset?: number; // 指示器与文字的间距\n   // 手动渲染indicator(必需把animatedStyle和tabWidth赋值给最外层的元素)\n   renderIndicator?: (props: {\n     animatedStyle: AnimatedStyleProp<ViewStyle>;\n@@ -63,7 +64,8 @@\n   tabGap = 24,\n   animatedActive,\n   renderIndicator,\n-  renderOthers\n+  renderOthers,\n+  indicatorBottomOffset = -4\n }: TabsProps<T>) {\n   const validIndex =\n     typeof current === 'number'\n@@ -127,7 +129,11 @@\n           })\n         ) : (\n           <Animated.View\n-            style={[$indicator, $indicatorAnimateStyle, { width: tabWidth }]}\n+            style={[\n+              $indicator,\n+              $indicatorAnimateStyle,\n+              { width: tabWidth, bottom: indicatorBottomOffset }\n+            ]}\n           >\n             <View style={$indicatorPointer} />\n           </Animated.View>\n@@ -278,7 +284,6 @@\n \n const $indicator: ViewStyle = {\n   position: 'absolute',\n-  bottom: -4,\n   alignItems: 'center',\n   zIndex: $Z_INDEXES.zm1\n };\n"}, {"new_path": "src/components/tabs/simple-tabs.tsx", "old_path": "src/components/tabs/simple-tabs.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "--- \n+++ \n@@ -42,6 +42,7 @@\n   itemStyle?: ViewStyle; //item的样式\n   itemTextStyle?: TextStyle; // 文字样式\n   tabGap?: number; // tab间的间隙\n+  indicatorBottomOffset?: number; // 指示器与文字的间距\n   // 手动渲染indicator(必需把animatedStyle和tabWidth赋值给最外层的元素)\n   renderIndicator?: (props: {\n     tabWidth?: number;\n@@ -63,7 +64,8 @@\n   tabGap = 24,\n   animatedActive,\n   renderIndicator,\n-  renderOthers\n+  renderOthers,\n+  indicatorBottomOffset = -4\n }: TabsProps<T>) {\n   const validIndex =\n     typeof current === 'number'\n@@ -115,7 +117,13 @@\n         visibleStyle\n       })\n     ) : (\n-      <Animated.View style={[$indicator, { width: tabWidth }, visibleStyle]}>\n+      <Animated.View\n+        style={[\n+          $indicator,\n+          { width: tabWidth, bottom: indicatorBottomOffset },\n+          visibleStyle\n+        ]}\n+      >\n         <View style={$indicatorPointer} />\n       </Animated.View>\n     );\n@@ -143,6 +151,7 @@\n                 animatedActive={animatedActive}\n                 renderContent={renderContent}\n                 renderIndicator={_renderIndicator}\n+                indicatorBottomOffset={indicatorBottomOffset}\n               />\n             </Pressable>\n           ))}\n@@ -162,6 +171,7 @@\n   animatedActive?: boolean;\n   renderContent: (tab: T, idx: number, textStyle: TextStyle) => ReactElement;\n   renderIndicator?: (visibleStyle: ViewStyle) => React.ReactElement | undefined;\n+  indicatorBottomOffset?: number;\n }\n \n function TabItem<T extends Tab>({\n"}, {"new_path": "src/components/waterfall/WaterFall2.tsx", "old_path": "src/components/waterfall/WaterFall2.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "--- \n+++ \n@@ -165,6 +165,10 @@\n             };\n           }\n \n+          if (!data || data.length === 0) {\n+            return {};\n+          }\n+\n           if (!data?.[indexNum]?.card) {\n             errorReport(\n               `CardExpoReportMissingId_${pagePathRef.current}`,\n@@ -489,7 +493,8 @@\n             {\n               width: '100%',\n               height: '100%',\n-              paddingBottom: 5\n+              paddingBottom: 5,\n+              paddingTop: 5\n             },\n             isLeft\n               ? {\n"}, {"new_path": "src/components/waterfall/useWaterfallGesture.tsx", "old_path": "src/components/waterfall/useWaterfallGesture.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "--- \n+++ \n@@ -9,6 +9,9 @@\n import { usePersistFn } from '@/src/hooks';\n import { InfiniteListRef } from '../infiniteList/typing';\n \n+/**\n+ * @deprecated 已废弃\n+ */\n export const useWaterfallGesture = (params: {\n   threshold?: number;\n   updateUnlockTop?: (status: boolean) => void;\n"}, {"new_path": "src/store/asyncMessage.tsx", "old_path": "src/store/asyncMessage.tsx", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "--- \n+++ \n@@ -4,7 +4,7 @@\n import { router } from 'expo-router';\n import { create } from 'zustand';\n import { Socket } from '@/src/api/websocket';\n-import { PageTab } from '@/src/bizComponents/userScreen/types';\n+import { UserPageTab } from '@/src/bizComponents/userScreen/constants';\n import { showMessage } from '@/src/components/v2/systemMessage';\n import { TabItemType } from '@/src/types';\n import { reportClick } from '@/src/utils/report';\n@@ -97,7 +97,7 @@\n                 const { go2HomePage } = onNavigate;\n                 go2HomePage({\n                   tab: TabItemType.PROFILE,\n-                  pageTab: PageTab.SECRET,\n+                  pageTab: UserPageTab.SECRET,\n                   refresh: true\n                 });\n               }\n"}, {"new_path": "src/store/font.ts", "old_path": "src/store/font.ts", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "--- \n+++ \n@@ -23,6 +23,8 @@\n   | 'HanRoundedCNHeavy'\n   | 'BarlowBlack'\n   | 'BarlowBold'\n+  | 'BarlowSemiBold'\n+  | 'RanyMedium'\n   | 'DouyinSansBold'\n   | 'MuyaoSoftbrush';\n \n@@ -85,6 +87,14 @@\n     url: 'https://resource.lipuhome.com/resource/file/prod/20250306/3e5e203ff456c318de37f41ff3317d83.ttf',\n     md5: '3e5e203ff456c318de37f41ff3317d83'\n   },\n+  BarlowSemiBold: {\n+    url: 'https://resource.lipuhome.com/resource/file/prod/20250322/29527ab52af2334e2bcb6290c8692f70.ttf',\n+    md5: '29527ab52af2334e2bcb6290c8692f70'\n+  },\n+  RanyMedium: {\n+    url: 'https://resource.lipuhome.com/resource/file/prod/20250322/8a96a720c5d6678c04b61c1e56c2054d.otf',\n+    md5: '8a96a720c5d6678c04b61c1e56c2054d'\n+  },\n   DouyinSansBold: {\n     url: 'https://resource.lipuhome.com/resource/file/prod/20250306/6068c4698b30572ac0d208464cc8d03a.ttf',\n     md5: '6068c4698b30572ac0d208464cc8d03a'\n"}, {"new_path": "src/store/personalCenter.ts", "old_path": "src/store/personalCenter.ts", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "--- \n+++ \n@@ -1,13 +1 @@\n-import { create } from 'zustand';\n-import { PageTab } from '../bizComponents/userScreen/types';\n-import { immer } from 'zustand/middleware/immer';\n \n-interface PersonalCenterStore {\n-  // 个人中心\n-}\n-\n-export const usePersonalCenterStore = create<PersonalCenterStore>()(\n-  immer((set, get) => ({\n-    //\n-  }))\n-);\n"}, {"new_path": "src/store/userInfo.ts", "old_path": "src/store/userInfo.ts", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "--- \n+++ \n@@ -21,25 +21,25 @@\n const MIN_FETCH_DURATION = 2000;\n \n //\n-export type UserInfo = {\n+export interface UserInfo {\n   uid: string;\n   profile: UserProfile;\n   stat: UserSocialStat;\n   goodsWallRes: PartialMessage<GetPlaceRsp>;\n-};\n+}\n \n-type States = {\n+interface States {\n   __users: UserInfo[]; // 不要直接依赖 __users!!，用getUserInfo获取\n-};\n+}\n \n-type Actions = {\n+interface Actions {\n   // 同步UserInfo，支持传入预加载的profile\n   syncUserInfo: (uid: string, preloadProfile?: UserProfile) => void;\n   // 获取本地的\n   getUserInfo: (uid: string) => UserInfo | undefined;\n   // update stat\n   updateStat: (uid: string, stat: Partial<UserSocialStat>) => void;\n-};\n+}\n \n export const useUserInfoStore = create<States & Actions>()((set, get) => {\n   // 将uid 推到最前面\n"}, {"new_path": "src/theme/tokens/colors/base/dark.ts", "old_path": "src/theme/tokens/colors/base/dark.ts", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "--- \n+++ \n@@ -10,6 +10,9 @@\n     54: 'rgba(54, 54, 61)',\n     62: 'rgb(62, 62, 66)',\n     244: 'rgba(244, 244, 244, 0.1)'\n+  },\n+  black: {\n+    600: 'rgba(0, 0, 0, 0.6)'\n   },\n   white: {\n     40: 'rgba(255, 255, 255, 0.04)',\n"}, {"new_path": "src/theme/tokens/colors/variants/dark.ts", "old_path": "src/theme/tokens/colors/variants/dark.ts", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "--- \n+++ \n@@ -20,7 +20,8 @@\n     sheetModal: darkColors.gray[42],\n     tag: darkColors.white[60],\n     bottomBar: darkColors.gray[29],\n-    bottomBarBlur: darkColors.gray.alpha29\n+    bottomBarBlur: darkColors.gray.alpha29,\n+    transTip: darkColors.black[600]\n   },\n   text: {\n     solid: darkColors.white[1000],\n@@ -37,7 +38,8 @@\n   },\n   border: {\n     alpha: darkColors.white[80],\n-    button: darkColors.white[100]\n+    button: darkColors.white[100],\n+    avatar: darkColors.white[200]\n   },\n   tag: {\n     bannerTopicBg: darkColors.white[60],\n"}, {"new_path": "src/theme/typography.ts", "old_path": "src/theme/typography.ts", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "--- \n+++ \n@@ -51,7 +51,11 @@\n   HanRoundedCNHeavy: 'HanRoundedCNHeavy',\n   <PERSON>: {\n     BlackItalic: 'BarlowBlack',\n-    BoldItalic: 'BarlowBold'\n+    BoldItalic: 'BarlowBold',\n+    SemiBold: 'BarlowSemiBold'\n+  },\n+  Rany: {\n+    Medium: 'RanyMedium'\n   },\n   DouyinSans: {\n     bold: 'DouyinSansBold'\n"}, {"new_path": "src/utils/event.ts", "old_path": "src/utils/event.ts", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "--- \n+++ \n@@ -53,7 +53,8 @@\n   | 'forceRefreshRecommend'\n   | 'handleUserScreenBack'\n   | 'resetUserScreenTimer'\n-  | 'videoTabSetPager';\n+  | 'videoTabSetPager'\n+  | 'goodsMaskLoaded';\n \n const CommonEventBus = new Event<TCOMMON_EVENT_KEYS>();\n \n"}, {"new_path": "eslint.config.mjs", "old_path": "eslint.config.mjs", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "--- \n+++ \n@@ -66,6 +66,12 @@\n \n       '@typescript-eslint/no-var-requires': 'off',\n       '@typescript-eslint/require-await': 'warn',\n+      '@typescript-eslint/no-require-imports': [\n+        'warn',\n+        {\n+          allow: ['^@Assets/.*']\n+        }\n+      ],\n \n       'no-restricted-imports': [\n         'error',\n@@ -103,8 +109,8 @@\n         }\n       ],\n       'eslint-comments/no-unused-disable': 'warn',\n-      '@typescript-eslint/no-require-imports': 'warn',\n-      'react/jsx-no-bind': 'warn',\n+      'react/jsx-uses-react': 'error',\n+      'react/jsx-no-bind': ['warn', { allowArrowFunctions: true }],\n       'react/jsx-no-constructed-context-values': 'warn',\n       'react/jsx-handler-names': 'warn',\n       'react/self-closing-comp': [\n@@ -113,7 +119,8 @@\n           component: true,\n           html: true\n         }\n-      ]\n+      ],\n+      '@typescript-eslint/consistent-type-definitions': 'off'\n     }\n   }\n );\n"}, {"new_path": "proto-gen.lock.json", "old_path": "proto-gen.lock.json", "deleted_file": false, "new_file": false, "renamed_file": false, "diff": "--- \n+++ \n@@ -1,13 +1,13 @@\n {\n   \"repos\": {\n     \"passport\": {\n-      \"commit\": \"42ac43100e6158410076d42cc35a261deffc3978\"\n+      \"commit\": \"b97f943b0582b78d880db52358eda895776a233b\"\n     },\n     \"step-bridge\": {\n       \"commit\": \"23d74262f05e9ecc75a94905bdae5381c998d9f3\"\n     },\n     \"raccoon\": {\n-      \"commit\": \"42ac43100e6158410076d42cc35a261deffc3978\"\n+      \"commit\": \"b97f943b0582b78d880db52358eda895776a233b\"\n     },\n     \"trpc-infra\": {\n       \"commit\": \"77fc3539f5d5f285f5a89ba2f20bfebef60c2561\"\n"}]