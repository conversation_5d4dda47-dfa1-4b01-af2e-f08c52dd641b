# CLAUDE.md

本文件为 Claude Code (claude.ai/code) 在此仓库中工作时提供指导。

## 开发命令

### 环境设置

- **使用 uv 设置**: `uv venv --python 3.12` (推荐的现代 Python 包管理器)
- **激活环境**: `source .venv/bin/activate`
- **安装依赖**: `uv pip install -r requirements.txt`
- **安装开发依赖**: `uv pip install -r requirements-dev.txt`
- **以开发模式安装**: `uv pip install -e .`

### 环境变量配置

PR-Agent 现在支持通过环境变量配置输出目录：

- **`PR_AGENT_OUTPUT_DIR`**: 指定所有输出文件的目录路径
  - 如果设置，会覆盖配置文件中的 `debug_output_dir`、`export_diff_dir` 和 `export_mr_description_dir` 设置
  - 用于 CI/CD 环境中动态指定输出路径
  - 示例: `export PR_AGENT_OUTPUT_DIR="/path/to/output"`

### 配置优先级

PR-Agent 使用以下配置优先级（从高到低）：
1. 环境变量（如 `PR_AGENT_OUTPUT_DIR`）
2. 仓库特定设置 (`.pr_agent.toml`)
3. 全局用户设置
4. 默认配置文件 (`pr_agent/settings/configuration.toml`)

PR-Agent 是一个 AI 驱动的工具，用于分析和改进跨多个 Git 提供商（GitHub、GitLab、BitBucket、Azure DevOps、Gitea）的拉取请求。

### 核心组件

1. **Agent 层** (`pr_agent/agent/`):
   - `PRAgent` 类：处理工具执行的主要协调器
   - 命令路由到相应的工具类

2. **工具** (`pr_agent/tools/`):
   - `PRReviewer`: 分析 PR 并提供评审反馈
   - `PRDescription`: 生成/更新 PR 标题和描述
   - `PRCodeSuggestions`: 提供代码改进建议
   - `PRQuestions`: 处理关于 PR 的问答
   - `PR_LineQuestions`: 针对特定行的问题
   - `PRUpdateChangelog`: 基于 PR 更新更新日志
   - `PRAddDocs`: 生成文档
   - `PRGenerateLabels`: 创建自定义标签
   - `PRHelpDocs`: 提供上下文帮助

3. **Git 提供商** (`pr_agent/git_providers/`):
   - 不同 Git 托管服务的抽象层
   - 跨平台 PR 操作的统一接口

4. **AI 处理器** (`pr_agent/algo/ai_handlers/`):
   - `LiteLLMAIHandler`: 主要 AI 集成（支持多种模型）
   - `BaseAiHandler`: AI 实现的抽象基类
   - 支持 OpenAI、Claude、Gemini 和其他模型

5. **配置系统** (`pr_agent/settings/`):
   - `configuration.toml`: 主配置文件
   - 工具特定的提示配置
   - 语言扩展和忽略模式

### 关键模式

- **工具架构**: 每个工具都实现了带有异步执行的一致接口
- **提供商抽象**: Git 操作通过提供商类进行抽象
- **提示工程**: 基于 TOML 的不同工具提示模板
- **Token 管理**: 自适应补丁匹配和 Token 感知处理
- **多模型支持**: 可配置的 AI 模型和回退选项

### 配置

系统使用分层配置方法：
1. `pr_agent/settings/configuration.toml` 中的默认设置
2. 仓库特定设置 (`.pr_agent.toml`)
3. 全局用户设置
4. 基于 Wiki 的设置（专业版功能）

关键配置区域：
- 模型选择和参数
- 工具特定行为
- Git 提供商设置
- Token 限制和补丁处理
- 响应语言和格式

### CLI 使用

注意，CLI 使用前需要使用 `source .venv/bin/activate` 激活环境。

主入口点：直接运行 `pr-agent`
常用命令：
- `review`: 分析和评审 PR
- `describe`: 生成 PR 标题/描述
- `improve`: 建议代码改进
- `ask`: 询问关于 PR 的问题
- `update_changelog`: 更新更新日志

示例：`pr-agent --pr_url=https://github.com/owner/repo/pull/123 describe`

### 测试策略

- 单个组件的单元测试
- 完整工作流的端到端测试
- 系统验证的健康测试
- Git 集成的提供商特定测试

## MR 报告生成器

项目包含一个基于 TypeScript + React 的现代化 MR 报告生成器，位于 `mr-report-generator/` 目录。

**详细开发指南**: 参见 `mr-report-generator/CLAUDE.md` 获取完整的开发、测试和调试指南。

### 快速使用

**完整的 MR 分析工作流:**

1. **生成带有 Diff 导出的 MR 描述:**
```bash
pr-agent --pr_url=https://gitlab.basemind.com/raccoon/lipu-mobile/-/merge_requests/1921 describe
```

2. **构建交互式 HTML 报告:**
```bash
cd mr-report-generator
MARKDOWN_FILE=./fixtures/mr_description_summary_1921.md \
DIFF_FILE=./fixtures/diff_1921.json \
bun run build
```

3. **查看报告:**
```bash
open mr-report-generator/dist/index.html
```
