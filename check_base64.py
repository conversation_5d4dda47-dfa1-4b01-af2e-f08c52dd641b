#!/usr/bin/env python3
"""
检测文件中是否包含 base64 内容的脚本
"""

import re
import sys
import os

def is_likely_base64(text):
    """更严格的 base64 验证"""
    import base64
    import string
    
    # 基本长度检查
    if len(text) < 50:  # 提高最小长度阈值
        return False
    
    # 检查字符组成
    valid_chars = string.ascii_letters + string.digits + '+/='
    if not all(c in valid_chars for c in text):
        return False
    
    # 检查 base64 结构
    if len(text) % 4 != 0:
        return False
    
    # 检查 padding
    padding_count = text.count('=')
    if padding_count > 2 or (padding_count > 0 and not text.endswith('=' * padding_count)):
        return False
    
    # 检查是否有太多重复字符（避免误报）
    if len(set(text)) < 10:  # 如果字符种类太少，可能不是真正的 base64
        return False
    
    # 尝试解码验证
    try:
        decoded = base64.b64decode(text, validate=True)
        # 检查解码后的内容是否合理
        if len(decoded) < 20:
            return False
        return True
    except:
        return False

def detect_base64_patterns(text):
    """检测文本中的 base64 模式"""
    base64_patterns = []
    
    # 模式1: data URLs (最可靠)
    data_url_pattern = re.compile(r'data:[^;,]+;base64,([A-Za-z0-9+/=]{50,})', re.IGNORECASE)
    for match in data_url_pattern.finditer(text):
        base64_part = match.group(1)
        if is_likely_base64(base64_part):
            start = match.start()
            end = match.end()
            patterns_found = {
                'type': 'data_url',
                'start': start,
                'end': end,
                'length': len(base64_part),
                'preview': base64_part[:50] + '...' if len(base64_part) > 50 else base64_part
            }
            base64_patterns.append(patterns_found)
    
    # 模式2: 长的连续 base64 字符序列（更严格的验证）
    # 只检测非常长的序列以避免误报
    continuous_pattern = re.compile(r'[A-Za-z0-9+/]{100,}={0,2}')
    for match in continuous_pattern.finditer(text):
        candidate = match.group(0)
        start = match.start()
        end = match.end()
        
        # 检查是否已经被 data_url 模式捕获
        already_captured = any(
            pattern['start'] <= start <= pattern['end'] 
            for pattern in base64_patterns if pattern['type'] == 'data_url'
        )
        
        if not already_captured and is_likely_base64(candidate):
            patterns_found = {
                'type': 'continuous',
                'start': start,
                'end': end,
                'length': len(candidate),
                'preview': candidate[:50] + '...' if len(candidate) > 50 else candidate
            }
            base64_patterns.append(patterns_found)
    
    return base64_patterns

def check_file_for_base64(filepath):
    """检查文件中是否包含 base64 内容"""
    if not os.path.exists(filepath):
        print(f"❌ 文件不存在: {filepath}")
        return False
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")
        return False
    
    patterns = detect_base64_patterns(content)
    
    if patterns:
        print(f"🔍 在文件 {filepath} 中发现 {len(patterns)} 个可能的 base64 序列:")
        for i, pattern in enumerate(patterns, 1):
            print(f"  {i}. 类型: {pattern['type']}")
            print(f"     位置: {pattern['start']}-{pattern['end']}")
            print(f"     长度: {pattern['length']} 字符")
            print(f"     预览: {pattern['preview']}")
            print()
        return True
    else:
        print(f"✅ 文件 {filepath} 中未发现 base64 内容")
        return False

def main():
    # 默认检查 prompts 文件
    default_file = "mr-report-generator/fixtures/prompts_pr_description_prompt_1921.md"
    
    if len(sys.argv) > 1:
        filepath = sys.argv[1]
    else:
        filepath = default_file
    
    print(f"🔍 检查文件: {filepath}")
    print("=" * 60)
    
    has_base64 = check_file_for_base64(filepath)
    
    print("=" * 60)
    if has_base64:
        print("❌ 发现 base64 内容，需要进一步处理")
        sys.exit(1)
    else:
        print("✅ 没有发现 base64 内容")
        sys.exit(0)

if __name__ == "__main__":
    main()